// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 60;
	objects = {

/* Begin PBXBuildFile section */
		3DACD5F92E33697700BDE918 /* ComposableArchitecture in Frameworks */ = {isa = PBXBuildFile; productRef = 3DACD5F82E33697700BDE918 /* ComposableArchitecture */; };
		3DD5D87A2E37940500747741 /* StoreKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 3DD5D8792E37940500747741 /* StoreKit.framework */; };
		CA0A5766242AA3A800BA537D /* TcaTemplateApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = CA0A5765242AA3A800BA537D /* TcaTemplateApp.swift */; };
		CA0A576A242AA3A900BA537D /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = CA0A5769242AA3A900BA537D /* Assets.xcassets */; };
		CA0A579F242AA7F800BA537D /* RootView.swift in Sources */ = {isa = PBXBuildFile; fileRef = CA0A579E242AA7F800BA537D /* RootView.swift */; };
		DCB9EA0126B20D4B00497C03 /* AppSwiftUI in Frameworks */ = {isa = PBXBuildFile; productRef = DCB9EA0026B20D4B00497C03 /* AppSwiftUI */; };
		DCB9EA0526B20E4C00497C03 /* AuthenticationClientLive in Frameworks */ = {isa = PBXBuildFile; productRef = DCB9EA0426B20E4C00497C03 /* AuthenticationClientLive */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		3D92C72A2E35D42600B377E6 /* tca-template.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = "tca-template.entitlements"; path = "tca-template/tca-template.entitlements"; sourceTree = "<group>"; };
		3DD5D8792E37940500747741 /* StoreKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = StoreKit.framework; path = System/Library/Frameworks/StoreKit.framework; sourceTree = SDKROOT; };
		CA0A5761242AA3A800BA537D /* tca-template.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "tca-template.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		CA0A5765242AA3A800BA537D /* TcaTemplateApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TcaTemplateApp.swift; sourceTree = "<group>"; };
		CA0A5769242AA3A900BA537D /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		CA0A579E242AA7F800BA537D /* RootView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RootView.swift; sourceTree = "<group>"; };
		DCB9E9FD26B207CD00497C03 /* tca-template */ = {isa = PBXFileReference; lastKnownFileType = folder; path = "tca-template"; sourceTree = "<group>"; };
		DCB9EA3D26B2188200497C03 /* README.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = README.md; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		CA0A575E242AA3A800BA537D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DCB9EA0126B20D4B00497C03 /* AppSwiftUI in Frameworks */,
				DCB9EA0526B20E4C00497C03 /* AuthenticationClientLive in Frameworks */,
				3DACD5F92E33697700BDE918 /* ComposableArchitecture in Frameworks */,
				3DD5D87A2E37940500747741 /* StoreKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		3DD5D8782E37940500747741 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				3DD5D8792E37940500747741 /* StoreKit.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		CA6AC25C2450FDB800C71CB3 /* App */ = {
			isa = PBXGroup;
			children = (
				CA0A579E242AA7F800BA537D /* RootView.swift */,
				CA0A5765242AA3A800BA537D /* TcaTemplateApp.swift */,
				CA0A5769242AA3A900BA537D /* Assets.xcassets */,
			);
			path = App;
			sourceTree = "<group>";
		};
		DC9193ED2420104100A5BE1F = {
			isa = PBXGroup;
			children = (
				3D92C72A2E35D42600B377E6 /* tca-template.entitlements */,
				DCB9E9FD26B207CD00497C03 /* tca-template */,
				DCB9EA3D26B2188200497C03 /* README.md */,
				CA6AC25C2450FDB800C71CB3 /* App */,
				3DD5D8782E37940500747741 /* Frameworks */,
				DC9193F72420104100A5BE1F /* Products */,
			);
			sourceTree = "<group>";
		};
		DC9193F72420104100A5BE1F /* Products */ = {
			isa = PBXGroup;
			children = (
				CA0A5761242AA3A800BA537D /* tca-template.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		CA0A5760242AA3A800BA537D /* tca-template */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = CA0A5781242AA3A900BA537D /* Build configuration list for PBXNativeTarget "tca-template" */;
			buildPhases = (
				CA0A575D242AA3A800BA537D /* Sources */,
				CA0A575E242AA3A800BA537D /* Frameworks */,
				CA0A575F242AA3A800BA537D /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "tca-template";
			packageProductDependencies = (
				DCB9EA0026B20D4B00497C03 /* AppSwiftUI */,
				DCB9EA0426B20E4C00497C03 /* AuthenticationClientLive */,
				3DACD5F82E33697700BDE918 /* ComposableArchitecture */,
			);
			productName = "tca-template";
			productReference = CA0A5761242AA3A800BA537D /* tca-template.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		DC9193EE2420104100A5BE1F /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastSwiftUpdateCheck = 1140;
				LastUpgradeCheck = 1600;
				ORGANIZATIONNAME = "Point-Free";
				TargetAttributes = {
					CA0A5760242AA3A800BA537D = {
						CreatedOnToolsVersion = 11.4;
					};
				};
			};
			buildConfigurationList = DC9193F12420104100A5BE1F /* Build configuration list for PBXProject "tca-template" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = DC9193ED2420104100A5BE1F;
			packageReferences = (
				DCB9EA0726B20F1200497C03 /* XCLocalSwiftPackageReference "tca-template" */,
				3DACD5F72E33697700BDE918 /* XCRemoteSwiftPackageReference "swift-composable-architecture" */,
			);
			productRefGroup = DC9193F72420104100A5BE1F /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				CA0A5760242AA3A800BA537D /* tca-template */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		CA0A575F242AA3A800BA537D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CA0A576A242AA3A900BA537D /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		CA0A575D242AA3A800BA537D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CA0A5766242AA3A800BA537D /* TcaTemplateApp.swift in Sources */,
				CA0A579F242AA7F800BA537D /* RootView.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		CA0A577D242AA3A900BA537D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_ENTITLEMENTS = "tca-template/tca-template.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 2;
				DEVELOPMENT_TEAM = F7P5G9SB49;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_CFBundleDisplayName = "蝴蝶婚纱";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 16.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.wenhaofree.bridal-swift";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		CA0A577E242AA3A900BA537D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_ENTITLEMENTS = "tca-template/tca-template.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 2;
				DEVELOPMENT_TEAM = F7P5G9SB49;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_CFBundleDisplayName = "蝴蝶婚纱";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 16.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.wenhaofree.bridal-swift";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		DC9194132420104400A5BE1F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_STRICT_CONCURRENCY = complete;
				SWIFT_VERSION = 6.0;
			};
			name = Debug;
		};
		DC9194142420104400A5BE1F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_STRICT_CONCURRENCY = complete;
				SWIFT_VERSION = 6.0;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		CA0A5781242AA3A900BA537D /* Build configuration list for PBXNativeTarget "tca-template" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				CA0A577D242AA3A900BA537D /* Debug */,
				CA0A577E242AA3A900BA537D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		DC9193F12420104100A5BE1F /* Build configuration list for PBXProject "tca-template" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DC9194132420104400A5BE1F /* Debug */,
				DC9194142420104400A5BE1F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCLocalSwiftPackageReference section */
		DCB9EA0726B20F1200497C03 /* XCLocalSwiftPackageReference "tca-template" */ = {
			isa = XCLocalSwiftPackageReference;
			relativePath = "tca-template";
		};
/* End XCLocalSwiftPackageReference section */

/* Begin XCRemoteSwiftPackageReference section */
		3DACD5F72E33697700BDE918 /* XCRemoteSwiftPackageReference "swift-composable-architecture" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/pointfreeco/swift-composable-architecture";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.20.2;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		3DACD5F82E33697700BDE918 /* ComposableArchitecture */ = {
			isa = XCSwiftPackageProductDependency;
			package = 3DACD5F72E33697700BDE918 /* XCRemoteSwiftPackageReference "swift-composable-architecture" */;
			productName = ComposableArchitecture;
		};
		DCB9EA0026B20D4B00497C03 /* AppSwiftUI */ = {
			isa = XCSwiftPackageProductDependency;
			package = DCB9EA0726B20F1200497C03 /* XCLocalSwiftPackageReference "tca-template" */;
			productName = AppSwiftUI;
		};
		DCB9EA0426B20E4C00497C03 /* AuthenticationClientLive */ = {
			isa = XCSwiftPackageProductDependency;
			package = DCB9EA0726B20F1200497C03 /* XCLocalSwiftPackageReference "tca-template" */;
			productName = AuthenticationClientLive;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = DC9193EE2420104100A5BE1F /* Project object */;
}
