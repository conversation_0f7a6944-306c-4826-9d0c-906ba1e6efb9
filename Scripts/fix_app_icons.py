#!/usr/bin/env python3
"""
修复 App Icon 透明度问题
移除 alpha 通道并添加白色背景
"""

import os
from PIL import Image

def fix_app_icon(input_path, output_path):
    """
    修复单个 App Icon 文件
    - 移除 alpha 通道
    - 添加白色背景
    - 转换为 RGB 格式
    """
    try:
        # 打开原始图像
        with Image.open(input_path) as img:
            # 如果图像有 alpha 通道，创建白色背景
            if img.mode in ('RGBA', 'LA') or (img.mode == 'P' and 'transparency' in img.info):
                # 创建白色背景
                background = Image.new('RGB', img.size, (255, 255, 255))
                
                # 如果是 P 模式，先转换为 RGBA
                if img.mode == 'P':
                    img = img.convert('RGBA')
                
                # 将原图像粘贴到白色背景上
                if img.mode == 'RGBA':
                    background.paste(img, mask=img.split()[-1])  # 使用 alpha 通道作为 mask
                else:
                    background.paste(img)
                
                # 保存为 RGB 格式
                background.save(output_path, 'PNG', optimize=True)
                print(f"✅ 修复完成: {input_path} -> {output_path}")
                
            else:
                # 如果没有 alpha 通道，直接转换为 RGB 并保存
                rgb_img = img.convert('RGB')
                rgb_img.save(output_path, 'PNG', optimize=True)
                print(f"✅ 转换完成: {input_path} -> {output_path}")
                
    except Exception as e:
        print(f"❌ 处理失败 {input_path}: {e}")

def main():
    """主函数：批量修复所有 App Icon"""
    
    # App Icon 目录
    icon_dir = "App/Assets.xcassets/AppIcon.appiconset"
    
    # 需要修复的图标文件
    icon_files = [
        '120.png',
        '152.png', 
        '167.png',
        '180.png',
        'transparent.png'
    ]
    
    print("🔧 开始修复 App Icon 透明度问题...")
    
    # 切换到图标目录
    os.chdir(icon_dir)
    
    # 创建备份目录
    backup_dir = 'backup_original'
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
        print(f"📁 创建备份目录: {backup_dir}")
    
    # 处理每个图标文件
    for filename in icon_files:
        if os.path.exists(filename):
            # 备份原文件
            backup_path = os.path.join(backup_dir, filename)
            if not os.path.exists(backup_path):
                import shutil
                shutil.copy2(filename, backup_path)
                print(f"💾 备份原文件: {filename} -> {backup_path}")
            
            # 修复图标
            temp_path = f"fixed_{filename}"
            fix_app_icon(filename, temp_path)
            
            # 替换原文件
            if os.path.exists(temp_path):
                os.replace(temp_path, filename)
                print(f"🔄 替换原文件: {filename}")
        else:
            print(f"⚠️  文件不存在: {filename}")
    
    print("\n🎉 App Icon 修复完成！")
    print("📋 修复内容:")
    print("   - 移除了所有 alpha 通道")
    print("   - 添加了白色背景")
    print("   - 转换为标准 RGB 格式")
    print("   - 原文件已备份到 backup_original/ 目录")
    
    # 验证修复结果
    print("\n🔍 验证修复结果:")
    for filename in icon_files:
        if os.path.exists(filename):
            try:
                with Image.open(filename) as img:
                    print(f"   {filename}: {img.mode} 模式, 尺寸 {img.size}")
            except Exception as e:
                print(f"   {filename}: 验证失败 - {e}")

if __name__ == "__main__":
    main()
