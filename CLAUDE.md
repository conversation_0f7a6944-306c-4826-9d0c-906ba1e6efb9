# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a **Bridal AI iOS application** built with **Swift Composable Architecture (TCA)** and **SwiftUI**. The app generates AI-powered bridal images with photo upload, style selection, subscription management, and user authentication.

**Key characteristics:**
- Modular TCA architecture with separate Core/SwiftUI modules
- Swift Package Manager for dependency management
- iOS 16+ minimum deployment target
- Swift 6.0 language mode

## Development Commands

### Building and Testing
```bash
# Build the project
cd tca-template && swift build

# Run all tests
./Scripts/run-tests.sh

# Run specific test module
./Scripts/run-tests.sh --filter LoginCore

# Run tests with coverage and verbose output
./Scripts/run-tests.sh --coverage --verbose

# Clean build and run tests
./Scripts/run-tests.sh --clean --parallel

# Development environment setup
./Scripts/setup-dev.sh
```

### Xcode Development
- Open `tca-template.xcodeproj` in Xcode
- Main scheme: `tca-template`
- Swift Package Manager handles dependencies

### Feature Generation
```bash
# Generate new feature module with boilerplate
./Scripts/generate-feature.sh FeatureName
```
This creates FeatureCore/, FeatureSwiftUI/, and FeatureCoreTests/ modules with:
- TCA reducer with ObservableState, Actions, and error handling
- SwiftUI view with navigation, loading states, and error handling
- Test module with TestStore examples and Swift Testing framework

## Architecture Overview

### Module Structure
The codebase follows a **3-layer modular architecture**:

1. **Core Modules** (`*Core`): Business logic, state management, TCA reducers
   - Location: `tca-template/Sources/*Core/`
   - Dependencies: TCA, client interfaces
   - Example: `LoginCore`, `SubscriptionCore`, `PhotoUploadCore`

2. **SwiftUI Modules** (`*SwiftUI`): UI components and views  
   - Location: `tca-template/Sources/*SwiftUI/`
   - Dependencies: Corresponding Core module, CommonUI
   - Example: `LoginSwiftUI`, `SubscriptionSwiftUI`

3. **Client Modules** (`*Client` + `*ClientLive`): External service abstractions
   - Interfaces: `*Client` (protocols)
   - Implementations: `*ClientLive` (concrete implementations)
   - Example: `AuthenticationClient` + `AuthenticationClientLive`

### Key Features
- **Authentication**: Login, two-factor auth, Apple Sign-In, keychain persistence
- **Subscription Management**: StoreKit integration, subscription tiers, upgrade flows
- **AI Image Generation**: Photo upload, style selection, AI generation workflow
- **User State**: Persistent user data with UserStateCore/UserStorageClient

### Data Flow Pattern
```
User Action → Store → Reducer → State Change → View Update
                ↓
           Side Effects (Network, Storage)
```

## Common Patterns

### Adding New Features
1. Use `./Scripts/generate-feature.sh NewFeature` for boilerplate
2. Implement state and actions in `NewFeatureCore`
3. Build UI components in `NewFeatureSwiftUI`
4. Add to navigation in `MainTabCore`/`AppCore`
5. Update `Package.swift` dependencies
6. Write tests in `NewFeatureCoreTests`

### TCA Testing Pattern
```swift
@Test
@MainActor
func testFeature() async {
  let store = TestStore(initialState: Feature.State()) {
    Feature()
  } withDependencies: {
    $0.networkClient.request = { _ in mockData }
  }
  
  await store.send(.actionTapped) {
    $0.isLoading = true
  }
  
  await store.receive(.dataLoaded(mockData)) {
    $0.isLoading = false
    $0.data = mockData
  }
}
```

### Dependency Injection
```swift
@Dependency(\.authenticationClient) var authenticationClient
```

## Project-Specific Implementation Notes

### Authentication Flow
- Uses keychain for secure token storage (`KeychainClient`)
- Supports email/password and Apple Sign-In
- Two-factor authentication with `TwoFactorCore`
- User persistence via `UserStateCore` and `UserStorageClient`

### Subscription System
- StoreKit 2 integration via `StoreKitClientLive`
- Subscription tiers: Free, Premium, Pro
- Upgrade/downgrade flows in `SubscriptionCore`
- Purchase validation and receipt handling

### AI Image Workflow
1. `PhotoUploadCore` - Handle image selection/upload
2. `ImageTypeSelectionCore` - Choose image generation type
3. `StyleSelectionCore` - Select artistic style
4. `ImageGenerationCore` - Process AI generation
5. `ImageViewCore` - Display and manage results

### State Management Best Practices
- Use `@ObservableState` for automatic SwiftUI updates
- Keep state flat, avoid deep nesting
- Use computed properties for derived values
- Normalize collections with ID-based dictionaries

## Testing Strategy

- **Unit Tests**: Test reducers and business logic with TestStore
- **Integration Tests**: Test feature interactions and data flow
- **Mock Dependencies**: Use TestDependencyKey for controlled testing
- **Coverage**: Aim for comprehensive state change and effect testing

## Development Workflow

1. **Planning**: Define state, actions, and dependencies
2. **Core Implementation**: Build reducer and business logic
3. **UI Development**: Create SwiftUI views and components
4. **Testing**: Write comprehensive tests for all scenarios
5. **Integration**: Add to app navigation and Package.swift
6. **Validation**: Run tests and verify build success

## Important Notes

- This is a **bridal/wedding AI app** - context matters for feature development
- **Swift 6.0 strict concurrency** is enabled with comprehensive language features
- **Minimum iOS 16.0** deployment target with modern SwiftUI APIs
- All network requests should go through `NetworkClient` abstraction
- User data persistence requires `UserStorageClient` and keychain integration
- StoreKit purchases must be validated and persisted properly
- Uses Swift Testing framework (@Test) instead of XCTest for new test modules

## File Locations Reference

- Main app entry: `App/TcaTemplateApp.swift`
- Swift Package: `tca-template/Package.swift`
- Feature modules: `tca-template/Sources/*/`
- Tests: `tca-template/Tests/*/`
- Scripts: `Scripts/`
- Documentation: `docs/`