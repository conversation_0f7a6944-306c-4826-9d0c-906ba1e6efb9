# 升级订阅功能完整实现

## 🎯 功能概述

用户点击升级按钮触发订阅支付逻辑，订阅支付成功后自动更新订阅状态并隐藏升级提示页面。

## 🔧 实现细节

### 1. **升级按钮触发逻辑修复**

**问题**: 升级按钮调用的是 `.showSubscriptionPage` 而不是直接的支付逻辑

**修复**: 
- 账户状态卡片中的"升级"按钮: `.upgradeSubscription`
- Pro 升级卡片中的"立即升级"按钮: `.upgradeSubscription`

**代码变更**:
```swift
// 之前
Button("升级") {
  store.send(.showSubscriptionPage)
}

// 修复后
Button("升级") {
  store.send(.upgradeSubscription)
}
```

### 2. **真实 StoreKit 支付集成**

**文件**: `Sources/ProfileCore/ProfileCore.swift`

**新增依赖**:
```swift
@Dependency(\.storeKitClient) var storeKitClient
```

**支付流程实现**:
```swift
case .upgradeSubscription:
  state.isLoading = true
  return .run { send in
    do {
      // 1. 加载可用订阅产品
      let products = try await storeKitClient.loadProducts()
      
      // 2. 选择年度订阅（优先）或月度订阅
      guard let selectedProduct = products.first(where: { $0.duration == .yearly }) ?? products.first else {
        await send(.subscriptionPurchaseFailed("没有可用的订阅产品"))
        return
      }
      
      // 3. 发起购买
      let newStatus = try await storeKitClient.purchase(selectedProduct)
      await send(.subscriptionPurchaseCompleted(newStatus))
      
    } catch {
      await send(.subscriptionPurchaseFailed("支付失败: \(error.localizedDescription)"))
    }
  }
```

### 3. **加载状态 UI 反馈**

**账户状态卡片升级按钮**:
```swift
Button(action: {
  store.send(.upgradeSubscription)
}) {
  HStack(spacing: 4) {
    if store.isLoading {
      ProgressView()
        .scaleEffect(0.8)
        .progressViewStyle(CircularProgressViewStyle(tint: .white))
    }
    Text(store.isLoading ? "处理中..." : "升级")
  }
}
.background(store.isLoading ? Color.gray : Color.blue)
.disabled(store.isLoading)
```

**Pro 升级卡片按钮**:
```swift
HStack {
  if store.isLoading {
    ProgressView()
      .progressViewStyle(CircularProgressViewStyle(tint: .white))
  } else {
    Image(systemName: "crown.fill")
  }
  
  Text(store.isLoading ? "正在升级..." : "立即升级")
  
  if !store.isLoading {
    Image(systemName: "arrow.right")
  }
}
.background(
  LinearGradient(
    colors: store.isLoading ? [Color.gray, Color.gray.opacity(0.8)] : [Color.purple, Color.pink]
  )
)
.disabled(store.isLoading)
```

### 4. **订阅成功后状态更新**

**自动状态同步**:
```swift
case .subscriptionPurchaseCompleted(let newStatus):
  state.isLoading = false
  
  if let currentUser = state.user {
    let updatedUser = User(
      id: currentUser.id,
      email: currentUser.email,
      displayName: currentUser.displayName,
      avatarURL: currentUser.avatarURL,
      createdAt: currentUser.createdAt,
      subscriptionStatus: newStatus
    )
    state.user = updatedUser
    state.isShowingSubscriptionSuccessAlert = true
  }
```

### 5. **升级提示页面自动隐藏**

**条件显示逻辑**:
```swift
// Pro订阅推广卡片 (如果是免费用户)
if store.user?.subscriptionStatus == .free {
  proUpgradeCard
}
```

**工作原理**:
- 订阅成功后，`user.subscriptionStatus` 从 `.free` 变为 `.premium`
- SwiftUI 响应式更新自动隐藏 Pro 升级卡片
- 账户状态卡片中的"升级"按钮也会被"刷新"按钮替换

### 6. **错误处理和用户反馈**

**支付失败提示**:
```swift
.alert(
  "支付失败",
  isPresented: Binding(
    get: { store.error != nil },
    set: { _ in store.send(.clearError) }
  )
) {
  Button("确定") {
    store.send(.clearError)
  }
  Button("重试") {
    store.send(.upgradeSubscription)
  }
} message: {
  Text(store.error?.localizedDescription ?? "支付过程中发生错误，请重试。")
}
```

**订阅成功提示**:
```swift
.alert(
  "订阅成功！",
  isPresented: Binding(
    get: { store.isShowingSubscriptionSuccessAlert },
    set: { _ in store.send(.dismissSubscriptionSuccessAlert) }
  )
) {
  Button("太棒了！") {
    store.send(.dismissSubscriptionSuccessAlert)
  }
} message: {
  Text("🎉 恭喜您成功升级到Pro版本！\n现在可以享受所有高级功能了。")
}
```

## 🔄 完整用户流程

### 用户操作流程:
1. **用户点击升级按钮** → 触发 `.upgradeSubscription`
2. **显示加载状态** → 按钮变灰，显示进度指示器
3. **加载订阅产品** → 从 App Store 获取可用产品
4. **发起支付** → 调用 StoreKit 2 购买 API
5. **支付成功** → 更新用户订阅状态
6. **UI 自动更新** → 隐藏升级卡片，显示成功提示
7. **状态持久化** → 保存订阅状态到本地存储

### 技术流程:
```
用户点击升级
    ↓
ProfileCore.upgradeSubscription
    ↓
StoreKitClient.loadProducts()
    ↓
StoreKitClient.purchase(selectedProduct)
    ↓
ProfileCore.subscriptionPurchaseCompleted
    ↓
更新 state.user.subscriptionStatus
    ↓
ProfileSwiftUI 自动响应状态变化
    ↓
隐藏升级卡片 + 显示成功提示
```

## 🎨 视觉反馈改进

### 加载状态:
- **进度指示器**: 白色圆形进度条
- **文本变化**: "升级" → "处理中..." / "立即升级" → "正在升级..."
- **颜色变化**: 蓝色/渐变 → 灰色
- **按钮禁用**: 防止重复点击

### 成功状态:
- **卡片隐藏**: Pro 升级卡片自动消失
- **状态更新**: 显示"高级版 Pro (到期: 日期)"
- **图标变化**: 灰色圆圈 → 紫色皇冠
- **成功提示**: 庆祝弹窗确认升级成功

### 错误状态:
- **错误弹窗**: 显示具体错误信息
- **重试选项**: 提供"重试"按钮
- **状态恢复**: 按钮恢复到初始状态

## 🧪 测试验证

### 测试场景:
1. **正常支付流程**: 点击升级 → 支付成功 → 状态更新
2. **支付失败处理**: 模拟支付失败 → 显示错误 → 重试
3. **加载状态显示**: 验证按钮加载状态和禁用
4. **UI 响应性**: 确认升级卡片正确隐藏
5. **状态持久性**: 应用重启后状态保持

### 预期结果:
- ✅ 升级按钮正确触发支付流程
- ✅ 支付过程中显示加载状态
- ✅ 支付成功后立即更新订阅状态
- ✅ Pro 升级卡片自动隐藏
- ✅ 显示订阅成功庆祝提示
- ✅ 支付失败时显示错误和重试选项

## 🎯 核心改进

1. **直接支付**: 升级按钮直接触发支付，无需跳转页面
2. **真实集成**: 使用真正的 StoreKit 2 API 而非模拟
3. **即时反馈**: 加载状态、成功提示、错误处理
4. **自动隐藏**: 订阅成功后升级提示自动消失
5. **状态同步**: 所有相关 UI 元素自动更新

现在用户可以直接在设置页面完成订阅升级，整个流程流畅且用户友好！🚀
