# 虚拟机中测试支付订阅功能配置指南

## 🖥️ 虚拟机环境准备

### 1. 虚拟机软件选择
- **推荐**: VMware Fusion Pro 或 Parallels Desktop
- **系统**: macOS Monterey 12.0+ (支持 StoreKit 2)
- **配置**: 8GB+ RAM, 50GB+ 存储
- **Xcode**: 14.0+ 版本

### 2. 虚拟机优化设置
```
VMware Fusion 设置:
- 处理器: 4+ 核心
- 内存: 8GB+
- 显卡: 启用 3D 加速
- 网络: NAT 模式
- 共享文件夹: 启用

Parallels Desktop 设置:
- 性能: 平衡模式
- 硬件加速: 启用
- Coherence 模式: 可选
```

## 🍎 Apple Developer 账号配置

### 1. 开发者账号准备
```
必需账号类型:
- Apple Developer Program ($99/年)
- 或 Apple Developer Enterprise Program

账号权限:
- App Manager 或 Admin 角色
- 能够创建 App ID 和证书
```

### 2. App Store Connect 配置

#### 创建应用:
```
1. 登录 App Store Connect
2. 我的 App → 新建 App
3. 平台: iOS
4. 名称: Bridal AI
5. Bundle ID: com.yourcompany.bridal-app
6. SKU: bridal-app-001
7. 用户访问权限: 完全访问权限
```

#### 配置订阅产品:
```
App 内购买项目 → 创建新项目:

月度订阅:
- 类型: 自动续期订阅
- 产品 ID: com.bridal.monthly
- 参考名称: Pro Monthly Subscription
- 订阅群组: Pro Subscription
- 订阅时长: 1个月
- 价格: 第1级 (¥6) 或自定义 ¥18

年度订阅:
- 类型: 自动续期订阅
- 产品 ID: com.bridal.yearly
- 参考名称: Pro Yearly Subscription  
- 订阅群组: Pro Subscription
- 订阅时长: 1年
- 价格: 第2级 (¥30) 或自定义 ¥128
```

### 3. 沙盒测试用户
```
用户和访问 → 沙盒测试员 → 添加测试员:

测试账户 1:
- 邮箱: <EMAIL>
- 密码: TestPass123!
- 名字: Test User 1
- 姓氏: Bridal
- 地区: 中国大陆

测试账户 2:
- 邮箱: <EMAIL>  
- 密码: TestPass123!
- 名字: Test User 2
- 姓氏: Bridal
- 地区: 美国
```

## 🔧 Xcode 项目配置

### 1. 签名和功能
```
Target → Signing & Capabilities:

Team: [您的开发团队]
Bundle Identifier: com.yourcompany.bridal-app
Provisioning Profile: Automatic

添加功能:
+ In-App Purchase
+ StoreKit Configuration (可选，用于本地测试)
```

### 2. StoreKit 配置文件 (本地测试)

创建 `BridalApp.storekit`:
```json
{
  "identifier" : "BridalApp",
  "nonRenewingSubscriptions" : [],
  "products" : [],
  "settings" : {
    "_applicationInternalID" : "*********",
    "_developerTeamID" : "YOUR_TEAM_ID",
    "_failTransactionsEnabled" : false,
    "_locale" : "zh_CN",
    "_storefront" : "CHN"
  },
  "subscriptionGroups" : [
    {
      "id" : "21234567",
      "localizations" : [
        {
          "description" : "Pro订阅服务",
          "displayName" : "Pro Subscription",
          "locale" : "zh_CN"
        }
      ],
      "name" : "Pro Subscription",
      "subscriptions" : [
        {
          "adHocOffers" : [],
          "codeOffers" : [],
          "displayPrice" : "18.00",
          "familyShareable" : false,
          "id" : "com.bridal.monthly",
          "introductoryOffer" : null,
          "localizations" : [
            {
              "description" : "解锁所有高级风格和功能",
              "displayName" : "Pro 月度订阅",
              "locale" : "zh_CN"
            }
          ],
          "productID" : "com.bridal.monthly",
          "recurringSubscriptionPeriod" : "P1M",
          "referenceName" : "Monthly Pro",
          "subscriptionGroupID" : "21234567",
          "type" : "RecurringSubscription"
        },
        {
          "adHocOffers" : [],
          "codeOffers" : [],
          "displayPrice" : "128.00",
          "familyShareable" : false,
          "id" : "com.bridal.yearly",
          "introductoryOffer" : null,
          "localizations" : [
            {
              "description" : "解锁所有高级风格和功能，享受最大优惠",
              "displayName" : "Pro 年度订阅",
              "locale" : "zh_CN"
            }
          ],
          "productID" : "com.bridal.yearly",
          "recurringSubscriptionPeriod" : "P1Y",
          "referenceName" : "Yearly Pro",
          "subscriptionGroupID" : "21234567",
          "type" : "RecurringSubscription"
        }
      ]
    }
  ],
  "version" : {
    "major" : 3,
    "minor" : 0
  }
}
```

### 3. 代码配置更新

#### 更新产品 ID:
```swift
// Sources/StoreKitClientLive/StoreKitClientLive.swift
private static let productIDs: Set<String> = [
  "com.bridal.monthly",    // 确保与 App Store Connect 一致
  "com.bridal.yearly"
]
```

#### 环境配置:
```swift
// Sources/BridalApp/BridalApp.swift
withDependencies: {
  $0.authenticationClient = .liveValue
  $0.userStorageClient = .liveValue
  
  #if DEBUG
  // 虚拟机测试环境
  if ProcessInfo.processInfo.environment["XCODE_RUNNING_FOR_PREVIEWS"] == "1" {
    $0.storeKitClient = .testValue  // 预览模式
  } else {
    $0.storeKitClient = $0.liveStoreKitClient  // 真实 StoreKit
  }
  #else
  $0.storeKitClient = $0.liveStoreKitClient  // 生产环境
  #endif
}
```

## 📱 虚拟机测试流程

### 1. 设备准备
```
iOS 模拟器设置:
- 设备: iPhone 14 Pro
- iOS 版本: 16.0+
- 地区: 中国大陆
- 语言: 简体中文

真实设备 (推荐):
- iPhone/iPad 连接到虚拟机
- 开发者模式启用
- 信任开发证书
```

### 2. 沙盒环境配置
```
设备设置 → App Store → 沙盒账户:
1. 退出当前 Apple ID
2. 不要登录沙盒账户 (应用内会提示)
3. 确保网络连接正常
```

### 3. 测试步骤

#### 本地 StoreKit 测试:
```
1. 在 Xcode 中选择 StoreKit 配置文件
2. Product → Scheme → Edit Scheme
3. Run → Options → StoreKit Configuration
4. 选择 BridalApp.storekit
5. 运行应用测试本地支付流程
```

#### 沙盒环境测试:
```
1. 使用真实设备或模拟器
2. 确保未选择 StoreKit 配置文件
3. 运行应用
4. 选择 Pro 模板触发订阅
5. 使用沙盒测试账户登录
6. 完成模拟支付流程
```

## 🐛 常见问题解决

### 1. 虚拟机性能问题
```
解决方案:
- 增加虚拟机内存到 12GB+
- 启用硬件加速
- 关闭不必要的后台应用
- 使用 SSD 存储
```

### 2. 网络连接问题
```
解决方案:
- 检查虚拟机网络设置
- 使用 NAT 模式而非桥接
- 确保防火墙不阻止连接
- 重启网络适配器
```

### 3. 证书和签名问题
```
解决方案:
- 确保开发证书有效
- 检查 Bundle ID 匹配
- 重新生成描述文件
- 清理 Xcode 缓存
```

### 4. StoreKit 错误
```
常见错误:
- "Cannot connect to iTunes Store"
  → 检查网络和沙盒账户
  
- "Product not found"
  → 确认产品 ID 和 App Store Connect 配置
  
- "Purchase failed"
  → 检查沙盒测试账户状态
```

## 📋 测试检查清单

### 环境准备:
- [ ] 虚拟机正常运行
- [ ] Xcode 安装完成
- [ ] 开发者账号配置
- [ ] App Store Connect 产品创建

### 代码配置:
- [ ] Bundle ID 正确设置
- [ ] 产品 ID 匹配
- [ ] 签名证书有效
- [ ] StoreKit 功能启用

### 测试执行:
- [ ] 本地 StoreKit 测试通过
- [ ] 沙盒环境测试通过
- [ ] 订阅状态正确显示
- [ ] 支付流程完整

### 验证结果:
- [ ] 免费用户看到订阅页面
- [ ] 支付成功后状态更新
- [ ] 重启应用状态保持
- [ ] 过期处理正确

## 🚀 优化建议

1. **使用真实设备**: 虚拟机中的模拟器性能有限
2. **多账户测试**: 使用不同地区的沙盒账户
3. **网络稳定**: 确保虚拟机网络连接稳定
4. **定期清理**: 清理 Xcode 缓存和派生数据
5. **版本控制**: 使用 Git 管理配置变更

通过以上配置，您就可以在虚拟机中完整测试支付订阅功能了！
