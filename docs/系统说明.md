# TCA 模板项目 - 系统说明

## 📋 项目概述

这是一个基于 **Swift Composable Architecture (TCA)** 的 iOS 应用模板脚手架，提供了完整的项目结构、认证流程、导航系统和开发工具。

### 🎯 主要特性

- ✅ **完整的认证系统** - 登录 + 双因素认证
- ✅ **模块化架构** - 基于 Swift Package Manager 的模块化设计
- ✅ **标准导航** - Tab 导航 (Home + Profile)
- ✅ **通用组件库** - 可复用的 UI 组件
- ✅ **网络层抽象** - 统一的网络请求处理
- ✅ **全面测试覆盖** - 单元测试和集成测试
- ✅ **开发工具** - 自动化脚本和代码生成

## 🔧 系统要求

### iOS 版本支持
- **最低支持版本**: iOS 15.0+
- **推荐版本**: iOS 16.0+
- **测试版本**: iOS 17.0+

### 开发环境
- **Xcode**: 15.0+
- **Swift**: 5.9+
- **macOS**: 13.0+ (Ventura)

## 📦 核心依赖

### Swift Package Manager 依赖

```swift
dependencies: [
    // TCA 核心框架
    .package(url: "https://github.com/pointfreeco/swift-composable-architecture", from: "1.20.2"),
    
    // 导航支持
    .package(url: "https://github.com/pointfreeco/swift-navigation", from: "2.3.1"),
    
    // 依赖注入
    .package(url: "https://github.com/pointfreeco/swift-dependencies", from: "1.9.2"),
    
    // 状态共享
    .package(url: "https://github.com/pointfreeco/swift-sharing", from: "2.5.2"),
    
    // 感知系统
    .package(url: "https://github.com/pointfreeco/swift-perception", from: "1.6.0"),
    
    // 工具库
    .package(url: "https://github.com/pointfreeco/swift-case-paths", from: "1.7.1"),
    .package(url: "https://github.com/pointfreeco/swift-custom-dump", from: "1.3.3"),
    .package(url: "https://github.com/pointfreeco/swift-identified-collections", from: "1.1.1"),
    .package(url: "https://github.com/apple/swift-collections", from: "1.2.1"),
    
    // 测试工具
    .package(url: "https://github.com/pointfreeco/xctest-dynamic-overlay", from: "1.6.0"),
    
    // 并发和调度
    .package(url: "https://github.com/pointfreeco/swift-concurrency-extras", from: "1.3.1"),
    .package(url: "https://github.com/pointfreeco/combine-schedulers", from: "1.0.3"),
    .package(url: "https://github.com/pointfreeco/swift-clocks", from: "1.0.6")
]
```

## 📁 项目结构

```
tca-template/
├── 📱 App/                          # iOS 应用入口
│   ├── TcaTemplateApp.swift         # 应用主入口
│   ├── RootView.swift               # 根视图
│   └── Assets.xcassets              # 应用资源
│
├── 📦 tca-template/                 # Swift Package 模块
│   ├── Package.swift               # 包配置文件
│   │
│   ├── 🏗️ Sources/                  # 源代码模块
│   │   ├── AppCore/                 # 应用核心逻辑
│   │   ├── AppSwiftUI/              # 应用 UI 层
│   │   │
│   │   ├── 🔐 AuthenticationClient/ # 认证客户端接口
│   │   ├── AuthenticationClientLive/ # 认证客户端实现
│   │   │
│   │   ├── 🌐 NetworkClient/        # 网络客户端接口
│   │   ├── NetworkClientLive/       # 网络客户端实现
│   │   │
│   │   ├── 🎨 CommonUI/             # 通用 UI 组件
│   │   │
│   │   ├── 🏠 HomeCore/             # 首页核心逻辑
│   │   ├── HomeSwiftUI/             # 首页 UI 实现
│   │   │
│   │   ├── 👤 ProfileCore/          # 个人资料核心逻辑
│   │   ├── ProfileSwiftUI/          # 个人资料 UI 实现
│   │   │
│   │   ├── 🔑 LoginCore/            # 登录核心逻辑
│   │   ├── LoginSwiftUI/            # 登录 UI 实现
│   │   │
│   │   ├── 🔒 TwoFactorCore/        # 双因素认证核心逻辑
│   │   └── TwoFactorSwiftUI/        # 双因素认证 UI 实现
│   │
│   └── 🧪 Tests/                    # 测试文件
│       ├── AppCoreTests/            # 应用核心测试
│       ├── HomeCoreTests/           # 首页逻辑测试
│       ├── ProfileCoreTests/        # 个人资料测试
│       ├── LoginCoreTests/          # 登录逻辑测试
│       └── TwoFactorCoreTests/      # 双因素认证测试
│
├── 🛠️ Scripts/                      # 开发工具脚本
│   ├── generate-feature.sh         # 功能模块生成器
│   ├── rename-project.sh            # 项目重命名工具
│   ├── run-tests.sh                 # 测试运行脚本
│   └── setup-dev.sh                 # 开发环境设置
│
├── 📚 docs/                         # 文档目录
│   ├── architecture.md             # 架构说明
│   ├── feature-development.md      # 功能开发指南
│   └── 系统说明.md                  # 本文档
│
├── 🔧 tca-template.xcodeproj        # Xcode 项目文件
└── 📖 README.md                     # 项目说明
```

## 🏗️ 架构设计

### 模块化分层架构

```
┌─────────────────────────────────────┐
│            App Layer                │  ← iOS 应用入口
├─────────────────────────────────────┤
│          SwiftUI Layer              │  ← UI 视图层
├─────────────────────────────────────┤
│           Core Layer                │  ← 业务逻辑层 (TCA Reducers)
├─────────────────────────────────────┤
│          Client Layer               │  ← 客户端接口层
├─────────────────────────────────────┤
│         Live Client Layer           │  ← 客户端实现层
└─────────────────────────────────────┘
```

### 核心模块说明

| 模块 | 职责 | 依赖关系 |
|------|------|----------|
| **AppCore** | 应用级状态管理和路由 | 依赖所有 Core 模块 |
| **AppSwiftUI** | 应用级 UI 组合 | 依赖 AppCore 和所有 SwiftUI 模块 |
| **CommonUI** | 通用 UI 组件库 | 无依赖，被其他 UI 模块使用 |
| **NetworkClient** | 网络请求接口定义 | 基础依赖 |
| **AuthenticationClient** | 认证服务接口定义 | 依赖 NetworkClient |
| **HomeCore/ProfileCore** | 业务逻辑核心 | 依赖对应的 Client 模块 |
| **LoginCore/TwoFactorCore** | 认证逻辑核心 | 依赖 AuthenticationClient |

## 🚀 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd tca-template
```

### 2. 设置开发环境
```bash
chmod +x Scripts/setup-dev.sh
./Scripts/setup-dev.sh
```

### 3. 构建项目
```bash
# Swift Package 构建
cd tca-template
swift build

# 运行测试
swift test

# Xcode 构建
open tca-template.xcodeproj
```

### 4. 生成新功能模块
```bash
chmod +x Scripts/generate-feature.sh
./Scripts/generate-feature.sh Settings
```

## 🧪 测试策略

### 测试覆盖范围
- ✅ **单元测试**: 所有 Core 模块的业务逻辑
- ✅ **状态测试**: TCA Reducer 的状态变化
- ✅ **副作用测试**: 异步操作和网络请求
- ✅ **UI 测试**: 关键用户交互流程

### 运行测试
```bash
# 运行所有测试
./Scripts/run-tests.sh

# 运行特定模块测试
swift test --filter HomeCoreTests

# 生成测试报告
swift test --enable-code-coverage
```

## 🔧 开发工具

### 脚本工具说明

| 脚本 | 功能 | 使用方法 |
|------|------|----------|
| `generate-feature.sh` | 生成新功能模块 | `./Scripts/generate-feature.sh FeatureName` |
| `rename-project.sh` | 重命名整个项目 | `./Scripts/rename-project.sh NewProjectName` |
| `run-tests.sh` | 运行测试套件 | `./Scripts/run-tests.sh` |
| `setup-dev.sh` | 设置开发环境 | `./Scripts/setup-dev.sh` |

### 代码生成模板

生成的功能模块包含：
- Core 模块 (业务逻辑)
- SwiftUI 模块 (UI 实现)
- 测试文件
- Package.swift 配置更新

## 📱 功能模块

### 认证系统
- **登录流程**: 邮箱/密码认证
- **双因素认证**: 验证码确认
- **状态管理**: 登录状态持久化
- **错误处理**: 网络错误和验证错误

### 主要界面
- **首页 (Home)**: 内容展示和搜索
- **个人资料 (Profile)**: 用户信息管理
- **设置**: 应用配置选项

### 通用组件
- **LoadingButton**: 带加载状态的按钮
- **ErrorView**: 统一错误展示
- **SearchBar**: 搜索输入组件
- **TabView**: 标准标签导航

## 🔄 状态管理

### TCA 状态流
```
Action → Reducer → State → View
   ↑                        ↓
Effect ←←←←←←←←←←←←←←←←←←←←←←←
```

### 依赖注入
使用 `@Dependency` 进行依赖注入：
```swift
@Dependency(\.networkClient) var networkClient
@Dependency(\.authenticationClient) var authenticationClient
```

## 📈 性能优化

### 编译优化
- 模块化设计减少编译时间
- 接口分离降低模块耦合
- 增量编译支持

### 运行时优化
- 状态最小化原则
- 副作用合理管控
- 内存使用优化

## 🔒 安全考虑

### 数据安全
- 敏感信息加密存储
- 网络传输 HTTPS
- Token 安全管理

### 代码安全
- 输入验证
- 错误处理
- 日志脱敏

---

## 🎨 UI 设计规范

### 设计系统
- **颜色主题**: 支持浅色/深色模式
- **字体系统**: SF Pro 系统字体
- **间距规范**: 8pt 网格系统
- **组件库**: 基于 SwiftUI 的可复用组件

### 响应式设计
- **iPhone**: 适配所有尺寸 (SE ~ Pro Max)
- **iPad**: 支持横竖屏切换
- **动态字体**: 支持辅助功能

## 🌐 网络架构

### 网络层设计
```swift
protocol NetworkClient {
    func request<T: Decodable>(
        _ endpoint: Endpoint,
        as type: T.Type
    ) async throws -> T
}
```

### 错误处理
- **网络错误**: 连接超时、无网络
- **服务器错误**: 4xx、5xx 状态码
- **数据错误**: JSON 解析失败
- **业务错误**: 自定义业务异常

### 请求配置
- **Base URL**: 可配置的服务器地址
- **超时设置**: 30秒请求超时
- **重试机制**: 自动重试失败请求
- **缓存策略**: 智能缓存管理

## 🔧 配置管理

### 环境配置
```swift
enum Environment {
    case development
    case staging
    case production

    var baseURL: String { ... }
    var apiKey: String { ... }
}
```

### 构建配置
- **Debug**: 开发调试版本
- **Release**: 生产发布版本
- **Testing**: 测试专用版本

## 📊 数据流架构

### 单向数据流
```
┌─────────┐    Action    ┌─────────┐    State    ┌──────┐
│  View   │ ──────────→  │ Reducer │ ──────────→ │ View │
└─────────┘              └─────────┘             └──────┘
     ↑                        │
     │                        ↓
     │                   ┌─────────┐
     └─────────────────  │ Effect  │
                         └─────────┘
```

### 状态持久化
- **UserDefaults**: 简单配置存储
- **Keychain**: 敏感信息存储
- **Core Data**: 复杂数据存储 (可选)

## 🚀 部署指南

### 构建流程
1. **代码检查**: SwiftLint 代码规范
2. **单元测试**: 确保测试通过
3. **构建验证**: 多平台构建测试
4. **打包签名**: 证书和描述文件
5. **上传分发**: App Store Connect

### CI/CD 集成
```yaml
# GitHub Actions 示例
name: iOS Build
on: [push, pull_request]
jobs:
  test:
    runs-on: macos-latest
    steps:
    - uses: actions/checkout@v3
    - name: Run Tests
      run: |
        cd tca-template
        swift test
```

## 🔍 调试技巧

### TCA 调试
```swift
// 启用状态变化日志
AppFeature()._printChanges()

// 自定义调试输出
.debug("LoginFeature")
```

### 网络调试
- **Charles Proxy**: 网络请求抓包
- **Console.app**: 系统日志查看
- **Xcode Network**: 网络请求监控

### 性能调试
- **Instruments**: 性能分析工具
- **Memory Graph**: 内存泄漏检测
- **Time Profiler**: CPU 使用分析

## 📚 学习资源

### TCA 相关
- [官方文档](https://pointfreeco.github.io/swift-composable-architecture/)
- [Point-Free 视频教程](https://www.pointfree.co)
- [TCA 示例项目](https://github.com/pointfreeco/swift-composable-architecture/tree/main/Examples)

### Swift 开发
- [Swift 官方文档](https://docs.swift.org)
- [SwiftUI 教程](https://developer.apple.com/tutorials/swiftui)
- [iOS 开发指南](https://developer.apple.com/ios/)

## 🔄 版本更新

### 更新日志
- **v1.0.0**: 初始版本发布
- **v1.1.0**: 添加 Profile 模块
- **v1.2.0**: 网络层重构
- **v2.0.0**: iOS 15+ 兼容性更新

### 升级指南
1. 备份当前项目
2. 查看 CHANGELOG.md
3. 更新依赖版本
4. 运行迁移脚本
5. 测试功能完整性

## 🤝 贡献指南

### 开发流程
1. Fork 项目仓库
2. 创建功能分支
3. 编写代码和测试
4. 提交 Pull Request
5. 代码审查和合并

### 代码规范
- 遵循 Swift API 设计指南
- 使用 SwiftLint 检查代码
- 编写清晰的注释和文档
- 保持测试覆盖率 > 80%

### 提交规范
```
feat: 添加新功能
fix: 修复 bug
docs: 更新文档
style: 代码格式调整
refactor: 代码重构
test: 添加测试
chore: 构建工具更新
```

## 📞 支持与联系

### 技术支持
- **GitHub Issues**: 报告 bug 和功能请求
- **Discussions**: 技术讨论和问答
- **Wiki**: 详细文档和教程

### 社区资源
- **Swift 中文社区**: iOS 开发交流
- **TCA 学习群**: 架构模式讨论
- **开源贡献**: 参与项目改进

---

## 📄 许可证

本项目采用 MIT 许可证，详见 [LICENSE](../LICENSE) 文件。

**Happy Coding! 🚀**
