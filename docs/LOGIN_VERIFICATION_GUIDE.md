# 🔐 Apple ID登录功能验证指南

## ❌ 虚拟机限制说明

您遇到的错误是预期的，因为Apple ID登录在虚拟机环境中有以下限制：

```
MCPasscodeManager passcode set check is not supported on this device.
Authorization failed: Error Domain=AKAuthenticationError Code=-7026
ASAuthorizationController credential request failed with error: Code=1000
```

### 🚫 虚拟机环境限制
- **Apple ID服务**: 虚拟机无法访问真实的Apple ID服务
- **Keychain服务**: 虚拟机的Keychain功能受限
- **生物识别**: 无Touch ID/Face ID硬件支持
- **设备认证**: 缺少真实设备的硬件标识

## ✅ 验证方案

### 方案1: Mock测试验证 (推荐用于开发阶段)

让我们创建一个完整的Mock测试来验证登录逻辑：

#### 1. 启用Mock模式
在`AuthenticationClientLive.swift`中添加Mock模式开关：

```swift
extension AuthenticationClient {
  public static let mockValue = Self(
    signInWithApple: { credential in
      // 模拟Apple ID登录成功
      try await Task.sleep(for: .seconds(1))
      
      let mockUser = AuthenticatedUser(
        id: "mock_apple_user_\(UUID().uuidString.prefix(8))",
        email: credential.email ?? "<EMAIL>",
        displayName: credential.fullName?.formatted() ?? "Mock Apple User",
        avatarURL: nil
      )
      
      return AuthenticationResponse(
        token: "mock_apple_token_\(Date().timeIntervalSince1970)",
        twoFactorRequired: false,
        user: mockUser
      )
    },
    // ... 其他方法的Mock实现
  )
}
```

#### 2. 在App中启用Mock模式
在`BridalApp.swift`中添加编译条件：

```swift
#if DEBUG || targetEnvironment(simulator)
  .dependency(\.authenticationClient, .mockValue)
#endif
```

### 方案2: 单元测试验证

创建详细的单元测试来验证登录和存储逻辑：

#### 测试文件: `LoginKeychainTests.swift`

```swift
import Testing
import ComposableArchitecture
@testable import UserStateCore
@testable import KeychainClient
@testable import UserStorageClient

@Test
func testAppleIDLoginFlow() async throws {
  // 创建测试存储
  let testStore = TestStore(initialState: UserState.State()) {
    UserState()
  } withDependencies: {
    $0.authenticationClient = .mockValue
    $0.keychainClient = .testValue
    $0.userStorageClient = .testValue
  }
  
  // 模拟Apple ID凭证
  let mockCredential = AppleIDCredential(
    userID: "test_user_123",
    email: "<EMAIL>",
    fullName: PersonNameComponents(givenName: "Test", familyName: "User"),
    identityToken: Data(),
    authorizationCode: Data()
  )
  
  // 测试登录流程
  await testStore.send(.signInWithApple(mockCredential)) {
    $0.isLoading = true
    $0.error = nil
  }
  
  // 验证登录成功
  await testStore.receive(\.loginSucceeded) { state in
    state.authenticationStatus = .authenticated
    state.isLoading = false
    state.user = User(
      id: "mock_apple_user_2E0F16A9",
      email: "<EMAIL>",
      displayName: "Test User",
      avatarURL: nil,
      createdAt: Date(),
      subscriptionStatus: .premium
    )
  }
}

@Test
func testKeychainStorage() async throws {
  var keychain = KeychainClient.testValue
  var storage: [String: Data] = [:]
  
  // Mock Keychain操作
  keychain.save = { key, data in
    storage[key] = data
  }
  
  keychain.load = { key in
    return storage[key]
  }
  
  // 测试存储用户数据
  let testUser = User(
    id: "test_123",
    email: "<EMAIL>",
    displayName: "Test User",
    avatarURL: nil,
    createdAt: Date(),
    subscriptionStatus: .premium
  )
  
  let testToken = "test_access_token_123"
  
  // 保存数据
  try await keychain.saveString(testToken, forKey: "access_token")
  try await keychain.save(testUser, forKey: "user_data")
  
  // 验证数据
  let loadedToken = try await keychain.loadString(forKey: "access_token")
  let loadedUser = try await keychain.load(User.self, forKey: "user_data")
  
  #expect(loadedToken == testToken)
  #expect(loadedUser?.id == testUser.id)
  #expect(loadedUser?.email == testUser.email)
}
```

### 方案3: 真实设备测试 (推荐用于最终验证)

#### 准备工作
1. **真实iOS设备** (iPhone/iPad)
2. **开发者账号** 配置Apple ID登录
3. **证书配置** 正确的Bundle ID和证书

#### 测试步骤

##### 1. 配置Apple ID登录
在Xcode项目中：
- 选择Target → Signing & Capabilities
- 添加"Sign in with Apple"能力
- 确保Bundle ID正确配置

##### 2. 真实设备测试流程
```swift
// 在真实设备上测试的完整流程
func testRealDeviceLogin() {
  // 1. 启动应用
  // 2. 点击"Apple ID登录"按钮
  // 3. 系统弹出Apple ID登录界面
  // 4. 完成Face ID/Touch ID验证
  // 5. 获取用户凭证
  // 6. 保存到Keychain
  // 7. 更新应用状态
  // 8. 关闭应用重新打开
  // 9. 验证自动登录
}
```

##### 3. Keychain验证
在真实设备上验证Keychain存储：

```swift
// 添加调试代码来验证Keychain
func debugKeychainContents() async {
  do {
    let keychain = KeychainClient.liveValue
    
    // 检查存储的令牌
    if let token = try await keychain.loadString(forKey: "access_token") {
      print("✅ 找到访问令牌: \(token.prefix(20))...")
    } else {
      print("❌ 未找到访问令牌")
    }
    
    // 检查用户数据
    if let userData = try await keychain.load(StoredUserData.self, forKey: "user_data") {
      print("✅ 找到用户数据:")
      print("   用户ID: \(userData.id)")
      print("   邮箱: \(userData.email)")
      print("   显示名: \(userData.displayName)")
      print("   最后登录: \(userData.lastLoginDate)")
    } else {
      print("❌ 未找到用户数据")
    }
    
  } catch {
    print("❌ Keychain操作失败: \(error)")
  }
}
```

## 🧪 当前环境验证方案

### 立即可执行的验证

#### 1. 运行Mock测试
```bash
# 在项目目录下运行
swift test --filter LoginKeychainTests
```

#### 2. 运行功能验证脚本
```bash
# 运行我们之前创建的测试脚本
swift test_keychain_login.swift
```

#### 3. 验证Mock登录流程
在Simulator中启用Mock模式，测试完整的登录流程：

```swift
// 在UserStateCore中添加调试输出
case let .signInWithApple(credential):
  print("🍎 开始Apple ID登录 (Mock模式)")
  print("   用户ID: \(credential.userID)")
  print("   邮箱: \(credential.email ?? "未提供")")
  
  return .run { send in
    do {
      // 使用Mock认证客户端
      let response = try await self.authenticationClient.signInWithApple(credential)
      print("✅ Mock登录成功")
      await send(.loginSucceeded(response.user!, response.token))
    } catch {
      print("❌ Mock登录失败: \(error)")
      await send(.loginFailed(error.localizedDescription))
    }
  }
```

### 验证清单

#### ✅ Mock环境验证 (当前可执行)
- [ ] Mock登录流程完整
- [ ] 用户状态正确更新
- [ ] Keychain存储操作正常
- [ ] 自动登录恢复功能
- [ ] 错误处理机制

#### ✅ 真实设备验证 (需要真实设备)
- [ ] Apple ID登录界面正常弹出
- [ ] 生物识别验证正常
- [ ] 用户凭证获取成功
- [ ] Keychain数据存储成功
- [ ] 应用重启后自动登录
- [ ] 登出功能正常

## 🔧 调试工具

### 1. Keychain查看器
```swift
// 添加到AppCore中的调试方法
#if DEBUG
func debugPrintKeychainContents() async {
  @Dependency(\.keychainClient) var keychain
  
  print("🔍 Keychain内容检查:")
  
  // 检查所有可能的键
  let keys = ["access_token", "user_data", "refresh_token"]
  
  for key in keys {
    do {
      if let data = try await keychain.load(key) {
        print("   ✅ \(key): \(data.count) bytes")
      } else {
        print("   ❌ \(key): 未找到")
      }
    } catch {
      print("   ❌ \(key): 错误 - \(error)")
    }
  }
}
#endif
```

### 2. 登录状态监控
```swift
// 在UserStateCore中添加状态变化监控
public var body: some ReducerOf<Self> {
  Reduce { state, action in
    // 监控所有状态变化
    defer {
      print("🔄 用户状态更新:")
      print("   认证状态: \(state.authenticationStatus)")
      print("   用户: \(state.user?.displayName ?? "未登录")")
      print("   加载中: \(state.isLoading)")
    }
    
    switch action {
    // ... 现有的action处理
    }
  }
}
```

## 📋 验证报告模板

### 测试结果记录
```
🔐 Apple ID登录功能验证报告
================================

测试环境: [ ] Simulator [ ] 真实设备
测试日期: ___________
测试人员: ___________

功能验证:
[ ] Mock登录流程
[ ] Keychain存储
[ ] 自动登录恢复
[ ] 错误处理
[ ] 用户状态管理

问题记录:
1. ________________
2. ________________
3. ________________

总体评价: [ ] 通过 [ ] 部分通过 [ ] 失败
```

## 🎯 推荐验证顺序

1. **立即执行**: 运行Mock测试和验证脚本
2. **Simulator测试**: 在模拟器中测试Mock登录流程
3. **真实设备测试**: 在真实设备上测试完整功能
4. **生产环境验证**: 在生产配置下最终验证

这样可以确保登录功能在各种环境下都能正常工作！
