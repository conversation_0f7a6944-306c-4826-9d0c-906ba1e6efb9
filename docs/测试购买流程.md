# 测试购买流程指南

## 🎯 当前状态

您的代码已经配置为在无法加载真实产品时自动使用模拟产品，这意味着您可以立即测试购买流程。

## 🧪 测试步骤

### 1. 验证产品加载
运行应用后，查看控制台输出：

```
🛒 Loading products from App Store...
🛒 Environment: SANDBOX
❌ Failed to load products: [错误信息]
🔄 Fallback to mock products for development/testing
```

如果看到这些日志，说明代码正在使用模拟产品，这是正常的。

### 2. 测试订阅页面
1. 打开应用
2. 进入订阅页面
3. 应该能看到3个产品：
   - 单次生成 (¥1)
   - 高级月度订阅 (¥28) - 标记为"最受欢迎"
   - 年度订阅 (¥128)

### 3. 测试购买流程
1. 点击任意产品的购买按钮
2. 查看控制台输出：

```
💳 [SubscriptionCore Live] 开始真实购买: 高级月度订阅
❌ [SubscriptionCore Live] 产品未找到: com.wenhaofree.bridal.sub_monthly_40
❌ [SubscriptionCore Live] 购买失败: productNotFound
```

这是预期的行为，因为没有配置真实的StoreKit产品。

### 4. 验证状态更新
即使购买失败，您也可以通过以下方式测试状态更新逻辑：

1. 在SubscriptionCore中临时修改购买逻辑
2. 或者使用StoreKit Configuration文件

## 🛠️ 立即可用的解决方案

### 选项1: 使用StoreKit Configuration（推荐）

1. 在Xcode中创建StoreKit Configuration文件
2. 添加产品配置（参考Configuration.storekit文件）
3. 在Scheme中启用StoreKit Configuration
4. 重新运行应用

### 选项2: 临时修改代码进行测试

如果您想立即测试订阅状态更新，可以临时修改SubscriptionCore：

```swift
// 在SubscriptionCore.swift的purchase函数中
// 临时注释掉真实购买逻辑，直接返回成功状态
purchase: { product in
  print("💳 [SubscriptionCore Live] 模拟购买成功: \(product.displayName)")
  
  // 模拟成功购买
  if let duration = product.duration {
    let calendar = Calendar.current
    let now = Date()
    let expiryDate: Date
    
    switch duration {
    case .monthly:
      expiryDate = calendar.date(byAdding: .month, value: 1, to: now) ?? now
    case .yearly:
      expiryDate = calendar.date(byAdding: .year, value: 1, to: now) ?? now
    default:
      expiryDate = calendar.date(byAdding: .month, value: 1, to: now) ?? now
    }
    
    print("🎉 [SubscriptionCore Live] 模拟订阅成功，过期时间: \(expiryDate)")
    return .premium(expiryDate: expiryDate, usageLimits: product.usageLimits)
  } else {
    // 单次购买
    let expiryDate = Date().addingTimeInterval(24 * 60 * 60)
    return .premium(expiryDate: expiryDate, usageLimits: product.usageLimits)
  }
}
```

## 🔍 验证订阅状态更新

购买成功后，您应该能看到：

1. **控制台输出**：
```
✅ Subscription completed from settings page
📊 Settings subscription status: Premium (expires: 2024-08-29)
👤 User subscription status updated in profile from settings
```

2. **UI更新**：
   - Profile页面显示Premium状态
   - 订阅成功提示
   - 相关功能解锁

3. **状态持久化**：
   - 订阅状态保存到本地存储
   - 重启应用后状态保持

## 📱 真实设备测试

要在真实设备上测试StoreKit购买：

1. 使用真实iOS设备（不是模拟器）
2. 配置沙盒测试账户
3. 在App Store Connect中配置产品
4. 确保应用Bundle ID与App Store Connect匹配

## 🎯 下一步

1. **立即测试**: 使用当前的模拟产品功能
2. **配置StoreKit**: 创建Configuration.storekit文件
3. **App Store Connect**: 配置真实产品用于沙盒测试
4. **真实测试**: 在真实设备上使用沙盒账户测试

您的代码架构已经完整，只需要配置StoreKit产品即可进行真实测试！
