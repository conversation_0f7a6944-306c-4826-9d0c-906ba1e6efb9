# StoreKit故障排除指南

## 🚨 问题现象
```
💳 [SubscriptionCore Live] 开始真实购买: 高级月度订阅
❌ [SubscriptionCore Live] 产品未找到: com.wenhaofree.bridal.sub_monthly_40
❌ [SubscriptionCore Live] 购买失败: productNotFound
```

## 🔍 问题原因分析

### 主要原因
即使配置了StoreKit Configuration文件，SubscriptionCore中的purchase函数仍然直接调用`Product.products(for:)`，这不会使用StoreKit Configuration，而是尝试从真实的App Store Connect加载产品。

### 技术细节
1. **StoreKitClient.loadProducts()** - 可以使用StoreKit Configuration
2. **SubscriptionCore.purchase()** - 直接调用StoreKit API，绕过了Configuration

## ✅ 解决方案

### 方案1: 使用测试模式（推荐）

我已经在代码中添加了测试模式，现在有3种方式触发测试购买：

#### 1.1 在模拟器中运行
- 在iOS模拟器中运行应用
- 代码会自动检测模拟器环境并模拟购买成功

#### 1.2 设置环境变量
在Xcode中设置环境变量：
1. Edit Scheme → Run → Environment Variables
2. 添加: `ENABLE_PURCHASE_TEST = 1`
3. 运行应用

#### 1.3 使用StoreKit Configuration文件
1. 确保Configuration.storekit文件在项目中
2. Edit Scheme → Run → StoreKit Configuration
3. 选择Configuration.storekit文件

### 方案2: 检查配置是否正确

运行应用后，查看控制台输出：

```
🔍 Configuration.storekit file found in bundle: true/false
🔍 Running on simulator: true/false
🔍 STOREKIT_CONFIG environment variable: true/false
🧪 StoreKit Configuration detected: true/false
```

如果所有值都是false，说明配置有问题。

### 方案3: 手动验证StoreKit Configuration

#### 3.1 检查文件位置
确保Configuration.storekit文件：
- 已添加到Xcode项目中
- 在项目的Target中被包含
- 文件内容格式正确

#### 3.2 验证产品配置
在Configuration.storekit中确保包含：
```json
{
  "products": [
    {
      "identifier": "com.bridal.single.basic",
      "productID": "com.bridal.single.basic",
      "type": "NonConsumable",
      "displayPrice": "1.00"
    }
  ],
  "subscriptions": [
    {
      "identifier": "com.wenhaofree.bridal.sub_monthly_40",
      "productID": "com.wenhaofree.bridal.sub_monthly_40",
      "type": "RecurringSubscription",
      "displayPrice": "28.00"
    },
    {
      "identifier": "com.bridal.yearly",
      "productID": "com.bridal.yearly", 
      "type": "RecurringSubscription",
      "displayPrice": "128.00"
    }
  ]
}
```

## 🧪 测试步骤

### 立即测试（推荐）

1. **在模拟器中运行应用**
2. **进入订阅页面**
3. **点击购买按钮**
4. **查看控制台输出**：

期望看到：
```
🔍 [SubscriptionCore] StoreKit Config: true, Test Mode: false, Simulator: true
🧪 [SubscriptionCore Live] 检测到测试环境，模拟购买成功
🎉 [SubscriptionCore Live] 模拟订阅成功，过期时间: 2024-08-29
```

### 使用环境变量测试

1. **Edit Scheme → Run → Environment Variables**
2. **添加: `ENABLE_PURCHASE_TEST = 1`**
3. **运行应用并测试购买**

### 验证StoreKit Configuration

1. **确保Configuration.storekit在项目中**
2. **Edit Scheme → Run → StoreKit Configuration**
3. **选择Configuration.storekit**
4. **运行应用**

## 🔧 调试信息

运行应用时，注意以下调试输出：

### 产品加载阶段
```
🛒 Loading products from App Store...
🛒 Environment: SANDBOX
🔍 Configuration.storekit file found in bundle: true
🔍 Running on simulator: true
🧪 StoreKit Configuration detected: true
🧪 Using StoreKit Configuration for testing
```

### 购买阶段
```
💳 [SubscriptionCore Live] 开始真实购买: 高级月度订阅
🔍 [SubscriptionCore] StoreKit Config: true, Test Mode: false, Simulator: true
🧪 [SubscriptionCore Live] 检测到测试环境，模拟购买成功
🎉 [SubscriptionCore Live] 模拟订阅成功，过期时间: 2024-08-29
```

## 🎯 快速解决方案

如果您想立即测试购买流程：

1. **在iOS模拟器中运行应用**（最简单）
2. **或者设置环境变量 `ENABLE_PURCHASE_TEST = 1`**
3. **进入订阅页面并点击购买**
4. **应该看到购买成功的消息**

## ⚠️ 注意事项

- 测试模式只在DEBUG构建中有效
- 真实设备测试需要配置App Store Connect
- StoreKit Configuration只在开发环境中工作
- 生产环境会使用真实的StoreKit API

## 📱 真实设备测试

如果需要在真实设备上测试：
1. 配置App Store Connect中的产品
2. 创建沙盒测试账户
3. 在设备上登录沙盒账户
4. 运行应用进行测试

现在您应该能够在模拟器中成功测试购买流程了！
