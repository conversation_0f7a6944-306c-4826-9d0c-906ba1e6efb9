# 产品ID命名统一修改总结

## 🎯 修改目标

参考`com.wenhaofree.bridal.sub_monthly_40`的命名格式，统一所有产品ID的命名规范，包含一次性购买和年度订阅。

## 📋 统一命名规范

### 新的命名格式：
```
com.wenhaofree.bridal.{type}_{description}
```

### 具体产品ID：

| 产品类型 | 旧ID | 新ID | 说明 |
|---------|------|------|------|
| 一次性购买 | `com.bridal.single.basic` | `com.wenhaofree.bridal.single_basic` | 1次高清图片生成 |
| 月度订阅 | `com.wenhaofree.bridal.sub_monthly_40` | `com.wenhaofree.bridal.sub_monthly_40` | 每月40次生成（保持不变） |
| 年度订阅 | `com.bridal.yearly` | `com.wenhaofree.bridal.sub_yearly_600` | 每年600次生成 |

## ✅ 修改的文件

### 1. Configuration.storekit
**StoreKit配置文件**
- ✅ 更新一次性购买产品ID：`com.bridal.single.basic` → `com.wenhaofree.bridal.single_basic`
- ✅ 更新年度订阅产品ID：`com.bridal.yearly` → `com.wenhaofree.bridal.sub_yearly_600`
- ✅ 保持月度订阅ID不变：`com.wenhaofree.bridal.sub_monthly_40`

### 2. SubscriptionCore.swift
**订阅核心逻辑**
- ✅ 更新productIDs数组中的所有产品ID
- ✅ 更新getProductType函数中的产品类型映射
- ✅ 更新getDuration函数中的订阅时长映射
- ✅ 更新getUsageLimits函数中的使用限制映射
- ✅ 更新mockProducts函数中的模拟产品数据

### 3. StoreKitClientLive.swift
**StoreKit客户端实现**
- ✅ 更新LiveStoreKitClient.productIDs数组
- ✅ 更新mapProductToDetails函数中的产品映射
- ✅ 更新mockProducts函数中的模拟产品数据

### 4. SubscriptionView.swift
**订阅界面Preview**
- ✅ 更新Preview中的产品ID引用
- ✅ 确保UI预览使用正确的产品ID

## 🔍 命名规范解析

### 前缀统一：`com.wenhaofree.bridal`
- 使用统一的Bundle ID前缀
- 保持品牌一致性
- 便于App Store管理

### 类型标识：
- `single_` - 一次性购买产品
- `sub_` - 订阅产品

### 描述信息：
- `basic` - 基础功能
- `monthly_40` - 月度40次
- `yearly_600` - 年度600次

## 🧪 验证结果

### 构建状态：
```
Build complete! (4.52s)
```
✅ **构建成功**，所有产品ID引用已正确更新

### 功能验证：
- ✅ StoreKit配置文件语法正确
- ✅ 所有代码中的产品ID引用一致
- ✅ 模拟产品数据正确
- ✅ UI预览正常显示

## 📊 产品配置对比

### 修改前：
```
一次性购买: com.bridal.single.basic
月度订阅:   com.wenhaofree.bridal.sub_monthly_40
年度订阅:   com.bridal.yearly
```

### 修改后：
```
一次性购买: com.wenhaofree.bridal.single_basic
月度订阅:   com.wenhaofree.bridal.sub_monthly_40
年度订阅:   com.wenhaofree.bridal.sub_yearly_600
```

## 🎯 优势

### 1. 命名一致性
- 所有产品ID使用相同的前缀格式
- 类型标识清晰明确
- 便于维护和管理

### 2. 可扩展性
- 新增产品时可以遵循相同规范
- 支持更多产品类型和变体
- 便于自动化处理

### 3. 可读性
- 产品ID包含功能描述信息
- 开发者可以快速理解产品类型
- 便于调试和日志分析

## 🚀 下一步

### App Store Connect配置
在App Store Connect中创建对应的产品时，请使用以下新的产品ID：

1. **一次性购买**：`com.wenhaofree.bridal.single_basic`
   - 类型：非消耗型项目
   - 价格：¥1.00
   - 显示名称：单次生成

2. **月度订阅**：`com.wenhaofree.bridal.sub_monthly_40`
   - 类型：自动续期订阅
   - 价格：¥28.00
   - 显示名称：高级月度订阅

3. **年度订阅**：`com.wenhaofree.bridal.sub_yearly_600`
   - 类型：自动续期订阅
   - 价格：¥128.00
   - 显示名称：年度订阅

### 测试验证
- 在StoreKit Configuration中测试新的产品ID
- 验证购买流程正常工作
- 确认订阅状态正确更新

所有产品ID命名已成功统一！🎉
