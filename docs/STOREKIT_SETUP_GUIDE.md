# StoreKit 配置指南

## 🛒 App Store Connect 配置

### 1. 创建应用内购买产品

在 App Store Connect 中创建以下订阅产品：

#### 月度订阅
- **产品ID**: `com.bridal.monthly`
- **参考名称**: "Pro 月度订阅"
- **订阅群组**: "Pro Subscription"
- **订阅时长**: 1个月
- **价格**: ¥18 (或根据市场调整)

#### 年度订阅
- **产品ID**: `com.bridal.yearly`
- **参考名称**: "Pro 年度订阅"
- **订阅群组**: "Pro Subscription"
- **订阅时长**: 1年
- **价格**: ¥128 (或根据市场调整)
- **推荐**: 设为推荐产品

### 2. 配置订阅群组

1. 创建订阅群组 "Pro Subscription"
2. 设置订阅级别：年度订阅 > 月度订阅
3. 配置家庭共享（如需要）

### 3. 添加本地化信息

为每个产品添加中文本地化：
- **显示名称**: "Pro 月度订阅" / "Pro 年度订阅"
- **描述**: "解锁所有高级风格和功能，享受专业级AI婚纱照生成"

## 🧪 测试环境配置

### 1. 沙盒测试用户

在 App Store Connect 中创建沙盒测试用户：
1. 进入 "用户和访问" > "沙盒测试员"
2. 创建测试账户
3. 使用测试账户在设备上登录

### 2. StoreKit 配置文件 (推荐)

创建本地 StoreKit 配置文件用于开发测试：

```json
{
  "identifier" : "BridalApp",
  "nonRenewingSubscriptions" : [],
  "products" : [],
  "settings" : {
    "_applicationInternalID" : "123456789",
    "_developerTeamID" : "YOUR_TEAM_ID",
    "_failTransactionsEnabled" : false,
    "_locale" : "zh_CN",
    "_storefront" : "CHN",
    "_storeKitErrors" : []
  },
  "subscriptionGroups" : [
    {
      "id" : "21234567",
      "localizations" : [
        {
          "description" : "Pro订阅服务",
          "displayName" : "Pro Subscription",
          "locale" : "zh_CN"
        }
      ],
      "name" : "Pro Subscription",
      "subscriptions" : [
        {
          "adHocOffers" : [],
          "codeOffers" : [],
          "displayPrice" : "18.00",
          "familyShareable" : false,
          "id" : "com.bridal.monthly",
          "introductoryOffer" : null,
          "localizations" : [
            {
              "description" : "解锁所有高级风格和功能",
              "displayName" : "Pro 月度订阅",
              "locale" : "zh_CN"
            }
          ],
          "productID" : "com.bridal.monthly",
          "recurringSubscriptionPeriod" : "P1M",
          "referenceName" : "Monthly Pro",
          "subscriptionGroupID" : "21234567",
          "type" : "RecurringSubscription"
        },
        {
          "adHocOffers" : [],
          "codeOffers" : [],
          "displayPrice" : "128.00",
          "familyShareable" : false,
          "id" : "com.bridal.yearly",
          "introductoryOffer" : null,
          "localizations" : [
            {
              "description" : "解锁所有高级风格和功能，享受最大优惠",
              "displayName" : "Pro 年度订阅",
              "locale" : "zh_CN"
            }
          ],
          "productID" : "com.bridal.yearly",
          "recurringSubscriptionPeriod" : "P1Y",
          "referenceName" : "Yearly Pro",
          "subscriptionGroupID" : "21234567",
          "type" : "RecurringSubscription"
        }
      ]
    }
  ],
  "version" : {
    "major" : 3,
    "minor" : 0
  }
}
```

### 3. Xcode 项目配置

1. 在 Xcode 中添加 StoreKit 配置文件
2. 在 Scheme 中启用 StoreKit 配置文件
3. 确保 Bundle ID 与 App Store Connect 中的一致

## 🔧 代码实现说明

### 1. 环境切换

应用会根据运行环境自动切换：

```swift
#if DEBUG || targetEnvironment(simulator)
// 调试/模拟器环境：使用模拟支付
$0.storeKitClient = .testValue
#else
// 真实设备：使用真正的 StoreKit
$0.storeKitClient = $0.liveStoreKitClient
#endif
```

### 2. 支付流程

1. **加载产品**: 从 App Store 获取产品信息
2. **发起购买**: 调用 StoreKit 2 购买API
3. **验证交易**: 验证购买凭证
4. **更新状态**: 更新用户订阅状态
5. **完成交易**: 标记交易完成

### 3. 错误处理

- 产品未找到
- 用户取消购买
- 网络错误
- 验证失败

## 📱 测试步骤

### 1. 开发环境测试

1. 使用 Xcode 模拟器运行应用
2. 选择付费模板触发订阅流程
3. 验证模拟支付成功
4. 检查用户状态更新

### 2. 沙盒环境测试

1. 在真实设备上安装应用
2. 使用沙盒测试账户登录
3. 进行真实的支付测试
4. 验证订阅状态同步

### 3. 生产环境验证

1. 通过 TestFlight 分发测试版本
2. 使用真实账户进行小额测试
3. 验证支付和订阅功能正常

## ⚠️ 注意事项

1. **产品ID必须匹配**: 代码中的产品ID必须与App Store Connect中的完全一致
2. **Bundle ID一致性**: 确保所有配置使用相同的Bundle ID
3. **测试账户**: 不要在生产环境使用测试账户
4. **交易完成**: 必须调用 `transaction.finish()` 完成交易
5. **收据验证**: 在服务端验证收据以确保安全性

## 🚀 上线检查清单

- [ ] App Store Connect 中产品配置完成
- [ ] 沙盒测试通过
- [ ] 真实设备测试通过
- [ ] 订阅状态正确同步
- [ ] 错误处理完善
- [ ] 用户界面更新正确
- [ ] 服务端收据验证（如有）
