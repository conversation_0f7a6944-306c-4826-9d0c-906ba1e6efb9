# 技术规格文档

## 📋 项目概览

**项目名称**: Dream Wedding - AI婚纱照生成应用  
**技术栈**: Swift 6 + SwiftUI + TCA  
**架构模式**: The Composable Architecture (TCA)  
**开发工具**: Xcode 15.0+, Swift Package Manager  

## 🛠️ 技术栈详情

### 核心技术
| 技术 | 版本 | 用途 |
|------|------|------|
| Swift | 6.0+ | 主要编程语言 |
| SwiftUI | iOS 16+ | 用户界面框架 |
| TCA | 1.15.0+ | 状态管理架构 |
| Combine | iOS 16+ | 响应式编程 |

### 开发工具
| 工具 | 版本 | 用途 |
|------|------|------|
| Xcode | 15.0+ | 集成开发环境 |
| Swift Package Manager | 6.0+ | 依赖管理 |
| Git | 2.0+ | 版本控制 |

### 平台支持
| 平台 | 最低版本 | 目标版本 |
|------|----------|----------|
| iOS | 16.0 | 17.0+ |
| macOS | 13.0 | 14.0+ |
| tvOS | 16.0 | 17.0+ |
| watchOS | 9.0 | 10.0+ |

## 🏗️ 架构设计

### TCA架构模式
```
┌─────────────────┐
│      View       │ ← SwiftUI Views
├─────────────────┤
│     Store       │ ← State Container
├─────────────────┤
│    Reducer      │ ← Business Logic
├─────────────────┤
│    Effect       │ ← Side Effects
└─────────────────┘
```

### 模块化架构
```
App
├── Core Modules
│   ├── AppCore              # 应用核心逻辑
│   ├── UserStateCore        # 用户状态管理
│   ├── AuthenticationClient # 认证服务
│   └── NetworkClient        # 网络服务
├── Feature Modules
│   ├── HomeCore            # 首页功能
│   ├── PhotoUploadCore     # 照片上传
│   ├── StyleSelectionCore  # 风格选择
│   ├── AIGenerationCore    # AI生成
│   └── ProfileCore         # 用户资料
├── UI Modules
│   ├── AppSwiftUI          # 主应用UI
│   ├── UserStateSwiftUI    # 用户状态UI
│   ├── CommonUI            # 通用UI组件
│   └── [Feature]SwiftUI    # 各功能UI
└── Support
    ├── BridalApp           # 应用入口
    └── Tests               # 测试模块
```

## 🔧 核心组件

### 1. 用户状态管理 (UserStateCore)
```swift
@Reducer
public struct UserState: Sendable {
  @ObservableState
  public struct State: Equatable {
    public var authenticationStatus: AuthenticationStatus = .guest
    public var user: User?
    public var guestUsageStats: GuestUsageStats = GuestUsageStats()
    public var shouldPromptLogin: Bool = false
  }
  
  public enum Action: BindableAction, Sendable {
    case signInWithApple(AppleIDCredential)
    case loginSucceeded(User)
    case logout
    // ...
  }
}
```

**功能特性**:
- 游客模式支持
- Apple ID登录集成
- 使用统计跟踪
- 渐进式注册引导

### 2. 认证客户端 (AuthenticationClient)
```swift
@DependencyClient
public struct AuthenticationClient: Sendable {
  public var signInWithApple:
    @Sendable (_ appleIDCredential: AppleIDCredential) async throws -> AuthenticationResponse
  public var logout:
    @Sendable () async throws -> Void
}
```

**支持的认证方式**:
- Apple ID一键登录
- 邮箱密码登录
- 二次验证支持

### 3. AI生成引擎 (AIGenerationCore)
```swift
@Reducer
public struct AIGeneration: Sendable {
  @ObservableState
  public struct State: Equatable {
    public var generationRequest: GenerationRequest?
    public var generationStatus: GenerationStatus = .idle
    public var generatedImages: [GeneratedImage] = []
  }
}
```

**核心功能**:
- 多风格AI生成
- 实时进度跟踪
- 结果预览和保存

## 📱 用户界面设计

### SwiftUI组件架构
```
Views
├── Screens
│   ├── HomeView            # 首页
│   ├── CreationFlowView    # 创作流程
│   ├── GalleryView         # 作品画廊
│   └── ProfileView         # 用户资料
├── Components
│   ├── UserStatusIndicator # 用户状态指示器
│   ├── GuestUsageBanner    # 游客使用横幅
│   ├── AppleSignInButton   # Apple登录按钮
│   └── StyleCard           # 风格卡片
└── Modals
    ├── LoginPromptModal    # 登录提示弹窗
    ├── CreationFlowModal   # 创作流程弹窗
    └── SettingsModal       # 设置弹窗
```

### 设计系统
```swift
// 颜色系统
extension Color {
  static let primaryGradient = LinearGradient(
    colors: [.pink, .purple],
    startPoint: .leading,
    endPoint: .trailing
  )
}

// 字体系统
extension Font {
  static let appTitle = Font.largeTitle.weight(.bold)
  static let cardTitle = Font.headline.weight(.semibold)
  static let bodyText = Font.subheadline
}
```

## 🔄 数据流

### 单向数据流
```
User Action → Store → Reducer → State Update → View Update
     ↑                                              ↓
Effect ← Side Effect ← Business Logic ← State Change
```

### 状态管理
```swift
// 全局应用状态
struct AppState {
  var selectedTab: Tab = .home
  var userState = UserState.State()
  var home = Home.State()
  var photoUpload = PhotoUpload.State()
  var styleSelection = StyleSelection.State()
  var aiGeneration = AIGeneration.State()
  var profile = Profile.State()
}
```

## 🧪 测试策略

### 测试金字塔
```
    ┌─────────────┐
    │  UI Tests   │ ← 端到端测试
    ├─────────────┤
    │Integration  │ ← 集成测试
    │   Tests     │
    ├─────────────┤
    │   Unit      │ ← 单元测试
    │   Tests     │
    └─────────────┘
```

### 测试覆盖
- **单元测试**: 所有Reducer逻辑
- **集成测试**: 模块间交互
- **UI测试**: 关键用户流程

### 测试工具
```swift
// TCA TestStore
let store = TestStore(initialState: UserState.State()) {
  UserState()
}

await store.send(.signInWithApple(mockCredential))
await store.receive(.loginSucceeded(mockUser)) {
  $0.authenticationStatus = .authenticated
  $0.user = mockUser
}
```

## 🚀 性能优化

### 编译时优化
- Swift 6严格并发检查
- 静态类型安全
- 编译时错误检测

### 运行时优化
- 懒加载模块
- 图片缓存策略
- 网络请求优化

### 内存管理
- ARC自动内存管理
- 弱引用避免循环引用
- 及时释放大对象

## 🔐 安全考虑

### 数据安全
- Apple ID安全认证
- 本地数据加密
- 网络传输HTTPS

### 隐私保护
- 最小权限原则
- 用户数据匿名化
- 符合GDPR要求

## 📦 依赖管理

### 主要依赖
```swift
dependencies: [
  .package(url: "https://github.com/pointfreeco/swift-composable-architecture", from: "1.15.0"),
  .package(url: "https://github.com/pointfreeco/swift-dependencies", from: "1.4.0"),
  .package(url: "https://github.com/pointfreeco/swift-navigation", from: "2.2.0"),
  .package(url: "https://github.com/apple/swift-collections", from: "1.1.0"),
]
```

### 版本策略
- 主要版本: 手动更新
- 次要版本: 自动更新
- 补丁版本: 自动更新

## 🔄 CI/CD流程

### 持续集成
```yaml
# GitHub Actions示例
- name: Build and Test
  run: |
    swift build
    swift test
```

### 部署流程
1. 代码提交
2. 自动化测试
3. 代码审查
4. 合并主分支
5. 自动部署

## 📊 监控和分析

### 性能监控
- 应用启动时间
- 内存使用情况
- CPU使用率
- 网络请求延迟

### 用户分析
- 功能使用统计
- 用户行为分析
- 错误报告收集

## 🔮 未来规划

### 短期目标 (1-3个月)
- 完善AI生成算法
- 优化用户体验
- 增加更多风格选择

### 中期目标 (3-6个月)
- 社交功能集成
- 高级编辑工具
- 订阅服务

### 长期目标 (6-12个月)
- 跨平台支持
- AI模型优化
- 国际化支持

---

**本文档版本**: v1.0  
**最后更新**: 2024年12月  
**维护者**: 开发团队
