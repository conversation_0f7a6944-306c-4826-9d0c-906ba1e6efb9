# Xcode Cloud Swift 6.0 宏支持修复

## 🎯 问题描述

在 Xcode Cloud 中使用 Swift 6.0 构建时遇到以下错误：

```
Macro "DependenciesMacrosPlugin" from package "swift-dependencies" must be enabled before it can be used
Macro "ComposableArchitectureMacros" from package "swift-composable-architecture" must be enabled before it can be used
```

## 🔍 根本原因

Swift 6.0 中，宏（Macros）需要显式启用才能使用。在 Package.swift 中需要为使用宏的 targets 添加特定的 `swiftSettings` 配置。

## 🔧 修复方案

### 1. 创建通用的 Swift 设置

在 Package.swift 顶部添加通用的 Swift 6.0 设置：

```swift
// Swift 6.0 settings for macro support
let swiftSettings: [SwiftSetting] = [
  .enableExperimentalFeature("StrictConcurrency"),
  .enableUpcomingFeature("BareSlashRegexLiterals"),
  .enableUpcomingFeature("ConciseMagicFile"),
  .enableUpcomingFeature("ForwardTrailingClosures"),
  .enableUpcomingFeature("ImportObjcForwardDeclarations"),
  .enableUpcomingFeature("DisableOutwardActorInference"),
  .enableUpcomingFeature("ExistentialAny"),
  .enableUpcomingFeature("DeprecateApplicationMain"),
  .enableUpcomingFeature("GlobalConcurrency"),
  .enableUpcomingFeature("IsolatedDefaultValues"),
  .unsafeFlags(["-enable-experimental-feature", "AccessLevelOnImport"]),
]
```

### 2. 为所有使用宏的 targets 添加设置

为以下类型的 targets 添加 `swiftSettings: swiftSettings`：

#### 使用 Dependencies 宏的 targets：
- AuthenticationClient
- LoggingClient
- NetworkClient
- KeychainClient
- UserStorageClient
- PhotoUploadCore
- SubscriptionCore
- StyleSelectionCore
- AIGenerationCore
- QuotaClient
- ImageGenerationClient

#### 使用 ComposableArchitecture 宏的 targets：
- BridalApp (executable)
- LaunchCore
- ImageTypeSelectionCore
- ImageGenerationCore
- ImageViewCore
- AppCore
- MainTabCore
- LoginCore
- ProfileCore
- TwoFactorCore
- UserStateCore
- HomeCore
- QuotaExhaustedSwiftUI
- CommonUI
- StoreKitClientLive

### 3. 示例修改

```swift
// 修改前
.target(
  name: "AuthenticationClient",
  dependencies: [
    "CommonUI",
    "LoggingClient",
    .product(name: "Dependencies", package: "swift-dependencies"),
    .product(name: "DependenciesMacros", package: "swift-dependencies"),
  ]
),

// 修改后
.target(
  name: "AuthenticationClient",
  dependencies: [
    "CommonUI",
    "LoggingClient",
    .product(name: "Dependencies", package: "swift-dependencies"),
    .product(name: "DependenciesMacros", package: "swift-dependencies"),
  ],
  swiftSettings: swiftSettings
),
```

## ✅ 修复结果

- ✅ 本地构建成功
- ✅ 所有宏正确启用
- ✅ Swift 6.0 兼容性完整
- ✅ Xcode Cloud 构建应该成功

## 📊 构建验证

运行以下命令验证修复：

```bash
cd tca-template
swift build
```

构建成功，输出：`Build complete! (13.29s)`

## ⚠️ 注意事项

### 警告信息
构建过程中会出现一些警告，这些都是正常的：

1. **已启用的功能警告**：
   ```
   warning: upcoming feature 'IsolatedDefaultValues' is already enabled as of Swift version 6
   ```
   这些警告表示某些功能在 Swift 6.0 中已经默认启用，不影响构建。

2. **类型注解警告**：
   ```
   warning: use of protocol 'Error' as a type must be written 'any Error'
   ```
   这些是 Swift 6.0 的类型系统改进，建议但不强制修改。

### 性能影响
- 添加的设置主要是启用 Swift 6.0 的新功能和宏支持
- 对构建性能影响最小
- 提高了代码的类型安全性和并发安全性

## 🔄 后续优化建议

1. **清理警告**：逐步修复 `any Error` 类型注解警告
2. **移除冗余设置**：某些 `enableUpcomingFeature` 在 Swift 6.0 中已默认启用，可以移除
3. **监控构建**：关注 Xcode Cloud 构建日志，确保稳定性

## 📝 技术细节

### Swift 6.0 宏系统
- 宏在 Swift 6.0 中需要显式启用
- `DependenciesMacros` 和 `ComposableArchitectureMacros` 是项目的核心宏
- 通过 `swiftSettings` 配置启用宏支持

### 并发安全性
- `StrictConcurrency` 启用严格的并发检查
- `IsolatedDefaultValues` 和 `GlobalConcurrency` 提高并发安全性
- `DisableOutwardActorInference` 防止意外的 actor 推断

---

**修复完成时间**: 2025-07-28  
**修复人员**: AI Assistant  
**影响范围**: 整个项目的 Swift 6.0 兼容性和 Xcode Cloud 构建
