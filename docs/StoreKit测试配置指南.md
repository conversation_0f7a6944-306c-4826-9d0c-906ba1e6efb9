# StoreKit测试配置指南

## 🚨 问题描述
```
❌ [SubscriptionCore Live] 产品未找到: com.wenhaofree.bridal.sub_monthly_40
❌ [SubscriptionCore Live] 购买失败: productNotFound
```

## 🛠️ 解决方案

### 方案1: 使用StoreKit Configuration文件（推荐用于开发测试）

#### 1. 在Xcode中添加StoreKit Configuration文件
1. 在Xcode中，右键点击项目 → New File
2. 选择 "StoreKit Configuration File"
3. 命名为 `Configuration.storekit`
4. 添加到项目中

#### 2. 配置产品
在Configuration.storekit文件中添加以下产品：

**单次购买产品:**
- Product ID: `com.bridal.single.basic`
- Type: Non-Consumable
- Price: ¥1.00
- Display Name: 单次生成
- Description: 1次高清图片生成

**月度订阅:**
- Product ID: `com.wenhaofree.bridal.sub_monthly_40`
- Type: Auto-Renewable Subscription
- Price: ¥28.00
- Duration: 1 Month
- Display Name: 高级月度订阅
- Description: 每月40次生成，解锁所有功能

**年度订阅:**
- Product ID: `com.bridal.yearly`
- Type: Auto-Renewable Subscription
- Price: ¥128.00
- Duration: 1 Year
- Display Name: 年度订阅
- Description: 每年600次生成，享受最大优惠

#### 3. 在Xcode中启用StoreKit Configuration
1. 选择你的Scheme → Edit Scheme
2. 在Run选项卡中，找到StoreKit Configuration
3. 选择你创建的Configuration.storekit文件

### 方案2: App Store Connect配置（用于真实测试）

#### 1. 在App Store Connect中配置产品
1. 登录 [App Store Connect](https://appstoreconnect.apple.com)
2. 选择你的应用
3. 进入 "App内购买项目" 或 "订阅"
4. 添加以下产品ID：
   - `com.bridal.single.basic` (非消耗型项目)
   - `com.wenhaofree.bridal.sub_monthly_40` (自动续期订阅)
   - `com.bridal.yearly` (自动续期订阅)

#### 2. 创建沙盒测试账户
1. 在App Store Connect中，进入 "用户和访问" → "沙盒测试员"
2. 创建测试账户
3. 在设备的设置 → App Store → 沙盒账户中登录测试账户

#### 3. 确保应用已提交审核
- 产品必须与应用一起提交审核才能在沙盒中使用
- 或者应用已经在App Store上架

### 方案3: 使用Mock产品（当前实现）

当前代码已经实现了fallback机制，如果无法加载真实产品，会自动使用模拟产品：

```swift
// 在StoreKitClientLive中
if storeProducts.isEmpty {
  print("⚠️ No products loaded, returning mock products for testing")
  return mockProducts()
}
```

## 🧪 测试步骤

### 使用StoreKit Configuration测试
1. 确保在Xcode中选择了Configuration.storekit文件
2. 运行应用
3. 进入订阅页面
4. 点击购买按钮
5. 会弹出模拟的购买对话框

### 使用沙盒环境测试
1. 确保使用沙盒测试账户
2. 确保产品已在App Store Connect中配置
3. 运行应用
4. 进入订阅页面
5. 点击购买按钮
6. 使用沙盒账户完成购买

## 🔍 调试信息

当前代码会输出详细的调试信息：

```
🛒 Loading products from App Store...
🛒 Environment: SANDBOX
🧪 Using StoreKit Configuration for testing
✅ Loaded 3 products from StoreKit
📦 Product: com.bridal.single.basic - 单次生成 - ¥1.00
📦 Product: com.wenhaofree.bridal.sub_monthly_40 - 高级月度订阅 - ¥28.00
📦 Product: com.bridal.yearly - 年度订阅 - ¥128.00
```

## ⚡ 快速解决方案

如果您想立即测试购买流程，当前代码已经配置为在无法加载真实产品时自动使用模拟产品。这意味着：

1. 应用不会崩溃
2. 订阅页面会显示产品
3. 可以测试购买流程（使用模拟数据）
4. 订阅状态会正确更新

## 🎯 推荐流程

1. **开发阶段**: 使用StoreKit Configuration文件
2. **内部测试**: 使用沙盒环境 + App Store Connect配置
3. **生产环境**: 使用真实的App Store Connect配置

## 📝 注意事项

- StoreKit Configuration只在开发环境中有效
- 沙盒测试需要真实设备，模拟器无法测试购买
- 产品ID必须与App Store Connect中的完全一致
- 订阅产品需要创建订阅组
