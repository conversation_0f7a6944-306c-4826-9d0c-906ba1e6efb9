import Foundation
import ComposableArchitecture
import QuotaClient

// MARK: - Image Generation Client Interface

@DependencyClient
public struct ImageGenerationClient: Sendable {
  /// Generate image with server-side API call and quota validation
  public var generateImage: @Sendable (GenerateImageRequest) async throws -> GeneratedImageResponse = { _ in
    throw ImageGenerationError.notImplemented
  }
  
  /// Check generation status
  public var getGenerationStatus: @Sendable (String) async throws -> GenerationStatusResponse = { _ in
    throw ImageGenerationError.notImplemented
  }
  
  /// Cancel ongoing generation
  public var cancelGeneration: @Sendable (String) async throws -> Bool = { _ in
    throw ImageGenerationError.notImplemented
  }
  
  /// Validate quota before generation
  public var validateQuotaForGeneration: @Sendable () async throws -> QuotaValidationResult = {
    throw ImageGenerationError.notImplemented
  }
}

// MARK: - Request/Response Models

public struct GenerateImageRequest: Codable, Sendable {
  public let templateId: String
  public let templateName: String
  public let imageData: Data
  public let styleType: String
  public let deviceFingerprint: String
  public let requestId: String
  
  public init(
    templateId: String,
    templateName: String,
    imageData: Data,
    styleType: String,
    deviceFingerprint: String = UUID().uuidString,
    requestId: String = UUID().uuidString
  ) {
    self.templateId = templateId
    self.templateName = templateName
    self.imageData = imageData
    self.styleType = styleType
    self.deviceFingerprint = deviceFingerprint
    self.requestId = requestId
  }
}

public struct GeneratedImageResponse: Codable, Sendable {
  public let success: Bool
  public let generationId: String
  public let imageUrls: [String] // Changed to array for multiple images
  public let thumbnailUrls: [String] // Changed to array for multiple thumbnails
  public let remainingQuota: Int
  public let quotaRefreshDate: Date?
  public let processingTime: TimeInterval?
  public let message: String?

  public init(
    success: Bool,
    generationId: String,
    imageUrls: [String] = [],
    thumbnailUrls: [String] = [],
    remainingQuota: Int = 0,
    quotaRefreshDate: Date? = nil,
    processingTime: TimeInterval? = nil,
    message: String? = nil
  ) {
    self.success = success
    self.generationId = generationId
    self.imageUrls = imageUrls
    self.thumbnailUrls = thumbnailUrls
    self.remainingQuota = remainingQuota
    self.quotaRefreshDate = quotaRefreshDate
    self.processingTime = processingTime
    self.message = message
  }
}

public struct GenerationStatusResponse: Codable, Sendable {
  public let generationId: String
  public let status: GenerationServerStatus
  public let progress: Double
  public let imageUrls: [String] // Changed to array for multiple images
  public let thumbnailUrls: [String] // Changed to array for multiple thumbnails
  public let error: String?
  public let estimatedTimeRemaining: TimeInterval?

  public init(
    generationId: String,
    status: GenerationServerStatus,
    progress: Double = 0.0,
    imageUrls: [String] = [],
    thumbnailUrls: [String] = [],
    error: String? = nil,
    estimatedTimeRemaining: TimeInterval? = nil
  ) {
    self.generationId = generationId
    self.status = status
    self.progress = progress
    self.imageUrls = imageUrls
    self.thumbnailUrls = thumbnailUrls
    self.error = error
    self.estimatedTimeRemaining = estimatedTimeRemaining
  }
}

public struct QuotaValidationResult: Codable, Sendable, Equatable {
  public let canGenerate: Bool
  public let remainingQuota: Int
  public let reason: String?
  public let suggestedAction: SuggestedAction?
  
  public init(
    canGenerate: Bool,
    remainingQuota: Int,
    reason: String? = nil,
    suggestedAction: SuggestedAction? = nil
  ) {
    self.canGenerate = canGenerate
    self.remainingQuota = remainingQuota
    self.reason = reason
    self.suggestedAction = suggestedAction
  }
}

public enum SuggestedAction: String, Codable, Sendable {
  case upgrade = "upgrade"
  case login = "login"
  case waitForRefresh = "wait"
  case contactSupport = "support"
  
  public var displayMessage: String {
    switch self {
    case .upgrade:
      return "升级到高级版本以获得更多生成次数"
    case .login:
      return "登录以获得更多生成次数"
    case .waitForRefresh:
      return "等待配额刷新或升级订阅"
    case .contactSupport:
      return "联系客服获取帮助"
    }
  }
}

public enum GenerationServerStatus: String, Codable, Sendable {
  case queued = "queued"
  case processing = "processing"
  case generating = "generating"
  case finalizing = "finalizing"
  case completed = "completed"
  case failed = "failed"
  case cancelled = "cancelled"
  
  public var displayMessage: String {
    switch self {
    case .queued:
      return "排队中..."
    case .processing:
      return "处理中..."
    case .generating:
      return "AI生成中..."
    case .finalizing:
      return "最终处理..."
    case .completed:
      return "生成完成"
    case .failed:
      return "生成失败"
    case .cancelled:
      return "已取消"
    }
  }
  
  public var isTerminal: Bool {
    switch self {
    case .completed, .failed, .cancelled:
      return true
    case .queued, .processing, .generating, .finalizing:
      return false
    }
  }
}

// MARK: - Errors

public enum ImageGenerationError: Error, Equatable, LocalizedError, Sendable {
  case notImplemented
  case quotaExhausted
  case networkError(String)
  case serverError(Int, String?)
  case invalidRequest(String)
  case processingFailed(String)
  case generationNotFound(String)
  case invalidImageData
  case unsupportedFormat
  case imageTooLarge
  case requestTimeout
  case unauthorized
  
  public var errorDescription: String? {
    switch self {
    case .notImplemented:
      return "Feature not implemented"
    case .quotaExhausted:
      return "生成次数已用完，请升级订阅或稍后再试"
    case .networkError(let message):
      return "网络错误：\(message)"
    case .serverError(let code, let message):
      return "服务器错误 (\(code))：\(message ?? "未知错误")"
    case .invalidRequest(let message):
      return "请求无效：\(message)"
    case .processingFailed(let message):
      return "处理失败：\(message)"
    case .generationNotFound(let id):
      return "未找到生成任务：\(id)"
    case .invalidImageData:
      return "图片数据无效"
    case .unsupportedFormat:
      return "不支持的图片格式"
    case .imageTooLarge:
      return "图片文件过大"
    case .requestTimeout:
      return "请求超时，请重试"
    case .unauthorized:
      return "未授权访问，请重新登录"
    }
  }
  
  public var isRetryable: Bool {
    switch self {
    case .networkError, .serverError, .processingFailed, .requestTimeout:
      return true
    case .quotaExhausted, .invalidRequest, .invalidImageData, .unsupportedFormat, .imageTooLarge, .unauthorized, .generationNotFound, .notImplemented:
      return false
    }
  }
}

// MARK: - Dependency Key

extension ImageGenerationClient: DependencyKey {
  public static let liveValue = ImageGenerationClient(
    generateImage: { _ in
      throw ImageGenerationError.notImplemented
    },
    getGenerationStatus: { _ in
      throw ImageGenerationError.notImplemented
    },
    cancelGeneration: { _ in
      throw ImageGenerationError.notImplemented
    },
    validateQuotaForGeneration: {
      throw ImageGenerationError.notImplemented
    }
  )

  // Mock implementation for development and testing
  public static let mockValue = ImageGenerationClient(
    generateImage: { request in
      // Simulate network delay
      try await Task.sleep(for: .seconds(1))

      // Mock successful response with 4 images
      return GeneratedImageResponse(
        success: true,
        generationId: "mock-\(UUID().uuidString)",
        imageUrls: [
          "https://picsum.photos/512/768?random=1",
          "https://picsum.photos/512/768?random=2",
          "https://picsum.photos/512/768?random=3",
          "https://picsum.photos/512/768?random=4"
        ],
        thumbnailUrls: [
          "https://picsum.photos/256/384?random=1",
          "https://picsum.photos/256/384?random=2",
          "https://picsum.photos/256/384?random=3",
          "https://picsum.photos/256/384?random=4"
        ],
        remainingQuota: 39,
        processingTime: 5.0,
        message: "Mock generation completed successfully"
      )
    },
    getGenerationStatus: { id in
      // Simulate processing states
      let progress = Double.random(in: 0.8...1.0)
      let isCompleted = progress >= 0.95

      return GenerationStatusResponse(
        generationId: id,
        status: isCompleted ? .completed : .generating,
        progress: progress,
        imageUrls: isCompleted ? [
          "https://picsum.photos/512/768?random=1",
          "https://picsum.photos/512/768?random=2",
          "https://picsum.photos/512/768?random=3",
          "https://picsum.photos/512/768?random=4"
        ] : [],
        thumbnailUrls: isCompleted ? [
          "https://picsum.photos/256/384?random=1",
          "https://picsum.photos/256/384?random=2",
          "https://picsum.photos/256/384?random=3",
          "https://picsum.photos/256/384?random=4"
        ] : []
      )
    },
    cancelGeneration: { _ in
      return true
    },
    validateQuotaForGeneration: {
      return QuotaValidationResult(
        canGenerate: true,
        remainingQuota: 40,
        reason: nil,
        suggestedAction: nil
      )
    }
  )
  
  public static let testValue = ImageGenerationClient(
    generateImage: { request in
      // Mock successful response for testing - returns 4 images
      return GeneratedImageResponse(
        success: true,
        generationId: "test-\(UUID().uuidString)",
        imageUrls: [
          "https://example.com/generated-image-1.jpg",
          "https://example.com/generated-image-2.jpg",
          "https://example.com/generated-image-3.jpg",
          "https://example.com/generated-image-4.jpg"
        ],
        thumbnailUrls: [
          "https://example.com/thumbnail-1.jpg",
          "https://example.com/thumbnail-2.jpg",
          "https://example.com/thumbnail-3.jpg",
          "https://example.com/thumbnail-4.jpg"
        ],
        remainingQuota: 39,
        processingTime: 5.0,
        message: "Test generation completed"
      )
    },
    getGenerationStatus: { id in
      return GenerationStatusResponse(
        generationId: id,
        status: .completed,
        progress: 1.0,
        imageUrls: [
          "https://example.com/generated-image-1.jpg",
          "https://example.com/generated-image-2.jpg",
          "https://example.com/generated-image-3.jpg",
          "https://example.com/generated-image-4.jpg"
        ],
        thumbnailUrls: [
          "https://example.com/thumbnail-1.jpg",
          "https://example.com/thumbnail-2.jpg",
          "https://example.com/thumbnail-3.jpg",
          "https://example.com/thumbnail-4.jpg"
        ]
      )
    },
    cancelGeneration: { _ in
      return true
    },
    validateQuotaForGeneration: {
      return QuotaValidationResult(
        canGenerate: true,
        remainingQuota: 40,
        reason: nil,
        suggestedAction: nil
      )
    }
  )
}

extension DependencyValues {
  public var imageGenerationClient: ImageGenerationClient {
    get { self[ImageGenerationClient.self] }
    set { self[ImageGenerationClient.self] = newValue }
  }
}