import Foundation
import ComposableArchitecture

// MARK: - Quota Client Interface

@DependencyClient
public struct QuotaClient: Sendable {
  /// Get current user's quota information
  public var getCurrentQuota: @Sendable () async throws -> QuotaInfo = { 
    throw ClientError.notImplemented 
  }
  
  /// Refresh quota status from server
  public var refreshQuota: @Sendable () async throws -> QuotaInfo = { 
    throw ClientError.notImplemented 
  }
  
  /// Check if user can generate images
  public var canGenerate: @Sendable () async throws -> Bool = { 
    throw ClientError.notImplemented 
  }
  
  /// Consume one generation quota
  public var consumeGeneration: @Sendable () async throws -> QuotaInfo = { 
    throw ClientError.notImplemented 
  }
  
  /// Get quota status for specific user type
  public var getQuotaForUser: @Sendable (UserType) async throws -> QuotaInfo = { _ in
    throw ClientError.notImplemented 
  }
}

// MARK: - Supporting Types

public struct QuotaInfo: Equatable, Sendable, Codable {
  public let userType: UserType
  public let remainingQuota: Int
  public let totalQuota: Int
  public let quotaPeriod: QuotaPeriod
  public let refreshDate: Date?
  public let deviceId: String?
  
  public init(
    userType: UserType,
    remainingQuota: Int,
    totalQuota: Int,
    quotaPeriod: QuotaPeriod = .monthly,
    refreshDate: Date? = nil,
    deviceId: String? = nil
  ) {
    self.userType = userType
    self.remainingQuota = remainingQuota
    self.totalQuota = totalQuota
    self.quotaPeriod = quotaPeriod
    self.refreshDate = refreshDate
    self.deviceId = deviceId
  }
  
  public var canGenerate: Bool {
    return remainingQuota > 0
  }
  
  public var usagePercentage: Double {
    guard totalQuota > 0 else { return 0.0 }
    return Double(totalQuota - remainingQuota) / Double(totalQuota)
  }
  
  public var shouldPromptUpgrade: Bool {
    switch userType {
    case .guest:
      return remainingQuota <= 1
    case .subscriber:
      return remainingQuota <= 3
    }
  }
}

public enum UserType: String, Equatable, Sendable, Codable {
  case guest = "guest"
  case subscriber = "subscriber"
  
  public var defaultQuota: Int {
    switch self {
    case .guest: return 1
    case .subscriber: return 40
    }
  }
}

public enum QuotaPeriod: String, Equatable, Sendable, Codable {
  case oneTime = "one_time"
  case monthly = "monthly" 
  case yearly = "yearly"
  
  public var displayName: String {
    switch self {
    case .oneTime: return "一次性"
    case .monthly: return "每月"
    case .yearly: return "每年"
    }
  }
}

public enum ClientError: Error, Equatable, LocalizedError {
  case notImplemented
  case networkError(String)
  case quotaExhausted
  case invalidToken
  case serverError(Int)
  case deviceNotFound
  
  public var errorDescription: String? {
    switch self {
    case .notImplemented:
      return "Client not implemented"
    case .networkError(let message):
      return "Network error: \(message)"
    case .quotaExhausted:
      return "Generation quota exhausted"
    case .invalidToken:
      return "Invalid or expired token"
    case .serverError(let code):
      return "Server error: \(code)"
    case .deviceNotFound:
      return "Device not found"
    }
  }
}

// MARK: - Dependency Key

extension QuotaClient: DependencyKey {
  public static let liveValue = QuotaClient(
    getCurrentQuota: {
      throw ClientError.notImplemented
    },
    refreshQuota: {
      throw ClientError.notImplemented
    },
    canGenerate: {
      throw ClientError.notImplemented
    },
    consumeGeneration: {
      throw ClientError.notImplemented
    },
    getQuotaForUser: { _ in
      throw ClientError.notImplemented
    }
  )
  
  public static let testValue = QuotaClient()
}

extension DependencyValues {
  public var quotaClient: QuotaClient {
    get { self[QuotaClient.self] }
    set { self[QuotaClient.self] = newValue }
  }
}