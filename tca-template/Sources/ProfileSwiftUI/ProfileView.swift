import ComposableArchitecture
import CommonUI
import ProfileCore
import UserStateCore
import NotificationSettingsSwiftUI
import NotificationSettingsCore
import SwiftUI
#if canImport(UIKit)
import UIKit
#endif

public struct ProfileView: View {
  @Perception.Bindable var store: StoreOf<ProfileCore.Profile>

  public init(store: StoreOf<ProfileCore.Profile>) {
    self.store = store
  }

  public var body: some View {
    WithPerceptionTracking {
      SettingsView(store: store)
      .navigationTitle("设置")
      #if os(iOS)
      .navigationBarTitleDisplayMode(.large)
      #endif
      .onAppear {
        store.send(ProfileCore.Profile.Action.onAppear)
      }
      .sheet(isPresented: Binding(
        get: { store.notificationSettings != nil },
        set: { isPresented in
          if !isPresented {
            store.send(ProfileCore.Profile.Action.dismissNotificationSettings)
          }
        }
      )) {
        if let notificationStore = store.scope(state: \.notificationSettings, action: \.notificationSettings) {
          NavigationView {
            NotificationSettingsView(store: notificationStore)
              .toolbar {
                ToolbarItem(placement: {
                  #if os(iOS)
                  return .navigationBarTrailing
                  #else
                  return .automatic
                  #endif
                }()) {
                  Button("完成") {
                    store.send(ProfileCore.Profile.Action.dismissNotificationSettings)
                  }
                }
              }
          }
        }
      }
      .alert(
        "删除账号",
        isPresented: Binding(
          get: { store.isShowingDeleteAccountAlert },
          set: { _ in store.send(ProfileCore.Profile.Action.cancelDeleteAccount) }
        )
      ) {
        Button("取消", role: .cancel) {
          store.send(ProfileCore.Profile.Action.cancelDeleteAccount)
        }

        Button("确认删除", role: .destructive) {
          store.send(ProfileCore.Profile.Action.confirmDeleteAccount)
        }
      } message: {
        Text("删除账号后，您的所有数据将被永久删除且无法恢复。确定要继续吗？")
      }
      .alert(
        "账号删除成功",
        isPresented: Binding(
          get: { store.isShowingDeleteSuccessAlert },
          set: { _ in store.send(ProfileCore.Profile.Action.dismissDeleteSuccessAlert) }
        )
      ) {
        Button("确定") {
          store.send(ProfileCore.Profile.Action.dismissDeleteSuccessAlert)
        }
      } message: {
        Text("您的账号已成功删除，感谢您使用我们的服务。")
      }
      .alert(
        "订阅成功！",
        isPresented: Binding(
          get: { store.isShowingSubscriptionSuccessAlert },
          set: { _ in store.send(ProfileCore.Profile.Action.dismissSubscriptionSuccessAlert) }
        )
      ) {
        Button("太棒了！") {
          store.send(ProfileCore.Profile.Action.dismissSubscriptionSuccessAlert)
        }
      } message: {
        Text("🎉 恭喜您成功升级到Pro版本！\n现在可以享受所有高级功能了。")
      }
      .alert(
        "支付失败",
        isPresented: Binding(
          get: { store.error != nil },
          set: { _ in store.send(ProfileCore.Profile.Action.clearError) }
        )
      ) {
        Button("确定") {
          store.send(ProfileCore.Profile.Action.clearError)
        }
        Button("重试") {
          store.send(ProfileCore.Profile.Action.upgradeSubscription)
        }
      } message: {
        Text(store.error?.localizedDescription ?? "支付过程中发生错误，请重试。")
      }
    }
  }

}

// MARK: - Settings View

struct SettingsView: View {
  @Perception.Bindable var store: StoreOf<ProfileCore.Profile>

  var body: some View {
    WithPerceptionTracking {
      ScrollView {
        LazyVStack(spacing: 20) {
          // 用户信息卡片
          userInfoCard

          // 账户状态卡片
          accountStatusCard

          // Pro订阅推广卡片 (如果是免费用户)
          if store.user?.subscriptionStatus == .free {
            proUpgradeCard
          }
          
          // 应用设置
          appSettingsSection
          
          // 关于应用
          aboutSection
          
          // 账户操作
          accountActionsSection
          
          // 底部间距
          Spacer(minLength: 100)
        }
        .padding(.horizontal, 16)
        .padding(.top, 20)
      }
      .background(Color.gray.opacity(0.1))
    }
  }

  // MARK: - 用户信息卡片
  
  @ViewBuilder
  private var userInfoCard: some View {
    VStack(spacing: 16) {
      // 用户头像和基本信息
      HStack(spacing: 16) {
        // 头像
        ZStack {
          Circle()
            .fill(LinearGradient(
              colors: [Color.pink.opacity(0.3), Color.purple.opacity(0.3)],
              startPoint: .topLeading,
              endPoint: .bottomTrailing
            ))
            .frame(width: 80, height: 80)
          
          if let user = store.user {
            Text(user.initials)
              .font(.system(size: 32, weight: .semibold))
              .foregroundColor(.primary)
          } else {
            Image(systemName: "person.fill")
              .font(.system(size: 32))
              .foregroundColor(.secondary)
          }
        }
        
        // 用户信息
        VStack(alignment: .leading, spacing: 4) {
          Text(store.displayName)
            .font(.system(size: 20, weight: .semibold))
            .foregroundColor(.primary)
          
          if let user = store.user {
            Text(user.email)
              .font(.system(size: 14))
              .foregroundColor(.secondary)
            
            Text("加入于 \(formatDate(user.createdAt))")
              .font(.system(size: 12))
              .foregroundColor(.secondary)
          }
        }
        
        Spacer()
      }
    }
    .padding(20)
    .background(Color.white)
    .cornerRadius(16)
  }
  
  // MARK: - 账户状态卡片
  
  @ViewBuilder
  private var accountStatusCard: some View {
    WithPerceptionTracking {
      VStack(spacing: 16) {
      // 卡片标题
      HStack {
        Text("账户状态")
          .font(.system(size: 18, weight: .semibold))
          .foregroundColor(.primary)
        Spacer()
      }
      
      // 登录状态
      HStack(spacing: 12) {
        ZStack {
          Circle()
            .fill(Color.green)
            .frame(width: 32, height: 32)
          
          Image(systemName: "checkmark")
            .font(.system(size: 14, weight: .bold))
            .foregroundColor(.white)
        }
        
        VStack(alignment: .leading, spacing: 2) {
          Text("登录状态")
            .font(.system(size: 16, weight: .medium))
            .foregroundColor(.primary)
          
          Text("已登录")
            .font(.system(size: 14))
            .foregroundColor(.green)
        }
        
        Spacer()
      }
      
      Divider()
      
      // 订阅状态
      HStack(spacing: 12) {
        ZStack {
          Circle()
            .fill(subscriptionStatusColor)
            .frame(width: 32, height: 32)
          
          Image(systemName: subscriptionStatusIcon)
            .font(.system(size: 14, weight: .bold))
            .foregroundColor(.white)
        }
        
        VStack(alignment: .leading, spacing: 2) {
          Text("订阅状态")
            .font(.system(size: 16, weight: .medium))
            .foregroundColor(.primary)
          
          Text(subscriptionStatusText)
            .font(.system(size: 14))
            .foregroundColor(subscriptionStatusColor)
            .onAppear {
              print("🔍 ProfileSwiftUI: 当前订阅状态 = \(store.user?.subscriptionStatus.displayName ?? "nil")")
            }
            .onChange(of: store.user?.subscriptionStatus) { newValue in
              print("🔄 ProfileSwiftUI: 订阅状态发生变化")
              print("   新状态: \(newValue?.displayName ?? "nil")")
            }
        }
        
        Spacer()
        
        if store.user?.subscriptionStatus == .free {
          Button(action: {
            store.send(ProfileCore.Profile.Action.upgradeSubscription)
          }) {
            HStack(spacing: 4) {
              if store.isLoading {
                ProgressView()
                  .scaleEffect(0.8)
                  .progressViewStyle(CircularProgressViewStyle(tint: .white))
              }
              Text(store.isLoading ? "处理中..." : "升级")
            }
          }
          .font(.system(size: 14, weight: .semibold))
          .foregroundColor(.white)
          .padding(.horizontal, 16)
          .padding(.vertical, 8)
          .background(store.isLoading ? Color.gray : Color.blue)
          .cornerRadius(8)
          .disabled(store.isLoading)
        }
      }
      }
    }
    .padding(20)
    .background(Color.white)
    .cornerRadius(16)
    }
  
  // MARK: - Pro升级推广卡片
  
  @ViewBuilder
  private var proUpgradeCard: some View {
    VStack(spacing: 16) {
      // 卡片标题和图标
      HStack {
        ZStack {
          Circle()
            .fill(LinearGradient(
              colors: [Color.purple, Color.pink],
              startPoint: .topLeading,
              endPoint: .bottomTrailing
            ))
            .frame(width: 40, height: 40)
          
          Image(systemName: "crown.fill")
            .font(.system(size: 20, weight: .bold))
            .foregroundColor(.white)
        }
        
        VStack(alignment: .leading, spacing: 4) {
          Text("升级到 Pro 版本")
            .font(.system(size: 18, weight: .bold))
            .foregroundColor(.primary)
          
          Text("解锁所有高级模板和功能")
            .font(.system(size: 14))
            .foregroundColor(.secondary)
        }
        
        Spacer()
      }
      
      // 功能列表
      VStack(alignment: .leading, spacing: 8) {
        ProFeatureRow(icon: "sparkles", text: "所有高级婚纱照模板")
        ProFeatureRow(icon: "wand.and.rays", text: "AI增强处理效果")
        ProFeatureRow(icon: "photo.stack", text: "无限制照片处理")
        ProFeatureRow(icon: "icloud.and.arrow.up", text: "云端存储同步")
      }
      
      // 升级按钮
      Button(action: {
        store.send(ProfileCore.Profile.Action.upgradeSubscription)
      }) {
        HStack {
          if store.isLoading {
            ProgressView()
              .scaleEffect(0.9)
              .progressViewStyle(CircularProgressViewStyle(tint: .white))
          } else {
            Image(systemName: "crown.fill")
              .font(.system(size: 16, weight: .semibold))
          }

          Text(store.isLoading ? "正在升级..." : "立即升级")
            .font(.system(size: 18, weight: .bold))

          Spacer()

          if !store.isLoading {
            Image(systemName: "arrow.right")
              .font(.system(size: 14, weight: .semibold))
          }
        }
        .foregroundColor(.white)
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(
          LinearGradient(
            colors: store.isLoading ? [Color.gray, Color.gray.opacity(0.8)] : [Color.purple, Color.pink],
            startPoint: .leading,
            endPoint: .trailing
          )
        )
        .cornerRadius(12)
      }
      .disabled(store.isLoading)
    }
    .padding(20)
    .background(Color.white)
    .cornerRadius(16)
    .overlay(
      RoundedRectangle(cornerRadius: 16)
        .stroke(
          LinearGradient(
            colors: [Color.purple.opacity(0.3), Color.pink.opacity(0.3)],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
          ),
          lineWidth: 2
        )
    )
  }

  // MARK: - 应用设置
  
  @ViewBuilder
  private var appSettingsSection: some View {
    VStack(spacing: 0) {
      // 标题
      HStack {
        Text("应用设置")
          .font(.system(size: 18, weight: .semibold))
          .foregroundColor(.primary)
        Spacer()
      }
      .padding(.bottom, 12)
      
      // 设置项
      VStack(spacing: 0) {
        SettingsActionRow(
          icon: "bell.fill",
          iconColor: .orange,
          title: "通知设置",
          action: {
            print("🔔 通知设置按钮被点击")
            store.send(ProfileCore.Profile.Action.showNotificationSettings)
          }
        )
        
        SettingsDivider()
        
        SettingsActionRow(
          icon: "lock.fill",
          iconColor: .blue,
          title: "隐私设置",
          action: { store.send(ProfileCore.Profile.Action.showPrivacySettings) }
        )
        
        SettingsDivider()
        
        SettingsActionRow(
          icon: "photo.fill",
          iconColor: .green,
          title: "照片质量",
          action: { store.send(ProfileCore.Profile.Action.showPhotoQualitySettings) }
        )
        
        SettingsDivider()
        
        SettingsActionRow(
          icon: "icloud.fill",
          iconColor: .blue,
          title: "云同步",
          action: { store.send(ProfileCore.Profile.Action.showCloudSyncSettings) }
        )
      }
      .background(Color.white)
      .cornerRadius(16)
    }
  }

  // MARK: - 关于应用
  
  @ViewBuilder
  private var aboutSection: some View {
    VStack(spacing: 0) {
      // 标题
      HStack {
        Text("关于应用")
          .font(.system(size: 18, weight: .semibold))
          .foregroundColor(.primary)
        Spacer()
      }
      .padding(.bottom, 12)
      
      // 关于项
      VStack(spacing: 0) {
        SettingsVersionRow(
          icon: "info.circle.fill",
          iconColor: .blue,
          title: "版本信息",
          version: "v1.0.0",
          action: { store.send(ProfileCore.Profile.Action.showVersionInfo) }
        )
        
        SettingsDivider()
        
        SettingsActionRow(
          icon: "star.fill",
          iconColor: .yellow,
          title: "评价应用",
          action: { store.send(ProfileCore.Profile.Action.showRating) }
        )
        
        SettingsDivider()
        
        SettingsActionRow(
          icon: "square.and.arrow.up",
          iconColor: .green,
          title: "分享应用",
          action: { store.send(ProfileCore.Profile.Action.shareApp) }
        )
        
        SettingsDivider()
        
        SettingsActionRow(
          icon: "questionmark.circle.fill",
          iconColor: .orange,
          title: "帮助中心",
          action: { store.send(ProfileCore.Profile.Action.showHelp) }
        )
        
        SettingsDivider()
        
        SettingsActionRow(
          icon: "exclamationmark.bubble.fill",
          iconColor: .red,
          title: "反馈问题",
          action: { store.send(ProfileCore.Profile.Action.showFeedback) }
        )
      }
      .background(Color.white)
      .cornerRadius(16)
    }
  }
  
  // MARK: - 账户操作
  
  @ViewBuilder
  private var accountActionsSection: some View {
    VStack(spacing: 16) {
      // 退出登录按钮
      Button(action: { store.send(ProfileCore.Profile.Action.logoutButtonTapped) }) {
        HStack {
          Image(systemName: "rectangle.portrait.and.arrow.right")
            .font(.system(size: 16, weight: .medium))
          
          Text("退出登录")
            .font(.system(size: 18, weight: .semibold))
          
          Spacer()
        }
        .foregroundColor(.white)
        .padding(.vertical, 16)
        .padding(.horizontal, 20)
        .background(Color.black)
        .cornerRadius(12)
      }
      .disabled(store.isLoading)
      
      // 删除账户按钮
      Button(action: { store.send(ProfileCore.Profile.Action.deleteAccountButtonTapped) }) {
        HStack {
          Image(systemName: "trash")
            .font(.system(size: 14, weight: .medium))
          
          Text("删除账户")
            .font(.system(size: 16, weight: .medium))
        }
        .foregroundColor(.red)
      }
      .disabled(store.isLoading)
    }
  }
}

extension SettingsView {
  // MARK: - 计算属性
  
  private var subscriptionStatusColor: Color {
    switch store.user?.subscriptionStatus {
    case .premium:
      return .purple  // 改为更明显的紫色
    case .free, .expired:
      return .gray
    case .none:
      return .gray
    }
  }
  
  private var subscriptionStatusIcon: String {
    switch store.user?.subscriptionStatus {
    case .premium:
      return "crown.fill"
    case .free, .expired:
      return "person.crop.circle"
    case .none:
      return "person.crop.circle"
    }
  }
  
  private var subscriptionStatusText: String {
    switch store.user?.subscriptionStatus {
    case .premium(let expiryDate, _):
      let formatter = DateFormatter()
      formatter.dateStyle = .medium
      formatter.locale = Locale(identifier: "zh_CN")
      return "高级版 Pro (到期: \(formatter.string(from: expiryDate)))"
    case .free:
      return "免费版"
    case .expired:
      return "订阅已过期"
    case .none:
      return "未知"
    }
  }
  
  // MARK: - 辅助函数
  
  private func formatDate(_ date: Date) -> String {
    let formatter = DateFormatter()
    formatter.dateStyle = .medium
    formatter.locale = Locale(identifier: "zh_CN")
    return formatter.string(from: date)
  }
}

// MARK: - Settings Components

struct SettingsActionRow: View {
  let icon: String
  let iconColor: Color
  let title: String
  let action: () -> Void

  var body: some View {
    Button(action: {
      print("🎯 SettingsActionRow 按钮被点击: \(title)")
      action()
    }) {
      HStack(spacing: 12) {
        // Icon
        ZStack {
          RoundedRectangle(cornerRadius: 6)
            .fill(iconColor)
            .frame(width: 28, height: 28)

          Image(systemName: icon)
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(.white)
        }

        // Title
        Text(title)
          .font(.system(size: 16, weight: .medium))
          .foregroundColor(.primary)

        Spacer()

        // Arrow
        Image(systemName: "chevron.right")
          .font(.system(size: 12, weight: .medium))
          .foregroundColor(.secondary)
      }
      .padding(.horizontal, 16)
      .padding(.vertical, 12)
    }
    .buttonStyle(PlainButtonStyle())
  }
}

struct SettingsVersionRow: View {
  let icon: String
  let iconColor: Color
  let title: String
  let version: String
  let action: () -> Void

  var body: some View {
    Button(action: action) {
      HStack(spacing: 12) {
        // Icon
        ZStack {
          RoundedRectangle(cornerRadius: 6)
            .fill(iconColor)
            .frame(width: 28, height: 28)

          Image(systemName: icon)
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(.white)
        }

        // Title
        Text(title)
          .font(.system(size: 16, weight: .medium))
          .foregroundColor(.primary)

        Spacer()

        // Version
        Text(version)
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(.secondary)
      }
      .padding(.horizontal, 16)
      .padding(.vertical, 12)
    }
    .buttonStyle(PlainButtonStyle())
  }
}

struct SettingsDivider: View {
  var body: some View {
    Divider()
      .padding(.leading, 56) // Align with text after icon
  }
}

// MARK: - Pro Feature Row Component

struct ProFeatureRow: View {
  let icon: String
  let text: String
  
  var body: some View {
    HStack(spacing: 12) {
      Image(systemName: icon)
        .font(.system(size: 14, weight: .medium))
        .foregroundColor(.purple)
        .frame(width: 16)
      
      Text(text)
        .font(.system(size: 14, weight: .medium))
        .foregroundColor(.primary)
      
      Spacer()
    }
  }
}








// MARK: - Previews

#Preview("Settings View") {
  NavigationView {
    ProfileView(
      store: Store(initialState: ProfileCore.Profile.State()) {
        ProfileCore.Profile()
      }
    )
  }
}






