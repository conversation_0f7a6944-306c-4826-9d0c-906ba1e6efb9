import Foundation
import StoreKit
import ComposableArchitecture
import SubscriptionCore
import UserStateCore

// MARK: - Development Environment Support

/// 检查是否在使用StoreKit Configuration文件
private func isUsingStoreKitConfiguration() -> Bool {
  // 方法1: 检查Bundle中是否存在.storekit文件
  let hasConfigFile = Bundle.main.url(forResource: "Configuration", withExtension: "storekit") != nil
  print("🔍 Configuration.storekit file found in bundle: \(hasConfigFile)")

  // 方法2: 检查是否在模拟器环境中（StoreKit Configuration通常在模拟器中使用）
  #if targetEnvironment(simulator)
  let isSimulator = true
  #else
  let isSimulator = false
  #endif
  print("🔍 Running on simulator: \(isSimulator)")

  // 方法3: 检查环境变量
  let hasStoreKitEnv = ProcessInfo.processInfo.environment["STOREKIT_CONFIG"] != nil
  print("🔍 STOREKIT_CONFIG environment variable: \(hasStoreKitEnv)")

  return hasConfigFile || (isSimulator && hasConfigFile)
}

/// 从StoreKit加载产品（支持Configuration文件）
private func loadProductsFromStoreKit() async throws -> [SubscriptionProduct] {
  print("🧪 [StoreKitClient] Loading products from StoreKit Configuration...")
  print("🧪 [StoreKitClient] Requesting product IDs: \(LiveStoreKitClient.productIDs)")

  do {
    let storeProducts = try await Product.products(for: LiveStoreKitClient.productIDs)
    print("✅ [StoreKitClient] Loaded \(storeProducts.count) products from StoreKit")

    // 详细列出每个产品
    for product in storeProducts {
      print("📦 [StoreKitClient] Found product: \(product.id) - \(product.displayName) - \(product.displayPrice)")
    }

    // 检查哪些产品ID没有找到
    let foundProductIDs = Set(storeProducts.map { $0.id })
    let missingProductIDs = LiveStoreKitClient.productIDs.subtracting(foundProductIDs)
    if !missingProductIDs.isEmpty {
      print("⚠️ [StoreKitClient] Missing product IDs: \(missingProductIDs)")
    }

    if storeProducts.isEmpty {
      print("⚠️ [StoreKitClient] No products found in StoreKit Configuration, using mock products")
      return mockProducts()
    }

    let subscriptionProducts = storeProducts.map { product in
    let (productType, duration, usageLimits) = mapProductToDetails(product)
    print("📦 [StoreKitClient] Product: \(product.id) - \(product.displayName) - \(product.displayPrice)")

    return SubscriptionProduct(
      id: product.id,
      displayName: product.displayName,
      description: product.description,
      price: product.displayPrice,
      priceValue: product.price,
      currencyCode: product.priceFormatStyle.currencyCode,
      productType: productType,
      duration: duration,
      usageLimits: usageLimits,
      isPopular: product.id == "com.wenhaofree.bridal.sub_monthly_40"
    )
    }

    return subscriptionProducts
  } catch {
    print("❌ [StoreKitClient] Error loading products from StoreKit: \(error)")
    print("🔄 [StoreKitClient] Falling back to mock products")
    return mockProducts()
  }
}

// MARK: - Live StoreKit 2 Implementation

@available(iOS 15.0, macOS 12.0, *)
public struct LiveStoreKitClient {
  
  // MARK: - Product IDs

  static let productIDs: Set<String> = [
    "com.wenhaofree.bridal.single_basic",
    "com.wenhaofree.bridal.sub_monthly_40",
    "com.wenhaofree.bridal.sub_yearly_600"
  ]
  
  // MARK: - Live Implementation
  
  public static let live = StoreKitClient(
    loadProducts: {
      print("🛒 Loading products from App Store...")
      print("🛒 Environment: \(isRunningInSandbox() ? "SANDBOX" : "PRODUCTION")")

      // 检查是否在开发环境中使用StoreKit Configuration
      let hasStoreKitConfig = isUsingStoreKitConfiguration()
      print("🧪 StoreKit Configuration detected: \(hasStoreKitConfig)")

      if hasStoreKitConfig {
        print("🧪 Using StoreKit Configuration for testing")
        return try await loadProductsFromStoreKit()
      }

      do {
        let storeProducts = try await Product.products(for: productIDs)
        print("✅ Loaded \(storeProducts.count) products from App Store")
        
        // 验证产品配置
        for productID in productIDs {
          if !storeProducts.contains(where: { $0.id == productID }) {
            print("⚠️ Product ID '\(productID)' not found in App Store Connect")
          }
        }
        
        let subscriptionProducts = storeProducts.map { product in
          let (productType, duration, usageLimits) = mapProductToDetails(product)
          print("📦 Product: \(product.id) - \(product.displayName) - \(product.displayPrice)")
          return SubscriptionProduct(
            id: product.id,
            displayName: product.displayName,
            description: product.description,
            price: product.displayPrice,
            priceValue: product.price,
            currencyCode: product.priceFormatStyle.currencyCode,
            productType: productType,
            duration: duration,
            usageLimits: usageLimits,
            isPopular: product.id == "com.wenhaofree.bridal.sub_monthly_40"
          )
        }
        
        if subscriptionProducts.isEmpty {
          print("⚠️ No products loaded, returning mock products for testing")
          return mockProducts()
        }
        
        return subscriptionProducts
        
      } catch {
        print("❌ Failed to load products: \(error)")
        print("❌ Error details: \(error.localizedDescription)")
        
        // 无论在什么环境，都返回模拟产品以确保应用可以正常使用
        print("🔄 Fallback to mock products for development/testing")
        return mockProducts()
      }
    },
    
    purchase: { subscriptionProduct in
      print("💳 Starting purchase for: \(subscriptionProduct.displayName)")
      print("💳 Product ID: \(subscriptionProduct.id)")
      print("💳 Environment: \(isRunningInSandbox() ? "SANDBOX" : "PRODUCTION")")

      do {
        // 查找对应的 StoreKit Product
        print("🔍 Looking for product with ID: \(subscriptionProduct.id)")
        let storeProducts = try await Product.products(for: [subscriptionProduct.id])
        print("🔍 Found \(storeProducts.count) products from App Store")

        guard let product = storeProducts.first else {
          print("❌ Product not found in App Store Connect")
          print("❌ Available products: \(storeProducts.map { $0.id })")
          print("❌ Requested product: \(subscriptionProduct.id)")
          
          // 在沙盒环境中，如果产品未找到，给出更详细的说明
          if isRunningInSandbox() {
            print("🧪 SANDBOX: 请确保产品已在App Store Connect中配置并审核通过")
            print("🧪 SANDBOX: 当前配置的产品IDs: \(productIDs)")
          }
          
          throw StoreKitError.productNotFound
        }

        print("✅ Found product: \(product.displayName) - \(product.displayPrice)")
        print("💰 Price: \(product.price) \(product.priceFormatStyle.currencyCode)")

        // 执行购买
        print("💳 Initiating purchase...")
        let result = try await product.purchase()
        print("💳 Purchase result received")

        switch result {
        case .success(let verification):
          print("✅ Purchase successful!")
          print("🔐 Verifying transaction...")

          // 验证交易
          let transaction = try checkVerified(verification)
          print("✅ Transaction verified: \(transaction.id)")

          // 完成交易
          await transaction.finish()
          print("✅ Transaction finished")

          // 计算过期日期
          if let duration = subscriptionProduct.duration {
            let expiryDate = calculateExpiryDate(for: subscriptionProduct)
            print("🎉 Subscription activated until: \(expiryDate)")
            return .premium(expiryDate: expiryDate, usageLimits: subscriptionProduct.usageLimits)
          } else {
            // Single purchase - return current premium status with short expiry for processing
            let expiryDate = Date().addingTimeInterval(24 * 60 * 60) // 1 day for processing
            print("🎉 Single purchase completed")
            return .premium(expiryDate: expiryDate, usageLimits: subscriptionProduct.usageLimits)
          }

        case .userCancelled:
          print("❌ User cancelled purchase")
          throw StoreKitError.userCancelled

        case .pending:
          print("⏳ Purchase pending approval (family sharing or parental controls)")
          if isRunningInSandbox() {
            print("🧪 SANDBOX: 在沙盒环境中，pending状态通常表示需要家庭共享批准")
          }
          throw StoreKitError.purchasePending

        @unknown default:
          print("❓ Unknown purchase result")
          throw StoreKitError.unknown
        }

      } catch let error as StoreKitError {
        print("❌ StoreKit error: \(error)")
        print("❌ Error description: \(error.localizedDescription)")
        throw error
      } catch {
        print("❌ Purchase failed with error: \(error)")
        print("❌ Error type: \(type(of: error))")
        print("❌ Error description: \(error.localizedDescription)")

        // 在沙盒环境中提供更详细的错误信息
        if isRunningInSandbox() {
          print("🧪 SANDBOX ERROR DETAILS:")
          print("🧪 - 确保使用沙盒测试账号登录")
          print("🧪 - 确保产品已在App Store Connect配置")
          print("🧪 - 确保测试账号在有效地区")
          print("🧪 - 尝试重新登录沙盒账号")
          
          // 在沙盒环境中，某些错误可以模拟成功用于测试
          if shouldMockSuccessInSandbox(error: error) {
            print("🧪 SANDBOX: 模拟成功购买用于测试")
            if let duration = subscriptionProduct.duration {
              let expiryDate = calculateExpiryDate(for: subscriptionProduct)
              return .premium(expiryDate: expiryDate, usageLimits: subscriptionProduct.usageLimits)
            } else {
              let expiryDate = Date().addingTimeInterval(24 * 60 * 60)
              return .premium(expiryDate: expiryDate, usageLimits: subscriptionProduct.usageLimits)
            }
          }
        }
        
        throw error
      }
    },
    
    restorePurchases: {
      print("🔄 Restoring purchases...")
      
      do {
        // 同步最新的交易状态
        try await AppStore.sync()
        
        // 检查当前的订阅状态
        let currentEntitlements = await Transaction.currentEntitlements
        
        for await verification in currentEntitlements {
          let transaction = try checkVerified(verification)
          
          if let expirationDate = transaction.expirationDate,
             expirationDate > Date() {
            print("✅ Found active subscription, expires: \(expirationDate)")
            // For restored purchases, we'll use yearly limits as default since we can't determine the exact product
            return .premium(expiryDate: expirationDate, usageLimits: UserStateCore.UsageLimits.yearlyStandard)
          }
        }
        
        print("ℹ️ No active subscriptions found")
        return .free
        
      } catch {
        print("❌ Restore failed: \(error)")
        throw error
      }
    },
    
    checkSubscriptionStatus: {
      print("🔍 Checking subscription status...")
      
      // 检查当前的订阅状态
      let currentEntitlements = await Transaction.currentEntitlements
      
      for await verification in currentEntitlements {
        do {
          let transaction = try checkVerified(verification)
          
          if let expirationDate = transaction.expirationDate,
             expirationDate > Date() {
            print("✅ Active subscription found, expires: \(expirationDate)")
            // For status checks, we'll use yearly limits as default since we can't determine the exact product
            return .premium(expiryDate: expirationDate, usageLimits: UserStateCore.UsageLimits.yearlyStandard)
          }
        } catch {
          print("⚠️ Failed to verify transaction: \(error)")
        }
      }
      
      print("ℹ️ No active subscription")
      return .free
    }
  )
}

// MARK: - Helper Functions

@available(iOS 15.0, macOS 12.0, *)
private func checkVerified<T>(_ result: VerificationResult<T>) throws -> T {
  switch result {
  case .unverified:
    throw StoreKitError.failedVerification
  case .verified(let safe):
    return safe
  }
}

private func isRunningInSandbox() -> Bool {
  // 检测是否在沙盒环境中运行
  #if DEBUG
  return true
  #else
  // 在生产环境中，可以通过检查receipt来判断
  guard let receiptURL = Bundle.main.appStoreReceiptURL else { return false }
  return receiptURL.lastPathComponent == "sandboxReceipt"
  #endif
}

private func shouldMockSuccessInSandbox(error: Error) -> Bool {
  // 在沙盒环境中，对于某些特定错误，我们可以模拟成功来帮助开发测试
  // 只有在开发者明确启用时才这样做
  let enableMockSuccess = ProcessInfo.processInfo.environment["MOCK_PURCHASE_SUCCESS"] == "1"
  
  if !enableMockSuccess {
    return false
  }
  
  // 对于网络错误或产品未找到错误，可以模拟成功
  let errorString = error.localizedDescription.lowercased()
  return errorString.contains("network") || 
         errorString.contains("connection") ||
         errorString.contains("not found")
}

private func mapProductToDetails(_ product: Product) -> (ProductType, SubscriptionDuration?, UserStateCore.UsageLimits) {
  switch product.id {
  case "com.wenhaofree.bridal.single_basic":
    return (.singlePurchase, nil, UserStateCore.UsageLimits.singleUse)
  case "com.wenhaofree.bridal.sub_monthly_40":
    return (.subscription, .monthly, UserStateCore.UsageLimits.monthlyPremium)
  case "com.wenhaofree.bridal.sub_yearly_600":
    return (.subscription, .yearly, UserStateCore.UsageLimits.yearlyStandard)
  default:
    return (.subscription, .monthly, UserStateCore.UsageLimits.monthlyPremium)
  }
}

private func calculateExpiryDate(for product: SubscriptionProduct) -> Date {
  let calendar = Calendar.current
  let now = Date()

  guard let duration = product.duration else {
    // For single purchases, return a short expiry for processing
    return calendar.date(byAdding: .day, value: 1, to: now) ?? now
  }

  print("⏰ 计算过期时间 - 产品: \(product.displayName), 周期: \(duration.displayName)")
  
  let expiryDate: Date
  switch duration {
  case .monthly:
    // 月度订阅：加1个月
    expiryDate = calendar.date(byAdding: .month, value: 1, to: now) ?? now
    print("📅 月度订阅过期时间: \(expiryDate)")
  case .quarterly:
    // 季度订阅：加3个月
    expiryDate = calendar.date(byAdding: .month, value: 3, to: now) ?? now
    print("📅 季度订阅过期时间: \(expiryDate)")
  case .yearly:
    // 年度订阅：加1年
    expiryDate = calendar.date(byAdding: .year, value: 1, to: now) ?? now
    print("📅 年度订阅过期时间: \(expiryDate)")
  }
  
  return expiryDate
}

private func mockProducts() -> [SubscriptionProduct] {
  return [
    // Single Purchase Option - 1元一次生成
    SubscriptionProduct(
      id: "com.wenhaofree.bridal.single_basic",
      displayName: "单次生成",
      description: "1次高清图片生成",
      price: "¥1",
      priceValue: 1.00,
      currencyCode: "CNY",
      productType: .singlePurchase,
      usageLimits: UserStateCore.UsageLimits.singleUse
    ),
    // Monthly Subscription - 28元月度方案
    SubscriptionProduct(
      id: "com.wenhaofree.bridal.sub_monthly_40",
      displayName: "高级月度订阅",
      description: "每月40次生成，解锁所有功能",
      price: "¥28",
      priceValue: 28.00,
      currencyCode: "CNY",
      productType: .subscription,
      duration: .monthly,
      usageLimits: UserStateCore.UsageLimits.monthlyPremium,
      isPopular: true
    ),
    // Yearly Subscription - 128元年度方案
    SubscriptionProduct(
      id: "com.wenhaofree.bridal.sub_yearly_600",
      displayName: "年度订阅",
      description: "每年600次生成，享受最大优惠",
      price: "¥128",
      priceValue: 128.00,
      currencyCode: "CNY",
      productType: .subscription,
      duration: .yearly,
      usageLimits: UserStateCore.UsageLimits.yearlyStandard
    )
  ]
}

// MARK: - StoreKit Errors

public enum StoreKitError: Error, LocalizedError {
  case productNotFound
  case userCancelled
  case purchasePending
  case failedVerification
  case unknown
  
  public var errorDescription: String? {
    switch self {
    case .productNotFound:
      return "产品未找到"
    case .userCancelled:
      return "用户取消购买"
    case .purchasePending:
      return "购买等待审核"
    case .failedVerification:
      return "交易验证失败"
    case .unknown:
      return "未知错误"
    }
  }
}

// MARK: - Dependency Registration

extension DependencyValues {
  public var liveStoreKitClient: StoreKitClient {
    get {
      if #available(iOS 15.0, macOS 12.0, *) {
        return LiveStoreKitClient.live
      } else {
        return StoreKitClient.liveValue // 回退到模拟实现
      }
    }
    set { self[StoreKitClient.self] = newValue }
  }
}
