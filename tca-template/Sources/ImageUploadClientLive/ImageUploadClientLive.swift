import Foundation
import Dependencies
import Network<PERSON>lient
import NetworkClientLive
import ImageUpload<PERSON>lient
import Authentication<PERSON>lient

// MARK: - Live Image Upload Client Implementation
// Note: DependencyKey implementation is now in ImageUploadClient.swift

// MARK: - Live Implementation

public final class LiveImageUploadClient: @unchecked Sendable {
  public static let shared = LiveImageUploadClient()

  private let baseURL = "http://127.0.0.1:8000"

  private init() {}
  
  public func uploadImage(_ imageData: Data, fileName: String) async throws -> ImageUploadResponse {
    print("🔄 [ImageUpload] Starting upload for file: \(fileName)")
    print("🔄 [ImageUpload] Image data size: \(imageData.count) bytes")

    // Get authentication token
    guard let authToken = AccessTokenManager.getAuthorizationHeader() else {
      print("❌ [ImageUpload] No valid authentication token found")
      throw ImageUploadError.authenticationRequired
    }

    print("🔐 [ImageUpload] Using auth token: \(authToken.prefix(50))...")

    // Get network client dependency
    @Dependency(\.networkClient) var networkClient

    // Create multipart form data
    let boundary = "----FormBoundary\(UUID().uuidString)"
    let multipartData = createMultipartFormData(
      imageData: imageData,
      fileName: fileName,
      boundary: boundary
    )

    print("🔄 [ImageUpload] Created multipart data, size: \(multipartData.count) bytes")

    // Create network request
    let url = URL(string: "\(baseURL)/api/v1/image/upload?file")!
    let request = NetworkRequest(
      url: url,
      method: .POST,
      headers: [
        "Authorization": authToken,
        "Content-Type": "multipart/form-data; boundary=\(boundary)",
        "User-Agent": "BridalApp/1.0.0",
        "Accept": "*/*",
        "Connection": "keep-alive"
      ],
      body: multipartData,
      timeout: 60.0
    )

    print("🔄 [ImageUpload] Sending request to: \(url.absoluteString)")
    print("🔄 [ImageUpload] Request headers: \(request.headers)")
    print("🔄 [ImageUpload] Request body size: \(multipartData.count) bytes")

    do {
      let responseData = try await networkClient.request(request)

      print("✅ [ImageUpload] Received response, size: \(responseData.count) bytes")

      // Log raw response for debugging
      if let responseString = String(data: responseData, encoding: .utf8) {
        print("✅ [ImageUpload] Raw response:")
        print(responseString)
      }

      // Parse response
      let response = try JSONDecoder().decode(ImageUploadResponse.self, from: responseData)

      print("✅ [ImageUpload] Upload successful!")
      print("✅ [ImageUpload] Response success: \(response.success)")
      print("✅ [ImageUpload] Response message: \(response.message)")
      print("✅ [ImageUpload] Image URL: \(response.url)")
      print("✅ [ImageUpload] Object name: \(response.objectName)")

      if let data = response.data {
        print("✅ [ImageUpload] Upload data - bucket: \(data.bucket), size: \(data.size)")
      }

      if let error = response.error {
        print("⚠️ [ImageUpload] Response contains error: \(error)")
      }

      return response
      
    } catch {
      print("❌ [ImageUpload] Upload failed with error: \(error)")
      
      if let networkError = error as? NetworkError {
        throw ImageUploadError.networkError(networkError.localizedDescription)
      } else {
        throw ImageUploadError.uploadFailed(error.localizedDescription)
      }
    }
  }
  
  public func uploadImages(_ imagesData: [Data]) async throws -> [ImageUploadResponse] {
    print("🔄 [ImageUpload] Starting batch upload for \(imagesData.count) images")
    
    var responses: [ImageUploadResponse] = []
    
    for (index, imageData) in imagesData.enumerated() {
      let fileName = "image_\(index + 1)_\(UUID().uuidString).jpeg"
      print("🔄 [ImageUpload] Uploading image \(index + 1)/\(imagesData.count): \(fileName)")
      
      do {
        let response = try await uploadImage(imageData, fileName: fileName)
        responses.append(response)
        print("✅ [ImageUpload] Image \(index + 1) uploaded successfully")
      } catch {
        print("❌ [ImageUpload] Failed to upload image \(index + 1): \(error)")
        throw error
      }
    }
    
    print("✅ [ImageUpload] Batch upload completed. \(responses.count) images uploaded successfully")
    return responses
  }
  
  private func createMultipartFormData(
    imageData: Data,
    fileName: String,
    boundary: String
  ) -> Data {
    var formData = Data()
    
    // Add file field
    formData.append("--\(boundary)\r\n".data(using: .utf8)!)
    formData.append("Content-Disposition: form-data; name=\"file\"; filename=\"\(fileName)\"\r\n".data(using: .utf8)!)
    formData.append("Content-Type: image/jpeg\r\n\r\n".data(using: .utf8)!)
    formData.append(imageData)
    formData.append("\r\n".data(using: .utf8)!)
    
    // Add closing boundary
    formData.append("--\(boundary)--\r\n".data(using: .utf8)!)
    
    return formData
  }
}
