import SwiftUI
import ComposableArchitecture
import AIGenerationCore
import CommonUI

// MARK: - AI Generation View

public struct AIGenerationView: View {
  let store: StoreOf<AIGeneration>
  
  public init(store: StoreOf<AIGeneration>) {
    self.store = store
  }
  
  public var body: some View {
    NavigationView {
      VStack(spacing: 0) {
        if store.hasResults {
          resultsSection
        } else if store.isGenerating {
          generatingSection
        } else if store.canRetry {
          retrySection
        } else {
          idleSection
        }
      }
      .navigationTitle("AI生成")
      #if os(iOS)
      .navigationBarTitleDisplayMode(.large)
      #endif
      .toolbar {
        ToolbarItem(placement: .automatic) {
          if store.isGenerating {
            Button("取消") {
              store.send(.cancelGeneration)
            }
            .foregroundColor(.red)
          }
        }
      }
      .alert(
        item: Binding(
          get: { store.error.map(ErrorWrapper.init) },
          set: { _ in store.send(.dismissError) }
        )
      ) { errorWrapper in
        Alert(
          title: Text("生成失败"),
          message: Text(errorWrapper.error.localizedDescription),
          primaryButton: .default(Text("重试")) {
            store.send(.retryGeneration)
          },
          secondaryButton: .cancel(Text("取消"))
        )
      }
    }
  }
  
  // MARK: - Idle Section
  
  private var idleSection: some View {
    VStack(spacing: 24) {
      Spacer()
      
      VStack(spacing: 16) {
        Image(systemName: "wand.and.stars")
          .font(.system(size: 60))
          .foregroundStyle(
            LinearGradient(
              colors: [.pink, .purple],
              startPoint: .topLeading,
              endPoint: .bottomTrailing
            )
          )
        
        VStack(spacing: 8) {
          Text("准备开始生成")
            .font(.title2)
            .fontWeight(.bold)
          
          Text("AI正在准备为您创造独特的婚纱照")
            .font(.subheadline)
            .foregroundColor(.secondary)
            .multilineTextAlignment(.center)
        }
      }
      
      Spacer()
    }
    .padding(.horizontal, 20)
  }
  
  // MARK: - Generating Section
  
  private var generatingSection: some View {
    VStack(spacing: 32) {
      Spacer()
      
      // Status Icon and Animation
      VStack(spacing: 20) {
        ZStack {
          Circle()
            .stroke(Color.pink.opacity(0.2), lineWidth: 8)
            .frame(width: 120, height: 120)
          
          Circle()
            .trim(from: 0, to: store.progress)
            .stroke(
              LinearGradient(
                colors: [.pink, .purple],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
              ),
              style: StrokeStyle(lineWidth: 8, lineCap: .round)
            )
            .frame(width: 120, height: 120)
            .rotationEffect(.degrees(-90))
            .animation(.easeInOut(duration: 0.5), value: store.progress)
          
          Image(systemName: store.generationStatus.systemImage)
            .font(.system(size: 32))
            .foregroundStyle(
              LinearGradient(
                colors: [.pink, .purple],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
              )
            )
        }
        
        VStack(spacing: 8) {
          Text(store.generationStatus.displayName)
            .font(.title2)
            .fontWeight(.semibold)
          
          Text("\(Int(store.progress * 100))%")
            .font(.title3)
            .fontWeight(.medium)
            .foregroundColor(.secondary)
        }
      }
      
      // Progress Details
      VStack(spacing: 12) {
        if store.estimatedTimeRemaining > 0 {
          HStack {
            Image(systemName: "clock")
              .foregroundColor(.secondary)
            
            Text("预计剩余时间: \(formatTime(store.estimatedTimeRemaining))")
              .font(.subheadline)
              .foregroundColor(.secondary)
          }
        }
        
        VStack(spacing: 8) {
          Text("AI正在精心创作您的婚纱照")
            .font(.headline)
          
          Text("请耐心等待，好的作品需要时间雕琢")
            .font(.subheadline)
            .foregroundColor(.secondary)
            .multilineTextAlignment(.center)
        }
      }
      
      Spacer()
      
      // Cancel Button
      Button("取消生成") {
        store.send(.cancelGeneration)
      }
      .font(.headline)
      .foregroundColor(.red)
      .padding(.horizontal, 24)
      .padding(.vertical, 12)
      .background(Color.red.opacity(0.1))
      .cornerRadius(12)
    }
    .padding(.horizontal, 20)
  }
  
  // MARK: - Results Section
  
  private var resultsSection: some View {
    ScrollView {
      VStack(spacing: 24) {
        // Header
        VStack(spacing: 12) {
          Image(systemName: "checkmark.circle.fill")
            .font(.system(size: 40))
            .foregroundColor(.green)
          
          VStack(spacing: 4) {
            Text("生成完成！")
              .font(.title2)
              .fontWeight(.bold)
            
            Text("为您生成了 \(store.generatedImages.count) 张精美婚纱照")
              .font(.subheadline)
              .foregroundColor(.secondary)
          }
        }
        .padding(.top, 20)
        
        // Generated Images Grid
        LazyVGrid(
          columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 2),
          spacing: 12
        ) {
          ForEach(store.generatedImages) { image in
            GeneratedImageCard(image: image) { action in
              switch action {
              case .select:
                store.send(.selectImage(image))
              case .save:
                store.send(.saveImage(image))
              case .share:
                store.send(.shareImage(image))
              }
            }
          }
        }
        .padding(.horizontal, 20)
        
        // Action Buttons
        VStack(spacing: 12) {
          Button("查看所有作品") {
            store.send(.proceedToGallery)
          }
          .font(.headline)
          .foregroundColor(.white)
          .frame(maxWidth: .infinity)
          .padding(.vertical, 16)
          .background(
            LinearGradient(
              colors: [.pink, .purple],
              startPoint: .leading,
              endPoint: .trailing
            )
          )
          .cornerRadius(12)
          
          Button("生成更多") {
            // This would trigger a new generation
          }
          .font(.headline)
          .foregroundColor(.primary)
          .frame(maxWidth: .infinity)
          .padding(.vertical, 16)
          .background(Color.gray.opacity(0.1))
          .cornerRadius(12)
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 20)
      }
    }
  }
  
  // MARK: - Retry Section
  
  private var retrySection: some View {
    VStack(spacing: 24) {
      Spacer()
      
      VStack(spacing: 16) {
        Image(systemName: "exclamationmark.triangle")
          .font(.system(size: 60))
          .foregroundColor(.orange)
        
        VStack(spacing: 8) {
          Text("生成失败")
            .font(.title2)
            .fontWeight(.bold)
          
          if let error = store.error {
            Text(error.localizedDescription)
              .font(.subheadline)
              .foregroundColor(.secondary)
              .multilineTextAlignment(.center)
          }
        }
      }
      
      VStack(spacing: 12) {
        Button("重新生成") {
          store.send(.retryGeneration)
        }
        .font(.headline)
        .foregroundColor(.white)
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(
          LinearGradient(
            colors: [.pink, .purple],
            startPoint: .leading,
            endPoint: .trailing
          )
        )
        .cornerRadius(12)
        .disabled(store.isRetrying)
        
        if store.isRetrying {
          HStack {
            ProgressView()
              .scaleEffect(0.8)
            
            Text("重试中...")
              .font(.subheadline)
              .foregroundColor(.secondary)
          }
        }
      }
      
      Spacer()
    }
    .padding(.horizontal, 20)
  }
  
  // MARK: - Helper Functions
  
  private func formatTime(_ timeInterval: TimeInterval) -> String {
    let minutes = Int(timeInterval) / 60
    let seconds = Int(timeInterval) % 60
    
    if minutes > 0 {
      return "\(minutes)分\(seconds)秒"
    } else {
      return "\(seconds)秒"
    }
  }
}

// MARK: - Supporting Views

struct GeneratedImageCard: View {
  let image: GeneratedImage
  let onAction: (ImageAction) -> Void

  enum ImageAction {
    case select
    case save
    case share
  }

  private var mockImageView: some View {
    VStack(spacing: 8) {
      Image(systemName: "sparkles")
        .font(.system(size: 40))
        .foregroundStyle(
          LinearGradient(
            colors: [.pink, .purple],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
          )
        )

      Text("AI生成")
        .font(.caption)
        .fontWeight(.medium)
        .foregroundColor(.secondary)

      Text(image.style.name)
        .font(.caption2)
        .foregroundColor(.secondary)
        .multilineTextAlignment(.center)
    }
  }
  
  var body: some View {
    VStack(spacing: 0) {
      // Image Preview
      ZStack {
        RoundedRectangle(cornerRadius: 12)
          .fill(Color.gray.opacity(0.1))
          .frame(height: 200)

        // Display mock generated image
        if !image.imageData.isEmpty {
          #if canImport(UIKit)
          if let uiImage = UIImage(data: image.imageData) {
            Image(uiImage: uiImage)
              .resizable()
              .aspectRatio(contentMode: .fill)
              .frame(height: 200)
              .clipped()
              .cornerRadius(12)
          } else {
            mockImageView
          }
          #else
          mockImageView
          #endif
        } else {
          mockImageView
        }
        
        // Quality Badge
        VStack {
          HStack {
            Spacer()
            
            Text(image.quality.displayName)
              .font(.caption2)
              .fontWeight(.bold)
              .foregroundColor(.white)
              .padding(.horizontal, 8)
              .padding(.vertical, 4)
              .background(Color.black.opacity(0.7))
              .cornerRadius(8)
              .padding(8)
          }
          
          Spacer()
        }
      }
      
      // Action Buttons
      HStack(spacing: 8) {
        Button(action: { onAction(.save) }) {
          Image(systemName: "square.and.arrow.down")
            .font(.title3)
            .foregroundColor(.primary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 12)
        .background(Color.gray.opacity(0.1))
        .cornerRadius(8)

        Button(action: { onAction(.share) }) {
          Image(systemName: "square.and.arrow.up")
            .font(.title3)
            .foregroundColor(.primary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 12)
        .background(Color.gray.opacity(0.1))
        .cornerRadius(8)
      }
      .padding(12)
    }
    .background(Color.white)
    .cornerRadius(16)
    .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    .onTapGesture {
      onAction(.select)
    }
  }
}

// MARK: - Error Wrapper

struct ErrorWrapper: Identifiable {
  let id = UUID()
  let error: AIGenerationError
}

// MARK: - Previews

#Preview("Idle") {
  AIGenerationView(
    store: Store(
      initialState: AIGeneration.State()
    ) {
      AIGeneration()
    }
  )
}

#Preview("Generating") {
  AIGenerationView(
    store: Store(
      initialState: AIGeneration.State(
        generationStatus: .processing,
        progress: 0.6,
        estimatedTimeRemaining: 15
      )
    ) {
      AIGeneration()
    }
  )
}

#Preview("Results") {
  AIGenerationView(
    store: Store(
      initialState: AIGeneration.State(
        generationStatus: .completed,
        progress: 1.0,
        generatedImages: [
          // Mock generated images would go here
        ]
      )
    ) {
      AIGeneration()
    }
  )
}
