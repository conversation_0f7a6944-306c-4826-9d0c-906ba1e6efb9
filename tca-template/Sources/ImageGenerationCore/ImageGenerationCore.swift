import Foundation
import ComposableArchitecture
import PhotoUploadCore
import LoggingClient
import ImageTypeSelectionCore
import ImageGenerationClient
import UserStateCore
import ImageUploadClient
import NewImageGenerationClient

#if canImport(UIKit)
import UIKit
#endif

// MARK: - Image Generation Feature

@Reducer
public struct ImageGeneration: Sendable {
  @ObservableState
  public struct State: Equatable, Sendable {
    public var template: ImageTemplate
    public var selectedPhotos: [PhotoItem] = []
    public var uploadedImageUrls: [String] = [] // Store uploaded image URLs
    public var generationStatus: GenerationStatus = .preparing
    public var progress: Double = 0.0
    public var estimatedTimeRemaining: TimeInterval = 0.0
    public var error: GenerationError?
    public var generatedImages: [GeneratedImage] = [] // Changed to array for multiple images
    public var retryCount = 0
    public var quotaValidation: QuotaValidationResult?
    public var isValidatingQuota = false
    
    public init(template: ImageTemplate, selectedPhotos: [PhotoItem] = []) {
      self.template = template
      self.selectedPhotos = selectedPhotos
      self.uploadedImageUrls = []
      self.estimatedTimeRemaining = template.estimatedGenerationTime
    }
  }
  
  public enum GenerationStatus: String, Equatable, Sendable {
    case preparing = "Preparing"
    case uploading = "Uploading"
    case generating = "Generating"
    case processing = "Processing"
    case finalizing = "Finalizing"
    case submitted = "Submitted"  // 新增：任务已提交状态
    case completed = "Completed"
    case failed = "Failed"
    
    public var message: String {
      switch self {
      case .preparing: return "正在准备生成您的图片..."
      case .uploading: return "正在上传您的照片..."
      case .generating: return "AI 正在为您创作精美图片..."
      case .processing: return "正在处理和优化细节..."
      case .finalizing: return "正在添加最后的润色..."
      case .submitted: return "生图任务已提交，请稍后查看"
      case .completed: return "您的图片已准备就绪！"
      case .failed: return "生成失败"
      }
    }
  }
  
  public enum Action: Sendable {
    case onAppear
    case validateQuota
    case quotaValidated(QuotaValidationResult)
    case quotaValidationFailed(String)
    case startGeneration
    case uploadingImages
    case imagesUploaded([String]) // Array of uploaded image URLs
    case imageUploadFailed(String)
    case generatingImages
    case serverGenerationStarted(GeneratedImageResponse)
    case serverGenerationFailed(ImageGenerationError)
    case newGenerationCompleted(NewGeneratedImageResponse)
    case newGenerationFailed(NewImageGenerationError)
    case pollGenerationStatus(String)
    case newTaskStatusReceived(NewTaskStatusResponse)
    case generationStatusUpdated(GenerationStatusResponse)
    case updateProgress(Double)
    case updateStatus(GenerationStatus)
    case generationSucceeded([GeneratedImage]) // Changed to array for multiple images
    case generationFailed(GenerationError)
    case retryGeneration
    case cancelGeneration
    case backToTypeSelection
  }
  
  @Dependency(\.loggingClient) var logger
  @Dependency(\.imageGenerationClient) var imageGenerationClient
  @Dependency(\.imageUploadClient) var imageUploadClient
  @Dependency(\.newImageGenerationClient) var newImageGenerationClient
  
  public init() {}
  
  public var body: some Reducer<State, Action> {
    Reduce<State, Action> { state, action in
      switch action {
      case .onAppear:
        logger.info(.aiGeneration, "Image generation screen appeared for template: \(state.template.name)")
        print("🚀 [ImageGeneration] Screen appeared, skipping quota validation and starting generation directly")
        return Effect<Action>.run { send in
          await send(Action.startGeneration)
        }
        
      case .validateQuota:
        logger.info(.aiGeneration, "Validating quota before generation")
        state.isValidatingQuota = true
        state.error = nil
        
        return .run { send in
          do {
            let validation = try await self.imageGenerationClient.validateQuotaForGeneration()
            await send(.quotaValidated(validation))
          } catch {
            await send(.quotaValidationFailed(error.localizedDescription))
          }
        }
        
      case let .quotaValidated(validation):
        state.isValidatingQuota = false
        state.quotaValidation = validation
        
        if validation.canGenerate {
          logger.info(.aiGeneration, "Quota validation passed, starting generation")
          return Effect<Action>.run { send in
            await send(Action.startGeneration)
          }
        } else {
          logger.warning(.aiGeneration, "Quota validation failed: \(validation.reason ?? "Unknown reason")")
          let error = GenerationError.insufficientCredits
          return Effect<Action>.run { send in
            await send(Action.generationFailed(error))
          }
        }
        
      case let .quotaValidationFailed(errorMessage):
        state.isValidatingQuota = false
        logger.error(.aiGeneration, "Quota validation failed: \(errorMessage)")
        let error = GenerationError.unknown(errorMessage)
        return .run { send in
          await send(.generationFailed(error))
        }
        
      case .startGeneration:
        logger.info(.aiGeneration, "Starting new image generation flow with upload")
        state.generationStatus = .preparing
        state.progress = 0.0
        state.error = nil
        state.estimatedTimeRemaining = state.template.estimatedGenerationTime

        print("🚀 [ImageGeneration] Starting generation process")
        print("🚀 [ImageGeneration] Template: \(state.template.name) (\(state.template.id))")
        print("🚀 [ImageGeneration] Selected photos count: \(state.selectedPhotos.count)")

        // First upload images
        return Effect<Action>.run { send in
          await send(Action.uploadingImages)
        }

      case .uploadingImages:
        logger.info(.aiGeneration, "Uploading images to server")
        state.generationStatus = .uploading
        state.progress = 0.1

        print("📤 [ImageGeneration] Starting image upload")

        return Effect<Action>.run { [selectedPhotos = state.selectedPhotos] send in
          do {
            // Convert PhotoItems to Data array
            let imagesData = selectedPhotos.map { $0.imageData }
            print("📤 [ImageGeneration] Uploading \(imagesData.count) images")

            // Upload images
            let uploadResponses = try await self.imageUploadClient.uploadImages(imagesData)

            // Extract URLs from responses
            let imageUrls = uploadResponses.map { $0.url }
            print("📤 [ImageGeneration] Upload completed, URLs: \(imageUrls)")

            await send(Action.imagesUploaded(imageUrls))
          } catch {
            print("❌ [ImageGeneration] Upload failed: \(error)")
            await send(Action.imageUploadFailed(error.localizedDescription))
          }
        }

      case let .imagesUploaded(imageUrls):
        logger.info(.aiGeneration, "Images uploaded successfully, starting generation")
        state.progress = 0.3
        state.uploadedImageUrls = imageUrls

        print("✅ [ImageGeneration] Images uploaded successfully")
        print("✅ [ImageGeneration] Image URLs: \(imageUrls)")

        return Effect<Action>.run { send in
          await send(Action.generatingImages)
        }

      case let .imageUploadFailed(error):
        logger.error(.aiGeneration, "Image upload failed: \(error)")
        print("❌ [ImageGeneration] Image upload failed: \(error)")
        return Effect<Action>.run { send in
          await send(Action.generationFailed(.uploadFailed))
        }

      case .generatingImages:
        logger.info(.aiGeneration, "Starting image generation with uploaded URLs")
        state.generationStatus = .generating
        state.progress = 0.5

        print("🎨 [ImageGeneration] Starting image generation")

        return Effect<Action>.run { [template = state.template, imageUrls = state.uploadedImageUrls] send in
          do {
            // Create prompt based on template
            let prompt = generatePromptForTemplate(template)
            print("🎨 [ImageGeneration] Generated prompt: \(prompt)")
            print("🎨 [ImageGeneration] Using image URLs: \(imageUrls)")

            let request = NewGenerateImageRequest(
              prompt: prompt,
              size: "1:1",
              isEnhance: false,
              uploadCn: false,
              nVariants: 1,
              enableFallback: false,
              fallbackModel: "FLUX_MAX",
              filesUrl: imageUrls,
              callBackUrl: "http://c89de6f7.natappfree.cc/api/v1/image/callback"
            )

            let response = try await self.newImageGenerationClient.generateImageSync(request)
            await send(Action.newGenerationCompleted(response))
          } catch {
            print("❌ [ImageGeneration] Generation failed: \(error)")
            if let apiError = error as? NewImageGenerationError {
              await send(Action.newGenerationFailed(apiError))
            } else {
              await send(Action.newGenerationFailed(.generationFailed(error.localizedDescription)))
            }
          }
        }

      case let .newGenerationCompleted(response):
        logger.info(.aiGeneration, "New generation task submitted successfully")
        state.progress = 0.5  // 任务提交成功，设置为50%
        state.generationStatus = .submitted  // 使用新的已提交状态

        print("✅ [ImageGeneration] Generation task submitted successfully")
        print("✅ [ImageGeneration] Response code: \(response.code)")
        print("✅ [ImageGeneration] Response message: \(response.message)")
        print("✅ [ImageGeneration] Is success: \(response.isSuccess)")

        if response.isSuccess, let data = response.data, let taskId = data.taskId {
          print("✅ [ImageGeneration] Task data:")
          print("   - Task ID: \(taskId)")
          if let generationRecordId = data.generationRecordId {
            print("   - Generation Record ID: \(generationRecordId)")
          }
          if let status = data.status {
            print("   - Status: \(status)")
          }
          if let estimatedCompletionTime = data.estimatedCompletionTime {
            print("   - Estimated Completion Time: \(estimatedCompletionTime)")
          }
          print("   - Remaining Credits: \(data.remainingCredits)")
          print("   - Remaining Monthly Usage: \(data.remainingMonthlyUsage)")

          // 开始轮询生成记录状态，使用 taskId
          return Effect<Action>.run { send in
            await send(Action.pollGenerationStatus(taskId))
          }
        } else {
          let errorMessage = "Generation task submission failed: \(response.message)"
          return Effect<Action>.run { send in
            await send(Action.newGenerationFailed(.serverError(errorMessage)))
          }
        }

      case let .newGenerationFailed(error):
        logger.error(.aiGeneration, "New generation failed: \(error)")
        print("❌ [ImageGeneration] Generation failed: \(error)")

        // 根据不同的错误类型设置相应的错误状态
        let generationError: GenerationError

        switch error {
        case .apiError(let code, let message):
          print("❌ [ImageGeneration] API Error - Code: \(code), Message: \(message)")

          // 根据错误码决定错误类型
          switch code {
          case 1001: // 积分不足
            generationError = .insufficientCredits
          case 1002: // 无有效订阅
            generationError = .insufficientCredits
          case 1003: // 月度限制已达
            generationError = .monthlyLimitReached
          case 3001, 3002: // API认证相关错误
            generationError = .authenticationFailed
          case 3004, 3005: // 服务不可用
            generationError = .serverError
          case 3006: // 队列已满
            generationError = .serverError
          default:
            generationError = .serverError
          }

        case .networkError:
          generationError = .networkError
        case .authenticationRequired:
          generationError = .authenticationFailed
        default:
          generationError = .serverError
        }

        return Effect<Action>.run { send in
          await send(Action.generationFailed(generationError))
        }

      case let .serverGenerationStarted(response):
        if response.success {
          logger.info(.aiGeneration, "Server generation started with ID: \(response.generationId)")
          return Effect<Action>.run { send in
            await send(Action.pollGenerationStatus(response.generationId))
          }
        } else {
          logger.error(.aiGeneration, "Server generation failed to start: \(response.message ?? "Unknown error")")
          return Effect<Action>.run { send in
            await send(Action.generationFailed(.serverError))
          }
        }
        
      case let .serverGenerationFailed(error):
        logger.error(.aiGeneration, "Server generation failed: \(error.localizedDescription)")
        let mappedError = mapImageGenerationError(error)
        return Effect<Action>.run { send in
          await send(Action.generationFailed(mappedError))
        }
        
      case let .pollGenerationStatus(taskId):
        logger.info(.aiGeneration, "Polling generation status for task ID: \(taskId)")

        return .run { send in
          // Poll every 3 seconds until completion
          while true {
            do {
              let statusResponse = try await newImageGenerationClient.getGenerationRecord(taskId)
              await send(.newTaskStatusReceived(statusResponse))

              // Check if task is completed (success or failed)
              if let data = statusResponse.data {
                // Stop polling if task is in a final state
                switch data.status {
                case "SUCCESS", "CREATE_TASK_FAILED", "GENERATE_FAILED":
                  print("🔄 [ImageGeneration] Task completed with status: \(data.status), stopping polling")
                  break
                case "GENERATING":
                  print("🔄 [ImageGeneration] Task still generating, progress: \(data.progress), continuing polling")
                  // Continue polling
                default:
                  print("🔄 [ImageGeneration] Unknown status: \(data.status), continuing polling")
                  // Continue polling for unknown statuses
                }
              }

              try await Task.sleep(for: .seconds(3))
            } catch {
              logger.error(.aiGeneration, "Failed to poll generation status: \(error)")
              if let apiError = error as? NewImageGenerationError {
                await send(.newGenerationFailed(apiError))
              } else {
                await send(.newGenerationFailed(.networkError(error.localizedDescription)))
              }
              break
            }
          }
        }

      case let .newTaskStatusReceived(statusResponse):
        logger.info(.aiGeneration, "Received task status update")
        print("✅ [ImageGeneration] Task status update received")
        print("✅ [ImageGeneration] Response code: \(statusResponse.code)")
        print("✅ [ImageGeneration] Response message: \(statusResponse.message)")

        if statusResponse.isSuccess, let recordData = statusResponse.data {
          print("✅ [ImageGeneration] Generation record data:")
          print("   - Task ID: \(recordData.taskId)")
          print("   - Status: \(recordData.status)")
          print("   - Success Flag: \(recordData.successFlag)")
          print("   - Progress: \(recordData.progress)")
          print("   - Is Success: \(recordData.isSuccess)")
          print("   - Image URLs count: \(recordData.imageUrls.count)")

          // Update progress
          state.progress = recordData.progressValue

          // Check task status and handle accordingly
          switch recordData.status {
          case "SUCCESS":
            print("✅ [ImageGeneration] Generation completed successfully!")
            state.generationStatus = .completed

            // Convert to GeneratedImage objects
            let generatedImages = recordData.imageUrls.enumerated().map { index, url in
              return GeneratedImage(
                id: "\(recordData.taskId)-\(index)",
                templateId: state.template.id,
                templateName: state.template.name,
                imageData: Data(), // Will be downloaded from URL later
                generatedAt: Date(),
                processingTime: 0, // Not provided in new API
                imageUrl: url,
                thumbnailUrl: url // Use same URL as thumbnail for now
              )
            }

            return Effect<Action>.run { send in
              await send(Action.generationSucceeded(generatedImages))
            }

          case "GENERATING":
            print("🔄 [ImageGeneration] Task is generating, progress: \(recordData.progress)")
            state.generationStatus = .generating
            // Update progress if available
            state.progress = recordData.progressValue

          case "CREATE_TASK_FAILED", "GENERATE_FAILED":
            print("❌ [ImageGeneration] Generation failed with status: \(recordData.status)")
            print("   - Error Code: \(recordData.errorCode ?? 0)")
            print("   - Error Message: \(recordData.errorMessage ?? "Unknown error")")

            let errorMessage = recordData.errorMessage ?? "Generation failed"
            return Effect<Action>.run { send in
              await send(Action.newGenerationFailed(.generationFailed(errorMessage)))
            }

          default:
            print("ℹ️ [ImageGeneration] Unknown status: \(recordData.status), treating as in progress")
            state.generationStatus = .generating
            // Update progress if available
            state.progress = recordData.progressValue
          }
        } else {
          // Status check failed
          let errorMessage = "Failed to get task status: \(statusResponse.message)"
          return Effect<Action>.run { send in
            await send(Action.newGenerationFailed(.serverError(errorMessage)))
          }
        }

        return .none

      case let .generationStatusUpdated(statusResponse):
        logger.info(.aiGeneration, "Generation status: \(statusResponse.status.rawValue) (\(statusResponse.progress * 100)%)")
        
        // Update progress and status
        state.progress = statusResponse.progress
        
        switch statusResponse.status {
        case .queued:
          state.generationStatus = .preparing
        case .processing, .generating:
          state.generationStatus = .generating
        case .finalizing:
          state.generationStatus = .finalizing
        case .completed:
          if !statusResponse.imageUrls.isEmpty {
            // Convert server response to local GeneratedImages (multiple)
            let generatedImages = statusResponse.imageUrls.enumerated().map { index, imageUrl in
              GeneratedImage(
                id: "\(statusResponse.generationId)-\(index)",
                templateId: state.template.id,
                templateName: state.template.name,
                imageData: Data(), // Will be downloaded from URL
                generatedAt: Date(),
                processingTime: state.template.estimatedGenerationTime,
                imageUrl: imageUrl,
                thumbnailUrl: index < statusResponse.thumbnailUrls.count ? statusResponse.thumbnailUrls[index] : nil
              )
            }
            return Effect<Action>.run { send in
              await send(Action.generationSucceeded(generatedImages))
            }
          } else {
            return Effect<Action>.run { send in
              await send(Action.generationFailed(.processingFailed))
            }
          }
        case .failed:
          let errorMessage = statusResponse.error ?? "Generation failed on server"
          return Effect<Action>.run { send in
            await send(Action.generationFailed(.unknown(errorMessage)))
          }
        case .cancelled:
          return Effect<Action>.run { send in
            await send(Action.generationFailed(.unknown("Generation was cancelled")))
          }
        }
        
        return .none
        
      case .updateProgress(let progress):
        state.progress = progress
        state.estimatedTimeRemaining = state.template.estimatedGenerationTime * (1.0 - progress)
        return .none
        
      case .updateStatus(let status):
        state.generationStatus = status
        logger.info(.aiGeneration, "Generation status updated: \(status.rawValue)")
        return .none
        
      case .generationSucceeded(let generatedImages):
        logger.info(.aiGeneration, "Image generation completed successfully with \(generatedImages.count) images")
        state.generationStatus = .completed
        state.progress = 1.0
        state.generatedImages = generatedImages
        state.estimatedTimeRemaining = 0.0
        return .none
        
      case .generationFailed(let error):
        logger.error(.aiGeneration, "Image generation failed: \(error.localizedDescription)")
        print("❌ [ImageGeneration] Setting error state: \(error)")
        print("❌ [ImageGeneration] Error type: \(error)")
        print("❌ [ImageGeneration] Error description: \(error.localizedDescription)")
        print("❌ [ImageGeneration] Is monthly limit: \(error == .monthlyLimitReached)")
        state.generationStatus = .failed
        state.error = error
        return .none
        
      case .retryGeneration:
        logger.info(.aiGeneration, "Retrying image generation")
        state.retryCount += 1
        state.error = nil
        return Effect<Action>.run { send in
          await send(Action.startGeneration)
        }
        
      case .cancelGeneration:
        logger.info(.aiGeneration, "Generation cancelled by user")
        return .none
        
      case .backToTypeSelection:
        logger.info(.aiGeneration, "Returning to template selection")
        return .none
      }
    }
  }
}

// MARK: - Helper Functions

@MainActor
private func generateDeviceFingerprint() -> String {
  let deviceId = UserDefaults.standard.string(forKey: "device_id") ?? UUID().uuidString
  let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0"

  #if canImport(UIKit)
  let systemVersion = UIDevice.current.systemVersion
  #else
  let systemVersion = "Unknown"
  #endif

  let timestamp = Date().timeIntervalSince1970

  let components = [deviceId, appVersion, systemVersion, String(Int(timestamp / 3600))]
  let combined = components.joined(separator: "|")

  return combined.data(using: .utf8)?.base64EncodedString() ?? deviceId
}

private func mapImageGenerationError(_ error: ImageGenerationError) -> GenerationError {
  switch error {
  case .quotaExhausted:
    return .insufficientCredits
  case .networkError:
    return .networkTimeout
  case .serverError:
    return .serverError
  case .invalidRequest, .invalidImageData, .unsupportedFormat, .imageTooLarge:
    return .invalidTemplate
  case .processingFailed:
    return .processingFailed
  case .requestTimeout:
    return .networkTimeout
  case .unauthorized:
    return .unknown("Authentication required")
  case .generationNotFound:
    return .unknown("Generation not found")
  case .notImplemented:
    return .unknown("Feature not available")
  }
}

// MARK: - Generated Image Model

public struct GeneratedImage: Identifiable, Equatable, Sendable {
  public let id: String
  public let templateId: String
  public let templateName: String
  public let imageData: Data
  public let generatedAt: Date
  public let processingTime: TimeInterval
  public let imageUrl: String?
  public let thumbnailUrl: String?
  
  public init(
    id: String,
    templateId: String,
    templateName: String,
    imageData: Data,
    generatedAt: Date,
    processingTime: TimeInterval,
    imageUrl: String? = nil,
    thumbnailUrl: String? = nil
  ) {
    self.id = id
    self.templateId = templateId
    self.templateName = templateName
    self.imageData = imageData
    self.generatedAt = generatedAt
    self.processingTime = processingTime
    self.imageUrl = imageUrl
    self.thumbnailUrl = thumbnailUrl
  }
}

// MARK: - Generation Error

public enum GenerationError: Error, Equatable, LocalizedError, Sendable {
  case networkTimeout
  case serverError
  case insufficientCredits
  case monthlyLimitReached
  case invalidTemplate
  case processingFailed
  case uploadFailed
  case networkError
  case authenticationFailed
  case unknown(String)
  
  public var errorDescription: String? {
    switch self {
    case .networkTimeout:
      return "网络超时，请检查网络连接后重试"
    case .serverError:
      return "服务器错误，请稍后重试"
    case .insufficientCredits:
      return "积分不足，请充值后再试"
    case .monthlyLimitReached:
      return "本月免费生图次数已用完\n\n💎 立即充值解锁更多次数\n🎯 或升级会员享受无限生图\n\n让我们继续为您创造美丽瞬间！"
    case .invalidTemplate:
      return "模板无效，请选择其他模板"
    case .processingFailed:
      return "图片处理失败，请重试"
    case .uploadFailed:
      return "图片上传失败，请检查网络连接后重试"
    case .networkError:
      return "网络连接失败，请检查网络设置"
    case .authenticationFailed:
      return "认证失败，请重新登录"
    case .unknown(let message):
      return "发生未知错误：\(message)"
    }
  }
  
  public var isRetryable: Bool {
    switch self {
    case .networkTimeout, .serverError, .processingFailed, .uploadFailed, .networkError:
      return true
    case .insufficientCredits, .monthlyLimitReached, .invalidTemplate, .authenticationFailed, .unknown:
      return false
    }
  }
}

// MARK: - Helper Functions

private func generatePromptForTemplate(_ template: ImageTemplate) -> String {
  // Use the template's predefined prompt directly
  let finalPrompt = template.prompt

  print("🎨 [Prompt] Using template prompt for '\(template.name)' (\(template.id)):")
  print("🎨 [Prompt] \(finalPrompt)")

  return finalPrompt
}