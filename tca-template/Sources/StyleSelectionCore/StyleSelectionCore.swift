import ComposableArchitecture
import Foundation
import SwiftUI

// MARK: - Style Selection Feature

@Reducer
public struct StyleSelection: Sendable {
  
  // MARK: - State
  
  @ObservableState
  public struct State: Equatable, Sendable {
    public var availableStyles: [WeddingStyle] = WeddingStyle.mockStyles
    public var selectedStyle: WeddingStyle?
    public var customPrompt: String = ""
    public var isShowingCustomPrompt = false
    public var isShowingGenerationConfirmation = false
    public var isLoading = false
    public var error: StyleSelectionError?

    public init(
      availableStyles: [WeddingStyle] = WeddingStyle.mockStyles,
      selectedStyle: WeddingStyle? = nil,
      customPrompt: String = "",
      isShowingCustomPrompt: Bool = false,
      isShowingGenerationConfirmation: Bool = false,
      isLoading: Bool = false,
      error: StyleSelectionError? = nil
    ) {
      self.availableStyles = availableStyles
      self.selectedStyle = selectedStyle
      self.customPrompt = customPrompt
      self.isShowingCustomPrompt = isShowingCustomPrompt
      self.isShowingGenerationConfirmation = isShowingGenerationConfirmation
      self.isLoading = isLoading
      self.error = error
    }
    
    public var canProceed: Bool {
      selectedStyle != nil
    }
    
    public var isCustomStyle: Bool {
      selectedStyle?.isCustom == true
    }
  }
  
  // MARK: - Action
  
  public enum Action: BindableAction, Sendable {
    case binding(BindingAction<State>)
    case onAppear
    case styleSelected(WeddingStyle)
    case showCustomPrompt
    case hideCustomPrompt
    case customPromptChanged(String)
    case showGenerationConfirmation
    case hideGenerationConfirmation
    case confirmGeneration
    case proceedToGeneration
    case loadMoreStyles
    case errorOccurred(StyleSelectionError)
    case dismissError
  }
  
  // MARK: - Dependencies
  
  @Dependency(\.styleClient) var styleClient

  // MARK: - Initializer

  public init() {}

  // MARK: - Reducer Body

  public var body: some Reducer<State, Action> {
    BindingReducer()
    
    Reduce { state, action in
      switch action {
      case .binding:
        return .none
        
      case .onAppear:
        state.isLoading = true
        return .run { send in
          do {
            let styles = try await styleClient.loadStyles()
            await send(.binding(.set(\.availableStyles, styles)))
            await send(.binding(.set(\.isLoading, false)))
          } catch {
            await send(.errorOccurred(.loadingFailed(error.localizedDescription)))
          }
        }
        
      case let .styleSelected(style):
        state.selectedStyle = style
        if style.isCustom {
          state.isShowingCustomPrompt = true
        } else {
          state.isShowingCustomPrompt = false
          state.customPrompt = ""
        }
        return .none
        
      case .showCustomPrompt:
        state.isShowingCustomPrompt = true
        return .none
        
      case .hideCustomPrompt:
        state.isShowingCustomPrompt = false
        return .none
        
      case let .customPromptChanged(prompt):
        state.customPrompt = prompt
        return .none

      case .showGenerationConfirmation:
        state.isShowingGenerationConfirmation = true
        return .none

      case .hideGenerationConfirmation:
        state.isShowingGenerationConfirmation = false
        return .none

      case .confirmGeneration:
        state.isShowingGenerationConfirmation = false
        return .send(.proceedToGeneration)

      case .proceedToGeneration:
        // This will be handled by parent reducer
        return .none
        
      case .loadMoreStyles:
        state.isLoading = true
        return .run { send in
          do {
            let moreStyles = try await styleClient.loadMoreStyles()
            await send(.binding(.set(\.availableStyles, moreStyles)))
            await send(.binding(.set(\.isLoading, false)))
          } catch {
            await send(.errorOccurred(.loadingFailed(error.localizedDescription)))
          }
        }
        
      case let .errorOccurred(error):
        state.error = error
        state.isLoading = false
        return .none
        
      case .dismissError:
        state.error = nil
        return .none
      }
    }
  }
}

// MARK: - Models

public struct WeddingStyle: Identifiable, Equatable, Sendable {
  public let id = UUID()
  public let name: String
  public let description: String
  public let previewImageName: String
  public let tags: [String]
  public let isCustom: Bool
  public let isPremium: Bool
  public let prompt: String
  
  public init(
    name: String,
    description: String,
    previewImageName: String,
    tags: [String] = [],
    isCustom: Bool = false,
    isPremium: Bool = false,
    prompt: String = ""
  ) {
    self.name = name
    self.description = description
    self.previewImageName = previewImageName
    self.tags = tags
    self.isCustom = isCustom
    self.isPremium = isPremium
    self.prompt = prompt
  }
  
  public static func == (lhs: WeddingStyle, rhs: WeddingStyle) -> Bool {
    lhs.id == rhs.id
  }
}

extension WeddingStyle {
  public static let mockStyles: [WeddingStyle] = [
    WeddingStyle(
      name: "经典优雅",
      description: "传统白色婚纱，优雅大方",
      previewImageName: "classic_elegant",
      tags: ["经典", "优雅", "白色"],
      prompt: "classic elegant white wedding dress, romantic, soft lighting"
    ),
    WeddingStyle(
      name: "浪漫花园",
      description: "花园背景，自然浪漫",
      previewImageName: "romantic_garden",
      tags: ["浪漫", "花园", "自然"],
      prompt: "romantic garden wedding, flowers, natural lighting, dreamy"
    ),
    WeddingStyle(
      name: "现代简约",
      description: "简洁现代的设计风格",
      previewImageName: "modern_minimal",
      tags: ["现代", "简约", "时尚"],
      prompt: "modern minimalist wedding dress, clean lines, contemporary"
    ),
    WeddingStyle(
      name: "复古宫廷",
      description: "华丽的宫廷风格",
      previewImageName: "vintage_royal",
      tags: ["复古", "宫廷", "华丽"],
      isPremium: true,
      prompt: "vintage royal wedding dress, ornate details, palace setting"
    ),
    WeddingStyle(
      name: "海滩度假",
      description: "轻松的海滩婚礼风格",
      previewImageName: "beach_casual",
      tags: ["海滩", "度假", "轻松"],
      isPremium: true,
      prompt: "beach wedding dress, casual, ocean background, sunset"
    ),
    WeddingStyle(
      name: "森林仙境",
      description: "神秘的森林主题",
      previewImageName: "forest_fairy",
      tags: ["森林", "仙境", "神秘"],
      isPremium: true,
      prompt: "forest fairy wedding, mystical, woodland setting, ethereal"
    ),
    WeddingStyle(
      name: "自定义风格",
      description: "创建您独特的风格",
      previewImageName: "custom_style",
      tags: ["自定义", "个性"],
      isCustom: true,
      prompt: ""
    )
  ]
}

public enum StyleSelectionError: Error, Equatable, LocalizedError, Sendable {
  case loadingFailed(String)
  case networkError(String)
  case invalidCustomPrompt
  case premiumRequired
  
  public var errorDescription: String? {
    switch self {
    case .loadingFailed(let message):
      return "加载风格失败: \(message)"
    case .networkError(let message):
      return "网络错误: \(message)"
    case .invalidCustomPrompt:
      return "自定义描述不能为空"
    case .premiumRequired:
      return "此风格需要VIP会员"
    }
  }
  
  public var recoverySuggestion: String? {
    switch self {
    case .loadingFailed, .networkError:
      return "请检查网络连接后重试"
    case .invalidCustomPrompt:
      return "请输入您想要的风格描述"
    case .premiumRequired:
      return "升级到VIP会员以使用高级风格"
    }
  }
}

// MARK: - Dependencies

@DependencyClient
public struct StyleClient: Sendable {
  public var loadStyles: @Sendable () async throws -> [WeddingStyle] = { [] }
  public var loadMoreStyles: @Sendable () async throws -> [WeddingStyle] = { [] }
  public var validateCustomPrompt: @Sendable (String) async throws -> Bool = { _ in false }
}

extension StyleClient: DependencyKey {
  public static let liveValue = StyleClient(
    loadStyles: {
      // TODO: Implement API call
      try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second delay
      return WeddingStyle.mockStyles
    },
    loadMoreStyles: {
      // TODO: Implement API call for more styles
      try await Task.sleep(nanoseconds: 1_000_000_000)
      return []
    },
    validateCustomPrompt: { prompt in
      // TODO: Implement prompt validation
      return !prompt.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
  )
  
  public static let testValue = StyleClient(
    loadStyles: { WeddingStyle.mockStyles },
    loadMoreStyles: { [] },
    validateCustomPrompt: { _ in true }
  )
}

extension DependencyValues {
  public var styleClient: StyleClient {
    get { self[StyleClient.self] }
    set { self[StyleClient.self] = newValue }
  }
}

// MARK: - Style Categories

public enum StyleCategory: String, CaseIterable, Sendable {
  case all = "全部"
  case classic = "经典"
  case romantic = "浪漫"
  case modern = "现代"
  case vintage = "复古"
  case outdoor = "户外"
  case premium = "高级"
  
  public var displayName: String {
    return self.rawValue
  }
  
  public var systemImage: String {
    switch self {
    case .all:
      return "square.grid.2x2"
    case .classic:
      return "crown"
    case .romantic:
      return "heart"
    case .modern:
      return "rectangle.3.group"
    case .vintage:
      return "clock"
    case .outdoor:
      return "leaf"
    case .premium:
      return "star"
    }
  }
}
