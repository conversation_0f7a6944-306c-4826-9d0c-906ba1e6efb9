import SwiftUI
import ComposableArchitecture
import UserStateCore
import AuthenticationClient
import CommonUI

#if canImport(UIKit)
import UIKit
#endif

// MARK: - Enhanced Guest Usage Banner

public struct GuestUsageBanner: View {
  let store: StoreOf<UserState>

  @State private var isAnimating = false
  @State private var hasAppeared = false

  public init(store: StoreOf<UserState>) {
    self.store = store
  }

  public var body: some View {
    if store.isGuest && store.guestUsageStats.remainingGenerations <= 1 {
      VStack(spacing: 16) {
        HStack(spacing: 12) {
          ZStack {
            Circle()
              .fill(
                LinearGradient(
                  colors: [.orange, .red],
                  startPoint: .topLeading,
                  endPoint: .bottomTrailing
                )
              )
              .frame(width: 40, height: 40)
              .scaleEffect(isAnimating ? 1.1 : 1.0)
              .animation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true), value: isAnimating)

            Image(systemName: "exclamationmark.triangle.fill")
              .font(.system(size: 18, weight: .bold))
              .foregroundColor(.white)
          }

          VStack(alignment: .leading, spacing: 4) {
            Text("剩余生成次数不足")
              .font(.headline)
              .fontWeight(.bold)
              .foregroundColor(.primary)

            Text("还可生成 \(store.guestUsageStats.remainingGenerations) 张图片")
              .font(.subheadline)
              .foregroundColor(.secondary)
          }

          Spacer()
        }

        // Progress Bar
        VStack(spacing: 8) {
          HStack {
            Text("使用进度")
              .font(.caption)
              .foregroundColor(.secondary)

            Spacer()

            Text("\(store.guestUsageStats.totalGenerations)/3")
              .font(.caption.weight(.medium))
              .foregroundColor(.orange)
          }

          GeometryReader { geometry in
            ZStack(alignment: .leading) {
              RoundedRectangle(cornerRadius: 4)
                .fill(Color.gray.opacity(0.2))
                .frame(height: 8)

              RoundedRectangle(cornerRadius: 4)
                .fill(
                  LinearGradient(
                    colors: [.orange, .red],
                    startPoint: .leading,
                    endPoint: .trailing
                  )
                )
                .frame(
                  width: geometry.size.width * (Double(store.guestUsageStats.totalGenerations) / 3.0),
                  height: 8
                )
                .animation(.easeInOut(duration: 0.8), value: store.guestUsageStats.totalGenerations)
            }
          }
          .frame(height: 8)
        }

        Button("立即注册解锁无限生成") {
          #if canImport(UIKit)
          let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
          impactFeedback.impactOccurred()
          #endif
          store.send(.promptLogin)
        }
        .font(.subheadline.weight(.semibold))
        .foregroundColor(.white)
        .frame(maxWidth: .infinity)
        .padding(.vertical, 12)
        .background(
          LinearGradient(
            colors: [.pink, .purple],
            startPoint: .leading,
            endPoint: .trailing
          )
        )
        .cornerRadius(12)
        .shadow(color: .pink.opacity(0.3), radius: 8, x: 0, y: 4)
      }
      .padding(20)
      .background(
        RoundedRectangle(cornerRadius: 16)
          .fill(Color.cardBackground)
          .shadow(color: .orange.opacity(0.2), radius: 12, x: 0, y: 6)
      )
      .overlay(
        RoundedRectangle(cornerRadius: 16)
          .stroke(
            LinearGradient(
              colors: [.orange.opacity(0.5), .red.opacity(0.3)],
              startPoint: .topLeading,
              endPoint: .bottomTrailing
            ),
            lineWidth: 2
          )
      )
      .padding(.horizontal)
      .scaleEffect(hasAppeared ? 1.0 : 0.9)
      .opacity(hasAppeared ? 1.0 : 0.0)
      .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.2), value: hasAppeared)
      .onAppear {
        hasAppeared = true
        isAnimating = true
      }
    }
  }
}

// MARK: - Login Prompt Modal (Deprecated - Use EnhancedLoginPromptModal)

public struct LoginPromptModal: View {
  let store: StoreOf<UserState>
  let onLogin: () -> Void
  let onDismiss: () -> Void

  public init(
    store: StoreOf<UserState>,
    onLogin: @escaping () -> Void,
    onDismiss: @escaping () -> Void
  ) {
    self.store = store
    self.onLogin = onLogin
    self.onDismiss = onDismiss
  }

  public var body: some View {
    // Use the new enhanced modal with Apple ID support
    EnhancedLoginPromptModal(
      store: store,
      onAppleSignIn: { credential in
        store.send(.signInWithApple(credential))
      },
      onDismiss: onDismiss
    )
  }
}

// MARK: - Benefit Row

private struct BenefitRow: View {
  let icon: String
  let title: String
  let description: String
  
  var body: some View {
    HStack(spacing: 12) {
      Image(systemName: icon)
        .font(.title2)
        .foregroundStyle(
          LinearGradient(
            colors: [.pink, .purple],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
          )
        )
        .frame(width: 24)
      
      VStack(alignment: .leading, spacing: 2) {
        Text(title)
          .font(.headline)
          .fontWeight(.medium)
        
        Text(description)
          .font(.subheadline)
          .foregroundColor(.secondary)
      }
      
      Spacer()
    }
  }
}

// MARK: - Enhanced User Status Indicator

public struct UserStatusIndicator: View {
  let store: StoreOf<UserState>

  @State private var isPressed = false
  @State private var hasAppeared = false

  public init(store: StoreOf<UserState>) {
    self.store = store
  }

  public var body: some View {
    HStack(spacing: 12) {
      // Enhanced Avatar with animation
      ZStack {
        Circle()
          .fill(
            LinearGradient(
              colors: store.isGuest ? [.gray, .gray.opacity(0.7)] : [.pink, .purple],
              startPoint: .topLeading,
              endPoint: .bottomTrailing
            )
          )
          .frame(width: 44, height: 44)
          .shadow(
            color: store.isGuest ? .gray.opacity(0.3) : .pink.opacity(0.4),
            radius: 6,
            x: 0,
            y: 3
          )

        Text(store.avatarInitials)
          .font(.subheadline)
          .fontWeight(.bold)
          .foregroundColor(.white)

        // Status indicator dot
        if !store.isGuest {
          Circle()
            .fill(Color.green)
            .frame(width: 12, height: 12)
            .overlay(
              Circle()
                .stroke(Color.white, lineWidth: 2)
            )
            .offset(x: 16, y: -16)
        }
      }

      VStack(alignment: .leading, spacing: 4) {
        HStack(spacing: 6) {
          Text(store.displayName)
            .font(.headline)
            .fontWeight(.semibold)
            .foregroundColor(.primary)

          if !store.isGuest, let user = store.user {
            // Subscription badge
            Text(user.subscriptionStatus.displayName.uppercased())
              .font(.caption2)
              .fontWeight(.bold)
              .foregroundColor(.white)
              .padding(.horizontal, 6)
              .padding(.vertical, 2)
              .background(
                Capsule()
                  .fill(subscriptionColor(for: user.subscriptionStatus))
              )
          }
        }

        HStack(spacing: 4) {
          if store.isGuest {
            Image(systemName: "person.crop.circle.badge.questionmark")
              .font(.caption)
              .foregroundColor(.orange)

            Text("游客模式 · 剩余 \(store.guestUsageStats.remainingGenerations) 次")
              .font(.caption)
              .foregroundColor(.secondary)
          } else if let user = store.user {
            Image(systemName: "checkmark.circle.fill")
              .font(.caption)
              .foregroundColor(.green)

            Text(user.subscriptionStatus.displayName)
              .font(.caption)
              .foregroundColor(.secondary)
          }
        }
      }

      Spacer()

      if store.isGuest {
        Button("登录") {
          #if canImport(UIKit)
          let impactFeedback = UIImpactFeedbackGenerator(style: .light)
          impactFeedback.impactOccurred()
          #endif
          store.send(.promptLogin)
        }
        .font(.caption.weight(.semibold))
        .foregroundColor(.white)
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(
          LinearGradient(
            colors: [.pink, .purple],
            startPoint: .leading,
            endPoint: .trailing
          )
        )
        .cornerRadius(16)
        .shadow(color: .pink.opacity(0.3), radius: 4, x: 0, y: 2)
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .animation(.easeInOut(duration: 0.1), value: isPressed)
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
          withAnimation(.easeInOut(duration: 0.1)) {
            isPressed = pressing
          }
        }, perform: {})
      } else {
        // Settings button for authenticated users
        Button(action: {}) {
          Image(systemName: "gearshape.fill")
            .font(.title3)
            .foregroundColor(.secondary)
        }
      }
    }
    .padding(.horizontal, 16)
    .padding(.vertical, 12)
    .background(
      RoundedRectangle(cornerRadius: 16)
        .fill(Color.surfaceBackground)
        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
    )
    .scaleEffect(hasAppeared ? 1.0 : 0.9)
    .opacity(hasAppeared ? 1.0 : 0.0)
    .animation(.spring(response: 0.6, dampingFraction: 0.8), value: hasAppeared)
    .onAppear {
      hasAppeared = true
    }
  }

  private func subscriptionColor(for status: SubscriptionStatus) -> Color {
    switch status {
    case .free: return .blue
    case .premium: return .purple
    case .expired: return .gray
    }
  }
}

#Preview("Guest Usage Banner") {
  GuestUsageBanner(
    store: Store(initialState: UserState.State()) {
      UserState()
    }
  )
  .padding()
}

#Preview("Login Prompt Modal") {
  LoginPromptModal(
    store: Store(initialState: UserState.State()) {
      UserState()
    },
    onLogin: {},
    onDismiss: {}
  )
  .padding()
}

#Preview("User Status Indicator") {
  UserStatusIndicator(
    store: Store(initialState: UserState.State()) {
      UserState()
    }
  )
  .padding()
}
