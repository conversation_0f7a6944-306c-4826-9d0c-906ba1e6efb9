import Foundation
import ComposableArchitecture
import PhotoUploadCore
import LoggingClient
import CommonUI
import UserStateCore

// MARK: - Image Type Selection Feature

@Reducer
public struct ImageTypeSelection {
  @ObservableState
  public struct State: Equatable, Sendable {
    public var isGuestMode = false
    public var templates: [ImageTemplate] = []
    public var isLoading = false
    public var error: String?
    public var selectedTemplate: ImageTemplate?
    public var selectedPhotos: [PhotoItem] = []
    public var userSubscriptionStatus: SubscriptionStatus = .free  // 添加用户订阅状态
    public var isShowingGenerationConfirmation = false

    public init(isGuestMode: Bool = false, selectedPhotos: [PhotoItem] = [], userSubscriptionStatus: SubscriptionStatus = .free) {
      self.isGuestMode = isGuestMode
      self.selectedPhotos = selectedPhotos
      self.userSubscriptionStatus = userSubscriptionStatus
    }
  }
  
  public enum Action: Sendable {
    case onAppear
    case loadTemplates
    case templatesLoaded([ImageTemplate])
    case templateLoadFailed(String)
    case templateSelected(ImageTemplate)
    case showGenerationConfirmation(ImageTemplate)
    case hideGenerationConfirmation
    case confirmGeneration
    case proceedToGeneration(ImageTemplate)
    case showSubscriptionForTemplate(ImageTemplate)
    case refreshTemplates
    case clearError
    case showProfile  // 新增：显示用户设置页面
  }
  
  @Dependency(\.loggingClient) var logger
  
  public init() {}
  
  public var body: some ReducerOf<Self> {
    Reduce { state, action in
      switch action {
      case .onAppear:
        logger.info(.ui, "Image type selection appeared")
        return .send(.loadTemplates)
        
      case .loadTemplates:
        state.isLoading = true
        state.error = nil
        
        return .run { send in
          do {
            // Simulate loading templates
            try await Task.sleep(for: .seconds(AppConstants.Network.mockDelayShort))
            let templates = generateMockTemplates()
            await send(.templatesLoaded(templates))
          } catch {
            await send(.templateLoadFailed(error.localizedDescription))
          }
        }
        
      case .templatesLoaded(let templates):
        logger.info(.ui, "Loaded \(templates.count) image templates")
        state.isLoading = false
        state.templates = templates
        return .none
        
      case .templateLoadFailed(let error):
        logger.error(.ui, "Failed to load templates: \(error)")
        state.isLoading = false
        state.error = error
        return .none
        
      case .templateSelected(let template):
        logger.info(.ui, "🎯 Template selected in Core: \(template.name)")
        state.selectedTemplate = template

        // Check if template requires subscription and user status
        if template.isPremium {
          logger.info(.ui, "🔒 Premium template selected: \(template.name), checking user subscription status")
          print("🔍 Debug: Template '\(template.name)' isPremium: \(template.isPremium)")
          print("🔍 Debug: Current user subscription status: \(state.userSubscriptionStatus)")
          print("🔍 Debug: isPremiumActive: \(state.userSubscriptionStatus.isPremiumActive)")

          // Check if user has active premium subscription
          let hasActiveSubscription = state.userSubscriptionStatus.isPremiumActive

          if hasActiveSubscription {
            logger.info(.ui, "✅ User has active premium subscription, showing confirmation")
            print("🎯 Debug: Showing confirmation for premium user with template: \(template.name)")
            return .send(.showGenerationConfirmation(template))
          } else {
            logger.info(.ui, "❌ User doesn't have premium subscription, showing subscription page")
            print("💳 Debug: Showing subscription page for free user with template: \(template.name)")
            return .send(.showSubscriptionForTemplate(template))
          }
        } else {
          logger.info(.ui, "✅ Free template selected, showing confirmation")
          print("🆓 Debug: Showing confirmation for free template: \(template.name)")
          return .send(.showGenerationConfirmation(template))
        }
        
      case .showGenerationConfirmation(let template):
        logger.info(.ui, "📋 Showing generation confirmation for template: \(template.name)")
        state.selectedTemplate = template
        state.isShowingGenerationConfirmation = true
        return .none

      case .hideGenerationConfirmation:
        logger.info(.ui, "❌ Hiding generation confirmation")
        state.isShowingGenerationConfirmation = false
        return .none

      case .confirmGeneration:
        logger.info(.ui, "✅ User confirmed generation")
        state.isShowingGenerationConfirmation = false
        if let template = state.selectedTemplate {
          return .send(.proceedToGeneration(template))
        }
        return .none

      case .showSubscriptionForTemplate(let template):
        logger.info(.ui, "🔒 Showing subscription for premium template: \(template.name)")
        // This will be handled by AppCore to navigate to subscription
        return .none

      case .proceedToGeneration(let template):
        logger.info(.ui, "🎯 Proceeding to generation with template: \(template.name)")
        // 这个动作应该被 AppCore 监听并导航到生成页面
        return .none
        
      case .refreshTemplates:
        return .send(.loadTemplates)
        
      case .clearError:
        state.error = nil
        return .none
        
      case .showProfile:
        logger.info(.ui, "👤 User tapped profile button, navigating to settings")
        // This will be handled by AppCore to navigate to profile/settings
        return .none
      }
    }
  }
}

// MARK: - Image Template Model

public struct ImageTemplate: Identifiable, Equatable, Sendable {
  public let id: String
  public let name: String
  public let description: String
  public let category: Category
  public let previewImageURL: String?
  public let isPremium: Bool
  public let estimatedGenerationTime: TimeInterval
  public let prompt: String  // 新增：英文描述词，用于生图

  public init(
    id: String,
    name: String,
    description: String,
    category: Category,
    previewImageURL: String? = nil,
    isPremium: Bool = false,
    estimatedGenerationTime: TimeInterval = 30.0,
    prompt: String
  ) {
    self.id = id
    self.name = name
    self.description = description
    self.category = category
    self.previewImageURL = previewImageURL
    self.isPremium = isPremium
    self.estimatedGenerationTime = estimatedGenerationTime
    self.prompt = prompt
  }
  
  public enum Category: String, CaseIterable, Sendable {
    case wedding = "婚纱"
    case portrait = "肖像"
    case fashion = "时尚"
    case vintage = "复古"
    case artistic = "艺术"
    case elegant = "优雅"
    
    public var systemImage: String {
      switch self {
      case .wedding: return "heart.fill"
      case .portrait: return "person.fill"
      case .fashion: return "sparkles"
      case .vintage: return "camera.fill"  // 修复：使用存在的系统图标
      case .artistic: return "paintbrush.fill"
      case .elegant: return "star.fill"
      }
    }
    
    public var color: String {
      switch self {
      case .wedding: return "pink"
      case .portrait: return "blue"
      case .fashion: return "purple"
      case .vintage: return "brown"
      case .artistic: return "orange"
      case .elegant: return "gold"
      }
    }
  }
}

// MARK: - Mock Data Generation

private func generateMockTemplates() -> [ImageTemplate] {
  [
    ImageTemplate(
      id: "wedding_classic",
      name: "经典婚纱",
      description: "优雅的传统婚纱风格，柔和光线",
      category: .wedding,
      isPremium: false,
      estimatedGenerationTime: 25.0,
      prompt: "Keep the same face from reference image, preserve facial identity, elegant traditional wedding dress style, soft lighting, delicate lace details, romantic bouquet, beautiful background, warm atmosphere, premium texture, flowing white veil, classic posing, refined makeup, dreamy glow, natural posture, tranquil mood, high resolution, professional quality, detailed, beautiful"
    ),
    ImageTemplate(
      id: "wedding_romantic",
      name: "浪漫新娘",
      description: "梦幻浪漫风格，鲜花与柔和色彩",
      category: .wedding,
      isPremium: true,
      estimatedGenerationTime: 35.0,
      prompt: "Keep the same face from reference image, preserve facial identity, dreamy romantic wedding style, soft pastel colors, fresh flowers, ethereal lighting, flowing fabric, gentle breeze, fairy-tale atmosphere, delicate petals, romantic garden setting, soft focus, magical ambiance, tender expression, graceful pose, enchanting beauty, cinematic quality, artistic composition"
    ),
    ImageTemplate(
      id: "portrait_professional",
      name: "专业肖像",
      description: "简洁专业的头像风格",
      category: .portrait,
      isPremium: false,
      estimatedGenerationTime: 20.0,
      prompt: "Keep the same face from reference image, preserve facial identity, professional portrait photography, clean background, soft studio lighting, confident expression, business attire, sharp focus, neutral colors, formal posing, executive style, corporate headshot, professional makeup, polished appearance, high-end retouching, commercial quality"
    ),
    ImageTemplate(
      id: "fashion_modern",
      name: "现代时尚",
      description: "当代时尚摄影风格",
      category: .fashion,
      isPremium: true,
      estimatedGenerationTime: 40.0,
      prompt: "Keep the same face from reference image, preserve facial identity, contemporary fashion photography, trendy styling, modern urban setting, dynamic pose, fashion-forward clothing, editorial lighting, bold composition, street style, avant-garde fashion, creative angles, vibrant colors, magazine quality, high fashion aesthetic, artistic direction"
    ),
    ImageTemplate(
      id: "vintage_classic",
      name: "复古胶片",
      description: "经典胶片摄影，温暖色调",
      category: .vintage,
      isPremium: false,
      estimatedGenerationTime: 30.0,
      prompt: "Keep the same face from reference image, preserve facial identity, classic film photography, warm vintage tones, retro styling, nostalgic atmosphere, analog film grain, golden hour lighting, timeless fashion, classic pose, sepia undertones, old-school glamour, vintage texture, film photography aesthetic, classic beauty, timeless elegance"
    ),
    ImageTemplate(
      id: "artistic_creative",
      name: "艺术创意",
      description: "创意艺术诠释，独特效果",
      category: .artistic,
      isPremium: true,
      estimatedGenerationTime: 45.0,
      prompt: "Keep the same face from reference image, preserve facial identity, creative artistic photography, unique perspective, experimental lighting, abstract composition, innovative styling, conceptual art, dramatic shadows, artistic expression, unconventional angles, creative vision, fine art photography, gallery-worthy, artistic interpretation, avant-garde style"
    ),
    ImageTemplate(
      id: "elegant_luxury",
      name: "优雅奢华",
      description: "精致奢华风格，高端质感",
      category: .elegant,
      isPremium: true,
      estimatedGenerationTime: 50.0,
      prompt: "Keep the same face from reference image, preserve facial identity, elegant luxury portrait, sophisticated styling, premium fashion, refined atmosphere, high-end jewelry, luxurious fabrics, opulent setting, graceful posture, aristocratic elegance, expensive taste, exclusive fashion, upscale environment, refined beauty, luxury lifestyle"
    ),
    ImageTemplate(
      id: "wedding_outdoor",
      name: "户外婚纱",
      description: "自然户外婚纱风格，美丽风景",
      category: .wedding,
      isPremium: false,
      estimatedGenerationTime: 30.0,
      prompt: "Keep the same face from reference image, preserve facial identity, outdoor wedding photography, natural landscape, beautiful scenery, fresh air atmosphere, natural lighting, scenic background, countryside setting, garden wedding, outdoor ceremony, nature-inspired, rustic charm, outdoor romance, natural beauty, landscape wedding"
    )
  ]
}