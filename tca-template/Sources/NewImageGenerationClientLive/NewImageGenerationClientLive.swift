import Foundation
import Dependencies
import NetworkClient
import NetworkClientLive
import NewImageGenerationClient
import AuthenticationClient

// MARK: - Live New Image Generation Client Implementation
// Note: DependencyKey implementation is now in NewImageGenerationClient.swift

// MARK: - Live Implementation

public final class LiveNewImageGenerationClient: @unchecked Sendable {
  public static let shared = LiveNewImageGenerationClient()
  
  @Dependency(\.networkClient) private var networkClient
  
  private let baseURL = "http://127.0.0.1:8000"
  
  private init() {}
  
  public func generateImageSync(_ request: NewGenerateImageRequest) async throws -> NewGeneratedImageResponse {
    print("🎨 [ImageGeneration] Starting image generation")
    print("🎨 [ImageGeneration] Request parameters:")
    print("   - Files URLs: \(request.filesUrl)")
    print("   - Prompt: \(request.prompt)")
    print("   - Size: \(request.size)")
    print("   - Variants: \(request.nVariants)")

    // Get authentication token
    guard let authToken = AccessTokenManager.getAuthorizationHeader() else {
      print("❌ [ImageGeneration] No valid authentication token found")
      throw NewImageGenerationError.authenticationRequired
    }

    print("🔐 [ImageGeneration] Using auth token: \(authToken.prefix(50))...")

    // Create network request
    let url = URL(string: "\(baseURL)/api/v1/image/generate-image-sync")!

    // Encode request body
    let encoder = JSONEncoder()
    let requestData = try encoder.encode(request)

    print("🎨 [ImageGeneration] Request body JSON:")
    if let jsonString = String(data: requestData, encoding: .utf8) {
      print(jsonString)
    }

    let networkRequest = NetworkRequest(
      url: url,
      method: .POST,
      headers: [
        "Authorization": authToken,
        "Content-Type": "application/json",
        "User-Agent": "BridalApp/1.0.0",
        "Accept": "*/*",
        "Connection": "keep-alive"
      ],
      body: requestData,
      timeout: 120.0 // Longer timeout for image generation
    )
    
    print("🎨 [ImageGeneration] Sending request to: \(url.absoluteString)")
    print("🎨 [ImageGeneration] Request headers: \(networkRequest.headers)")
    
    do {
      let responseData = try await networkClient.request(networkRequest)
      
      print("✅ [ImageGeneration] Received response, size: \(responseData.count) bytes")
      
      // Log raw response for debugging
      if let responseString = String(data: responseData, encoding: .utf8) {
        print("✅ [ImageGeneration] Raw response:")
        print(responseString)
      }
      
      // Parse response
      let decoder = JSONDecoder()
      let response = try decoder.decode(NewGeneratedImageResponse.self, from: responseData)
      
      print("✅ [ImageGeneration] Generation task submitted!")
      print("✅ [ImageGeneration] Response code: \(response.code)")
      print("✅ [ImageGeneration] Response message: \(response.message)")
      print("✅ [ImageGeneration] Is success: \(response.isSuccess)")
      
      if let data = response.data {
        print("✅ [ImageGeneration] Generation data:")
        print("   - Task ID: \(data.taskId)")
        print("   - Generation Record ID: \(data.generationRecordId)")
        print("   - Status: \(data.status)")
        print("   - Estimated Completion Time: \(data.estimatedCompletionTime)")
        print("   - Remaining Credits: \(data.remainingCredits)")
        print("   - Remaining Monthly Usage: \(data.remainingMonthlyUsage)")
      }

      // 检查是否成功
      if !response.isSuccess {
        print("❌ [ImageGeneration] Generation failed with code: \(response.code)")

        // 根据错误码创建相应的错误
        if let errorCode = GenerationErrorCode(rawValue: response.code) {
          print("❌ [ImageGeneration] Error type: \(errorCode)")
          print("❌ [ImageGeneration] User message: \(errorCode.userMessage)")
          throw NewImageGenerationError.apiError(response.code, errorCode.userMessage)
        } else {
          // 未知错误码
          print("❌ [ImageGeneration] Unknown error code: \(response.code)")
          throw NewImageGenerationError.apiError(response.code, response.message)
        }
      }
      
      return response
      
    } catch {
      print("❌ [ImageGeneration] Generation failed with error: \(error)")
      
      if let networkError = error as? NetworkError {
        throw NewImageGenerationError.networkError(networkError.localizedDescription)
      } else if error is DecodingError {
        throw NewImageGenerationError.serverError("Failed to parse server response")
      } else {
        throw NewImageGenerationError.generationFailed(error.localizedDescription)
      }
    }
  }
}
