import Foundation
import ComposableArchitecture
import PhotoUploadCore
import ImageTypeSelectionCore
import ImageGenerationCore
import ImageViewCore
import ProfileCore
import SubscriptionCore
import UserStateCore
import LoggingClient

// MARK: - Main Tab Feature

@Reducer
public struct MainTab: Sendable {
  @ObservableState
  public struct State: Equatable, Sendable {
    public var selectedTab: Tab = .home
    public var homeFlow: HomeFlow.State = HomeFlow.State()
    public var profile: Profile.State = Profile.State()
    public var subscription: Subscription.State?  // Optional subscription state
    public var isShowingSubscription = false
    public var user: UserStateCore.User?  // 添加用户状态
    
    public init(user: UserStateCore.User? = nil) {
      self.user = user
      // 初始化Profile状态时传入用户信息
      self.profile = Profile.State(user: user)
      // 初始化HomeFlow状态时传入用户订阅状态
      let subscriptionStatus = user?.subscriptionStatus ?? .free
      self.homeFlow = HomeFlow.State(userSubscriptionStatus: subscriptionStatus)
    }
  }
  
  public enum Tab: String, CaseIterable, Sendable {
    case home = "home"
    case settings = "settings"
    
    public var title: String {
      switch self {
      case .home: return "主页"
      case .settings: return "设置"
      }
    }
    
    public var iconName: String {
      switch self {
      case .home: return "house.fill"
      case .settings: return "person.circle.fill"
      }
    }
  }
  
  public enum Action: Sendable {
    case tabSelected(Tab)
    case homeFlow(HomeFlow.Action)
    case profile(Profile.Action)
    case subscription(Subscription.Action)
    case showSubscription(String?)  // Show subscription with optional template ID
    case hideSubscription
    case setShowingSubscription(Bool)  // For binding support
    case setUserData(UserStateCore.User)  // Set user data after login
  }
  
  @Dependency(\.loggingClient) var logger
  
  public init() {}
  
  public var body: some Reducer<State, Action> {
    Reduce { state, action in
      switch action {
      case .tabSelected(let tab):
        logger.info(.ui, "🔄 Tab selected: \(tab.title)")
        state.selectedTab = tab
        return .none

      case .setUserData(let user):
        logger.info(.ui, "👤 Setting user data in MainTab")
        print("✅ MainTabCore: 设置用户数据")
        print("   用户: \(user.displayName) (\(user.email))")
        print("   订阅状态: \(user.subscriptionStatus.displayName)")

        // 更新MainTab的用户状态
        state.user = user

        // 更新HomeFlow的订阅状态
        state.homeFlow.userSubscriptionStatus = user.subscriptionStatus
        state.homeFlow.imageTypeSelection.userSubscriptionStatus = user.subscriptionStatus

        // 发送用户数据到ProfileCore
        return .send(.profile(.setUser(user)))
        
      case .homeFlow(.imageTypeSelection(.showSubscriptionForTemplate(let template))):
        logger.info(.ui, "💳 Premium template selected, showing subscription page")
        return .send(.showSubscription(template.id))
        
      case .showSubscription(let templateId):
        logger.info(.ui, "🔔 Showing subscription page")
        state.subscription = Subscription.State(selectedTemplate: templateId)
        state.isShowingSubscription = true
        return .none
        
      case .hideSubscription:
        logger.info(.ui, "🔔 Hiding subscription page")
        state.isShowingSubscription = false
        state.subscription = nil
        return .none
        
      case .setShowingSubscription(let isShowing):
        state.isShowingSubscription = isShowing
        if !isShowing {
          state.subscription = nil
        }
        return .none
        
      case .subscription(.returnToPreviousFlow):
        logger.info(.ui, "🔙 Returning from subscription to previous flow")
        return .send(.hideSubscription)
        
      case .subscription(.proceedWithPremiumTemplate) where state.subscription?.selectedTemplate != nil:
        logger.info(.ui, "✅ Subscription completed, proceeding with premium template")

        // 获取选中的模板ID并继续生成流程
        if let templateId = state.subscription?.selectedTemplate,
           let template = state.homeFlow.imageTypeSelection.templates.first(where: { $0.id == templateId }) {
          logger.info(.ui, "🎯 Found template \(template.name), proceeding to generation")

          // 从订阅状态获取最新的订阅信息，而不是硬编码
          let newSubscriptionStatus = state.subscription?.subscriptionStatus ?? .free
          logger.info(.ui, "📊 Using subscription status from subscription flow: \(newSubscriptionStatus.displayName)")

          // 更新所有相关状态
          state.homeFlow.userSubscriptionStatus = newSubscriptionStatus
          state.homeFlow.imageTypeSelection.userSubscriptionStatus = newSubscriptionStatus

          // 更新用户信息
          if let currentUser = state.user {
            let updatedUser = User(
              id: currentUser.id,
              email: currentUser.email,
              displayName: currentUser.displayName,
              avatarURL: currentUser.avatarURL,
              createdAt: currentUser.createdAt,
              subscriptionStatus: newSubscriptionStatus
            )
            state.user = updatedUser
            // 不直接设置 state.profile.user，让ProfileCore通过subscriptionStatusUpdated来处理
            logger.info(.ui, "👤 User subscription status updated in MainTab")
          }

          return .concatenate(
            .send(.hideSubscription),
            .send(.profile(.subscriptionStatusUpdated(newSubscriptionStatus))),
            .send(.homeFlow(.imageTypeSelection(.proceedToGeneration(template))))
          )
        } else {
          logger.error(.ui, "❌ Could not find selected template for subscription completion")
          return .send(.hideSubscription)
        }
        
      case .profile(.showSubscriptionPage):
        logger.info(.ui, "💳 User requested subscription page from settings")
        return .send(.showSubscription(nil))  // No specific template

      case .subscription(.proceedWithPremiumTemplate) where state.subscription?.selectedTemplate == nil:
        // 处理从设置页面发起的订阅成功（没有特定模板）
        logger.info(.ui, "✅ Subscription completed from settings page")

        // 从订阅状态获取最新的订阅信息
        let newSubscriptionStatus = state.subscription?.subscriptionStatus ?? .free
        logger.info(.ui, "📊 Settings subscription status: \(newSubscriptionStatus.displayName)")

        // 更新所有相关状态
        state.homeFlow.userSubscriptionStatus = newSubscriptionStatus
        state.homeFlow.imageTypeSelection.userSubscriptionStatus = newSubscriptionStatus

        // 更新用户信息
        if let currentUser = state.user {
          let updatedUser = User(
            id: currentUser.id,
            email: currentUser.email,
            displayName: currentUser.displayName,
            avatarURL: currentUser.avatarURL,
            createdAt: currentUser.createdAt,
            subscriptionStatus: newSubscriptionStatus
          )
          state.user = updatedUser
          // 不直接设置 state.profile.user，让ProfileCore通过subscriptionStatusUpdated来处理
          logger.info(.ui, "👤 User subscription status updated in MainTab")
        }

        print("🚀 MainTabCore: 准备发送订阅状态更新到ProfileCore")
        print("📊 MainTabCore: 发送的订阅状态: \(newSubscriptionStatus.displayName)")

        return .concatenate(
          .send(.hideSubscription),
          .send(.profile(.subscriptionStatusUpdated(newSubscriptionStatus)))
        )

      case .subscription:
        // Other subscription actions handled by Subscription reducer
        return .none

      case .homeFlow:
        // Other home flow actions handled by HomeFlow reducer
        return .none

      case .profile:
        // Other profile actions handled by Profile reducer
        return .none
      }
    }
    
    Scope(state: \.homeFlow, action: \.homeFlow) {
      HomeFlow()
    }
    
    Scope(state: \.profile, action: \.profile) {
      Profile()
    }
    
    .ifLet(\.subscription, action: \.subscription) {
      Subscription()
    }
  }
}

// MARK: - Home Flow (PhotoUpload → ImageTypeSelection → ImageGeneration → ImageView)

@Reducer
public struct HomeFlow: Sendable {
  @ObservableState
  public struct State: Equatable, Sendable {
    public var currentStep: Step = .photoUpload
    public var photoUpload: PhotoUpload.State = PhotoUpload.State()
    public var imageTypeSelection: ImageTypeSelection.State = ImageTypeSelection.State()
    public var imageGeneration: ImageGeneration.State?  // Optional since it needs template
    public var imageView: ImageView.State?  // Optional since it needs generatedImage
    public var userSubscriptionStatus: SubscriptionStatus = .free  // 添加用户订阅状态
    
    public init(userSubscriptionStatus: SubscriptionStatus = .free) {
      self.userSubscriptionStatus = userSubscriptionStatus
      self.imageTypeSelection = ImageTypeSelection.State(userSubscriptionStatus: userSubscriptionStatus)
    }
  }
  
  public enum Step: Sendable, Equatable {
    case photoUpload
    case imageTypeSelection
    case imageGeneration
    case imageView
  }
  
  public enum Action: Sendable {
    case photoUpload(PhotoUpload.Action)
    case imageTypeSelection(ImageTypeSelection.Action)
    case imageGeneration(ImageGeneration.Action)
    case imageView(ImageView.Action)
    case navigateToStep(Step)
    case resetToStart
  }
  
  @Dependency(\.loggingClient) var logger
  
  public init() {}
  
  public var body: some Reducer<State, Action> {
    Reduce { state, action in
      switch action {
      case .navigateToStep(let step):
        logger.info(.ui, "🚀 Navigating to step: \(step)")
        state.currentStep = step
        return .none
        
      case .resetToStart:
        logger.info(.ui, "🔄 Resetting home flow to start")
        state.currentStep = .photoUpload
        state.photoUpload = PhotoUpload.State()
        state.imageTypeSelection = ImageTypeSelection.State(userSubscriptionStatus: state.userSubscriptionStatus)
        state.imageGeneration = nil
        state.imageView = nil
        return .none
        
      case .photoUpload(.proceedToStyleSelection):
        logger.info(.ui, "📸 Photo upload completed, proceeding to style selection")
        // Extract selected photos
        let selectedPhotos = state.photoUpload.selectedImages
        state.imageTypeSelection = ImageTypeSelection.State(
          selectedPhotos: selectedPhotos,
          userSubscriptionStatus: state.userSubscriptionStatus
        )
        return .send(.navigateToStep(.imageTypeSelection))
        
      case .imageTypeSelection(.proceedToGeneration(let template)):
        logger.info(.ui, "🎨 Template selected, proceeding to generation")
        print("🚀 Debug: HomeFlow received proceedToGeneration for template: \(template.name)")
        print("🚀 Debug: Template isPremium: \(template.isPremium)")
        print("🚀 Debug: User subscription status: \(state.userSubscriptionStatus)")

        let selectedPhotos = state.imageTypeSelection.selectedPhotos
        print("🚀 Debug: Selected photos count: \(selectedPhotos.count)")

        state.imageGeneration = ImageGeneration.State(template: template, selectedPhotos: selectedPhotos)
        print("🚀 Debug: Created ImageGeneration state, navigating to generation step")
        return .send(.navigateToStep(.imageGeneration))
        
      case .imageGeneration(.generationSucceeded(let generatedImages)):
        logger.info(.ui, "🖼️ Image generation succeeded with \(generatedImages.count) images")
        // For now, use the first generated image for the ImageView
        // TODO: Update ImageView to handle multiple images
        if let firstImage = generatedImages.first {
          state.imageView = ImageView.State(generatedImage: firstImage)
          return .send(.navigateToStep(.imageView))
        } else {
          logger.error(.ui, "❌ No images generated")
          return .none
        }
        
      case .imageGeneration(.backToTypeSelection):
        logger.info(.ui, "🔙 Returning to image type selection")
        return .send(.navigateToStep(.imageTypeSelection))
        
      case .imageView(.backToTypeSelection):
        logger.info(.ui, "🔙 Returning to type selection from image view")
        return .send(.navigateToStep(.imageTypeSelection))
        
      default:
        return .none
      }
    }
    
    Scope(state: \.photoUpload, action: \.photoUpload) {
      PhotoUpload()
    }
    
    Scope(state: \.imageTypeSelection, action: \.imageTypeSelection) {
      ImageTypeSelection()
    }
    
    .ifLet(\.imageGeneration, action: \.imageGeneration) {
      ImageGeneration()
    }
    
    .ifLet(\.imageView, action: \.imageView) {
      ImageView()
    }
  }
}