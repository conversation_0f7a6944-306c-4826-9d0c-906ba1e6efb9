import Foundation
import ComposableArchitecture
import UserStateCore
import StoreKit
import LoggingClient

// MARK: - StoreKit Error Types

@available(iOS 15.0, macOS 12.0, *)
enum StoreKitError: Error, LocalizedError {
  case productNotFound
  case userCancelled
  case purchasePending
  case unknown
  case verificationFailed

  var errorDescription: String? {
    switch self {
    case .productNotFound:
      return "Product not found"
    case .userCancelled:
      return "User cancelled the purchase"
    case .purchasePending:
      return "Purchase is pending approval"
    case .unknown:
      return "Unknown error occurred"
    case .verificationFailed:
      return "Transaction verification failed"
    }
  }
}

// MARK: - StoreKit Helper Functions

@available(iOS 15.0, macOS 12.0, *)
func checkVerified<T>(_ result: VerificationResult<T>) throws -> T {
  switch result {
  case .unverified:
    throw StoreKitError.verificationFailed
  case .verified(let safe):
    return safe
  }
}

// MARK: - Subscription Feature

@Reducer
public struct Subscription: Sendable {
  
  // MARK: - State
  
  @ObservableState
  public struct State: Equatable, Sendable {
    public var subscriptionStatus: UserStateCore.SubscriptionStatus = .free
    public var availableProducts: [SubscriptionProduct] = []
    public var isLoading = false
    public var isPurchasing = false
    public var error: SubscriptionError?
    public var selectedTemplate: String?  // Store template ID instead of the template object
    public var showingSuccessAlert = false
    public var successMessage: String?
    
    public init(
      subscriptionStatus: UserStateCore.SubscriptionStatus = .free,
      availableProducts: [SubscriptionProduct] = [],
      selectedTemplate: String? = nil
    ) {
      self.subscriptionStatus = subscriptionStatus
      self.availableProducts = availableProducts
      self.selectedTemplate = selectedTemplate
    }
    
    public var isPremiumActive: Bool {
      switch subscriptionStatus {
      case .premium(let expiryDate, _):
        return expiryDate > Date()
      case .free, .expired:
        return false
      }
    }
    
    public var canAccessPremiumFeatures: Bool {
      isPremiumActive
    }
  }
  
  // MARK: - Action
  
  public enum Action: BindableAction, Sendable {
    case binding(BindingAction<State>)
    case onAppear
    case loadProducts
    case productsLoaded([SubscriptionProduct])
    case productLoadFailed(SubscriptionError)
    case checkSubscriptionStatus
    case subscriptionStatusUpdated(UserStateCore.SubscriptionStatus)
    case purchaseProduct(SubscriptionProduct)
    case purchaseStarted
    case purchaseCompleted(UserStateCore.SubscriptionStatus, SubscriptionProduct)
    case purchaseFailed(SubscriptionError)
    case restorePurchases
    case restoreCompleted(UserStateCore.SubscriptionStatus)
    case restoreFailed(SubscriptionError)
    case proceedWithPremiumTemplate
    case dismissError
    case dismissSuccessAlert
    case returnToPreviousFlow
    case addSinglePurchaseCredits(Int)
  }
  
  // MARK: - Dependencies
  
  @Dependency(\.storeKitClient) var storeKitClient
  @Dependency(\.subscriptionStorage) var subscriptionStorage
  @Dependency(\.loggingClient) var logger
  
  // MARK: - Initializer
  
  public init() {}
  
  // MARK: - Reducer Body
  
  public var body: some Reducer<State, Action> {
    BindingReducer()

    Reduce<State, Action> { state, action in
      switch action {
      case .binding:
        return .none
        
      case .onAppear:
        print("🎯 Subscription view appeared")
        return .merge(
          .send(.loadProducts),
          .send(.checkSubscriptionStatus)
        )
        
      case .loadProducts:
        print("🎯 Loading subscription products...")
        state.isLoading = true
        state.error = nil

        return .run { send in
          do {
            let products = try await storeKitClient.loadProducts()
            print("🎯 Loaded \(products.count) products:")
            for product in products {
              print("  - \(product.id): \(product.displayName) (\(product.price))")
            }
            await send(.productsLoaded(products))
          } catch {
            print("❌ Failed to load products: \(error)")
            await send(.productLoadFailed(.storeKitError(error.localizedDescription)))
          }
        }
        
      case let .productsLoaded(products):
        print("🎯 Loaded \(products.count) subscription products")
        state.isLoading = false
        state.availableProducts = products
        return .none
        
      case let .productLoadFailed(error):
        print("❌ Failed to load products: \(error.localizedDescription)")
        state.isLoading = false
        state.error = error
        return .none
        
      case .checkSubscriptionStatus:
        return .run { send in
          let status = await subscriptionStorage.getSubscriptionStatus()
          await send(.subscriptionStatusUpdated(status))
        }
        
      case let .subscriptionStatusUpdated(status):
        print("🎯 Subscription status updated: \(status)")
        state.subscriptionStatus = status
        return .none
        
      case let .purchaseProduct(product):
        print("🎯 Starting purchase for product: \(product.id)")
        state.isPurchasing = true
        state.error = nil

        return .run { send in
          await send(.purchaseStarted)

          do {
            let result = try await storeKitClient.purchase(product)
            await send(.purchaseCompleted(result, product))
          } catch {
            await send(.purchaseFailed(.purchaseFailed(error.localizedDescription)))
          }
        }
        
      case .purchaseStarted:
        state.isPurchasing = true
        return .none
        
      case let .purchaseCompleted(newStatus, product):
        print("🎉 Purchase completed successfully")
        state.isPurchasing = false
        state.subscriptionStatus = newStatus
        state.showingSuccessAlert = true

        // Determine success message based on purchase type
        if product.isSinglePurchase {
          state.successMessage = "🎉 购买成功！\n您已获得1次高清图片生成机会！"
        } else {
          state.successMessage = "🎉 恭喜！您已成功升级到Pro版本\n现在可以享受所有高级功能了！"
        }

        return .run { send in
          await subscriptionStorage.saveSubscriptionStatus(newStatus)

          // For single purchases, add credits to user state
          if product.isSinglePurchase, let credits = product.usageLimits.singlePurchaseGenerations {
            await send(.addSinglePurchaseCredits(credits))
          }

          // 延迟2秒后自动关闭成功提示并继续流程
          try await Task.sleep(for: .seconds(2))
          await send(.dismissSuccessAlert)
          await send(.proceedWithPremiumTemplate)
        }
        
      case let .purchaseFailed(error):
        print("❌ Purchase failed: \(error.localizedDescription)")
        state.isPurchasing = false
        state.error = error
        return .none
        
      case .restorePurchases:
        print("🔄 Restoring purchases")
        state.isLoading = true
        state.error = nil
        
        return .run { send in
          do {
            let status = try await storeKitClient.restorePurchases()
            await send(.restoreCompleted(status))
          } catch {
            await send(.restoreFailed(.restoreFailed(error.localizedDescription)))
          }
        }
        
      case let .restoreCompleted(status):
        print("✅ Purchases restored successfully")
        state.isLoading = false
        state.subscriptionStatus = status
        
        return .run { send in
          await subscriptionStorage.saveSubscriptionStatus(status)
        }
        
      case let .restoreFailed(error):
        print("❌ Restore failed: \(error.localizedDescription)")
        state.isLoading = false
        state.error = error
        return .none
        
      case .proceedWithPremiumTemplate:
        // This will be handled by parent reducer (AppCore)
        return .none
        
      case .dismissError:
        state.error = nil
        return .none
        
      case .dismissSuccessAlert:
        state.showingSuccessAlert = false
        state.successMessage = nil
        return .none
        
      case .returnToPreviousFlow:
        // This will be handled by parent reducer (AppCore)
        return .none

      case let .addSinglePurchaseCredits(credits):
        // This action will be forwarded to the parent to update user state
        print("📝 Adding \(credits) single purchase credits")
        return .none
      }
    }
  }
}

// MARK: - Models

public struct SubscriptionProduct: Identifiable, Equatable, Sendable {
  public let id: String
  public let displayName: String
  public let description: String
  public let price: String
  public let priceValue: Decimal
  public let currencyCode: String
  public let productType: ProductType
  public let duration: SubscriptionDuration?  // nil for single purchases
  public let usageLimits: UserStateCore.UsageLimits
  public let isPopular: Bool

  public init(
    id: String,
    displayName: String,
    description: String,
    price: String,
    priceValue: Decimal,
    currencyCode: String,
    productType: ProductType,
    duration: SubscriptionDuration? = nil,
    usageLimits: UserStateCore.UsageLimits,
    isPopular: Bool = false
  ) {
    self.id = id
    self.displayName = displayName
    self.description = description
    self.price = price
    self.priceValue = priceValue
    self.currencyCode = currencyCode
    self.productType = productType
    self.duration = duration
    self.usageLimits = usageLimits
    self.isPopular = isPopular
  }

  // Convenience computed properties
  public var isSubscription: Bool {
    return productType == .subscription
  }

  public var isSinglePurchase: Bool {
    return productType == .singlePurchase
  }

  public var generationLimit: String {
    if let singlePurchaseGenerations = usageLimits.singlePurchaseGenerations {
      return "\(singlePurchaseGenerations)次生成"
    } else if let monthlyGenerations = usageLimits.generationsPerMonth {
      return "每月\(monthlyGenerations)次"
    } else if let yearlyGenerations = usageLimits.generationsPerYear {
      return "每年\(yearlyGenerations)次"
    } else {
      return "无限制"
    }
  }
}

// MARK: - Product Type

public enum ProductType: String, CaseIterable, Sendable, Codable {
  case subscription = "subscription"
  case singlePurchase = "single_purchase"

  public var displayName: String {
    switch self {
    case .subscription: return "订阅"
    case .singlePurchase: return "单次购买"
    }
  }
}

// MARK: - Subscription Duration

public enum SubscriptionDuration: String, CaseIterable, Sendable, Codable {
  case monthly = "monthly"
  case quarterly = "quarterly"
  case yearly = "yearly"

  public var displayName: String {
    switch self {
    case .monthly: return "月度订阅"
    case .quarterly: return "季度订阅"
    case .yearly: return "年度订阅"
    }
  }

  public var savingsMessage: String? {
    switch self {
    case .monthly: return nil
    case .quarterly: return "节省17%"
    case .yearly: return "节省42%"
    }
  }

  public var duration: TimeInterval {
    switch self {
    case .monthly: return 30 * 24 * 60 * 60 // 30 days
    case .quarterly: return 90 * 24 * 60 * 60 // 90 days
    case .yearly: return 365 * 24 * 60 * 60 // 365 days
    }
  }
}



public enum SubscriptionError: Error, Equatable, LocalizedError, Sendable {
  case storeKitError(String)
  case purchaseFailed(String)
  case restoreFailed(String)
  case productNotFound
  case userCancelled
  case networkError
  case unknownError(String)
  
  public var errorDescription: String? {
    switch self {
    case .storeKitError(let message):
      return "商店错误: \(message)"
    case .purchaseFailed(let message):
      return "购买失败: \(message)"
    case .restoreFailed(let message):
      return "恢复购买失败: \(message)"
    case .productNotFound:
      return "未找到订阅产品"
    case .userCancelled:
      return "用户取消了购买"
    case .networkError:
      return "网络连接错误"
    case .unknownError(let message):
      return "未知错误: \(message)"
    }
  }
  
  public var recoverySuggestion: String? {
    switch self {
    case .storeKitError, .purchaseFailed, .restoreFailed:
      return "请稍后重试或联系客服"
    case .productNotFound:
      return "请检查网络连接后重试"
    case .userCancelled:
      return nil
    case .networkError:
      return "请检查网络连接"
    case .unknownError:
      return "请重启应用后重试"
    }
  }
}

// MARK: - Dependencies

@DependencyClient
public struct StoreKitClient: Sendable {
  public var loadProducts: @Sendable () async throws -> [SubscriptionProduct] = { [] }
  public var purchase: @Sendable (SubscriptionProduct) async throws -> UserStateCore.SubscriptionStatus = { _ in .free }
  public var restorePurchases: @Sendable () async throws -> UserStateCore.SubscriptionStatus = { .free }
  public var checkSubscriptionStatus: @Sendable () async -> UserStateCore.SubscriptionStatus = { .free }
}

extension StoreKitClient: DependencyKey {
  public static let liveValue: StoreKitClient = {
    print("🛒 SubscriptionCore: 配置StoreKit liveValue...")
    
    // 检查是否有明确的禁用标志
    if ProcessInfo.processInfo.environment["DISABLE_PAYMENTS"] == "1" {
      print("⚠️ SubscriptionCore: 支付功能已禁用，使用模拟实现")
      return mockStoreKitClient()
    }
    
    // 在iOS 15+上，我们应该使用LiveStoreKitClient，但为了避免循环依赖，
    // 这里创建一个最小的真实实现，主要用作后备
    if #available(iOS 15.0, macOS 12.0, *) {
      print("🛒 SubscriptionCore: 使用iOS 15+ StoreKit 2 API")
      return StoreKitClient(
        loadProducts: {
          print("🛒 [SubscriptionCore Live] Loading products with StoreKit 2...")
          do {
            let productIDs: Set<String> = [
              "com.wenhaofree.bridal.single_basic",
              "com.wenhaofree.bridal.sub_monthly_40",
              "com.wenhaofree.bridal.sub_yearly_600"
            ]
            
            let storeProducts = try await Product.products(for: productIDs)
            print("✅ [SubscriptionCore Live] 从App Store加载了 \(storeProducts.count) 个产品")
            
            if storeProducts.isEmpty {
              print("⚠️ [SubscriptionCore Live] 未找到产品，使用模拟产品")
              return mockProducts()
            }
            
            let subscriptionProducts = storeProducts.map { product in
              return SubscriptionProduct(
                id: product.id,
                displayName: product.displayName,
                description: product.description,
                price: product.displayPrice,
                priceValue: product.price,
                currencyCode: product.priceFormatStyle.currencyCode,
                productType: getProductType(for: product.id),
                duration: getDuration(for: product.id),
                usageLimits: getUsageLimits(for: product.id),
                isPopular: product.id == "com.wenhaofree.bridal.sub_monthly_40"
              )
            }
            
            return subscriptionProducts
          } catch {
            print("❌ [SubscriptionCore Live] 加载产品失败: \(error)")
            return mockProducts()
          }
        },
        purchase: { product in
          print("💳 [SubscriptionCore Live] 开始真实购买: \(product.displayName)")

          // 检查是否在开发/测试环境中
          #if DEBUG
          let hasStoreKitConfig = Bundle.main.url(forResource: "Configuration", withExtension: "storekit") != nil
          let enableTestMode = ProcessInfo.processInfo.environment["ENABLE_PURCHASE_TEST"] == "1"
          #if targetEnvironment(simulator)
          let isSimulator = true
          #else
          let isSimulator = false
          #endif

          print("🔍 [SubscriptionCore] StoreKit Config: \(hasStoreKitConfig), Test Mode: \(enableTestMode), Simulator: \(isSimulator)")

          if hasStoreKitConfig || enableTestMode || isSimulator {
            print("🧪 [SubscriptionCore Live] 检测到测试环境，模拟购买成功")

            // 在测试环境中直接模拟购买成功
            if let duration = product.duration {
              let calendar = Calendar.current
              let now = Date()
              let expiryDate: Date

              switch duration {
              case .monthly:
                expiryDate = calendar.date(byAdding: .month, value: 1, to: now) ?? now
              case .quarterly:
                expiryDate = calendar.date(byAdding: .month, value: 3, to: now) ?? now
              case .yearly:
                expiryDate = calendar.date(byAdding: .year, value: 1, to: now) ?? now
              }

              print("🎉 [SubscriptionCore Live] 模拟订阅成功，过期时间: \(expiryDate)")
              return .premium(expiryDate: expiryDate, usageLimits: product.usageLimits)
            } else {
              // 单次购买
              let expiryDate = Date().addingTimeInterval(24 * 60 * 60)
              print("🎉 [SubscriptionCore Live] 模拟单次购买成功")
              return .premium(expiryDate: expiryDate, usageLimits: product.usageLimits)
            }
          }
          #endif

          // 使用真实的StoreKit 2 API进行购买
          do {
            let storeProducts = try await Product.products(for: [product.id])
            guard let storeProduct = storeProducts.first else {
              print("❌ [SubscriptionCore Live] 产品未找到: \(product.id)")
              throw StoreKitError.productNotFound
            }

            print("💳 [SubscriptionCore Live] 发起真实购买请求...")
            let result = try await storeProduct.purchase()

            switch result {
            case .success(let verification):
              print("✅ [SubscriptionCore Live] 购买成功，验证交易...")
              let transaction = try checkVerified(verification)
              await transaction.finish()

              // 计算过期日期
              if let duration = product.duration {
                let calendar = Calendar.current
                let now = Date()
                let expiryDate: Date

                switch duration {
                case .monthly:
                  expiryDate = calendar.date(byAdding: .month, value: 1, to: now) ?? now
                case .quarterly:
                  expiryDate = calendar.date(byAdding: .month, value: 3, to: now) ?? now
                case .yearly:
                  expiryDate = calendar.date(byAdding: .year, value: 1, to: now) ?? now
                }

                print("🎉 [SubscriptionCore Live] 真实购买成功，过期时间: \(expiryDate)")
                return .premium(expiryDate: expiryDate, usageLimits: product.usageLimits)
              } else {
                // 单次购买
                let expiryDate = Date().addingTimeInterval(24 * 60 * 60)
                return .premium(expiryDate: expiryDate, usageLimits: product.usageLimits)
              }

            case .userCancelled:
              print("❌ [SubscriptionCore Live] 用户取消购买")
              throw StoreKitError.userCancelled

            case .pending:
              print("⏳ [SubscriptionCore Live] 购买待处理")
              throw StoreKitError.purchasePending

            @unknown default:
              print("❓ [SubscriptionCore Live] 未知购买结果")
              throw StoreKitError.unknown
            }
          } catch {
            print("❌ [SubscriptionCore Live] 购买失败: \(error)")
            throw error
          }
        },
        restorePurchases: {
          print("🔄 [SubscriptionCore Live] 恢复购买...")

          do {
            // 同步最新的交易状态
            try await AppStore.sync()

            // 检查当前的订阅状态
            let currentEntitlements = await Transaction.currentEntitlements

            for await verification in currentEntitlements {
              let transaction = try checkVerified(verification)

              if let expirationDate = transaction.expirationDate,
                 expirationDate > Date() {
                print("✅ [SubscriptionCore Live] 找到有效订阅，过期时间: \(expirationDate)")
                return .premium(expiryDate: expirationDate, usageLimits: UserStateCore.UsageLimits.yearlyStandard)
              }
            }

            print("ℹ️ [SubscriptionCore Live] 未找到有效订阅")
            return .free
          } catch {
            print("❌ [SubscriptionCore Live] 恢复购买失败: \(error)")
            throw error
          }
        },
        checkSubscriptionStatus: {
          print("🔍 [SubscriptionCore Live] 检查订阅状态...")

          // 检查当前的订阅状态
          let currentEntitlements = await Transaction.currentEntitlements

          for await verification in currentEntitlements {
            do {
              let transaction = try checkVerified(verification)

              if let expirationDate = transaction.expirationDate,
                 expirationDate > Date() {
                print("✅ [SubscriptionCore Live] 找到有效订阅，过期时间: \(expirationDate)")
                return .premium(expiryDate: expirationDate, usageLimits: UserStateCore.UsageLimits.yearlyStandard)
              }
            } catch {
              print("⚠️ [SubscriptionCore Live] 验证交易失败: \(error)")
            }
          }

          print("ℹ️ [SubscriptionCore Live] 无有效订阅")
          return .free
        }
      )
    } else {
      print("⚠️ SubscriptionCore: iOS版本过低，使用模拟实现")
      return mockStoreKitClient()
    }
  }()
  
  public static let testValue = StoreKitClient(
    loadProducts: {
      return [
        SubscriptionProduct(
          id: "test.monthly",
          displayName: "Test Monthly",
          description: "Test monthly subscription",
          price: "$9.99",
          priceValue: 9.99,
          currencyCode: "USD",
          productType: .subscription,
          duration: .monthly,
          usageLimits: UserStateCore.UsageLimits.monthlyBasic
        )
      ]
    },
    purchase: { product in
      if let duration = product.duration {
        let calendar = Calendar.current
        let now = Date()
        let expiryDate: Date
        switch duration {
        case .monthly:
          expiryDate = calendar.date(byAdding: .month, value: 1, to: now) ?? now
        case .quarterly:
          expiryDate = calendar.date(byAdding: .month, value: 3, to: now) ?? now
        case .yearly:
          expiryDate = calendar.date(byAdding: .year, value: 1, to: now) ?? now
        }
        return .premium(expiryDate: expiryDate, usageLimits: product.usageLimits)
      } else {
        let expiryDate = Date().addingTimeInterval(24 * 60 * 60)
        return .premium(expiryDate: expiryDate, usageLimits: product.usageLimits)
      }
    },
    restorePurchases: { .free },
    checkSubscriptionStatus: { .free }
  )
}

// MARK: - Helper Functions for Product Details

private func getProductType(for productId: String) -> ProductType {
  switch productId {
  case "com.wenhaofree.bridal.single_basic":
    return .singlePurchase
  default:
    return .subscription
  }
}

private func getDuration(for productId: String) -> SubscriptionDuration? {
  switch productId {
  case "com.wenhaofree.bridal.sub_monthly_40":
    return .monthly
  case "com.wenhaofree.bridal.sub_yearly_600":
    return .yearly
  default:
    return nil
  }
}

private func getUsageLimits(for productId: String) -> UserStateCore.UsageLimits {
  switch productId {
  case "com.wenhaofree.bridal.single_basic":
    return UserStateCore.UsageLimits.singleUse
  case "com.wenhaofree.bridal.sub_monthly_40":
    return UserStateCore.UsageLimits.monthlyPremium
  case "com.wenhaofree.bridal.sub_yearly_600":
    return UserStateCore.UsageLimits.yearlyStandard
  default:
    return UserStateCore.UsageLimits.monthlyPremium
  }
}

// MARK: - Helper Functions

private func mockStoreKitClient() -> StoreKitClient {
  return StoreKitClient(
    loadProducts: {
      return mockProducts()
    },
    purchase: { product in
      if let duration = product.duration {
        let expiryDate = Date().addingTimeInterval(duration.duration)
        return .premium(expiryDate: expiryDate, usageLimits: product.usageLimits)
      } else {
        let expiryDate = Date().addingTimeInterval(24 * 60 * 60)
        return .premium(expiryDate: expiryDate, usageLimits: product.usageLimits)
      }
    },
    restorePurchases: {
      return .free
    },
    checkSubscriptionStatus: {
      return .free
    }
  )
}

private func mockProducts() -> [SubscriptionProduct] {
  return [
    // Single Purchase Option - 1元一次生成
    SubscriptionProduct(
      id: "com.wenhaofree.bridal.single_basic",
      displayName: "单次生成",
      description: "1次高清图片生成",
      price: "¥1",
      priceValue: 1.00,
      currencyCode: "CNY",
      productType: .singlePurchase,
      usageLimits: UserStateCore.UsageLimits.singleUse
    ),
    // Monthly Subscription - 28元月度方案
    SubscriptionProduct(
      id: "com.wenhaofree.bridal.sub_monthly_40",
      displayName: "高级月度订阅",
      description: "每月40次生成，解锁所有功能",
      price: "¥28",
      priceValue: 28.00,
      currencyCode: "CNY",
      productType: .subscription,
      duration: .monthly,
      usageLimits: UserStateCore.UsageLimits.monthlyPremium,
      isPopular: true
    ),
    // Yearly Subscription - 128元年度方案
    SubscriptionProduct(
      id: "com.wenhaofree.bridal.sub_yearly_600",
      displayName: "年度订阅",
      description: "每年600次生成，享受最大优惠",
      price: "¥128",
      priceValue: 128.00,
      currencyCode: "CNY",
      productType: .subscription,
      duration: .yearly,
      usageLimits: UserStateCore.UsageLimits.yearlyStandard
    )
  ]
}

@DependencyClient
public struct SubscriptionStorage: Sendable {
  public var getSubscriptionStatus: @Sendable () async -> UserStateCore.SubscriptionStatus = { .free }
  public var saveSubscriptionStatus: @Sendable (UserStateCore.SubscriptionStatus) async -> Void = { _ in }
  public var clearSubscriptionStatus: @Sendable () async -> Void = { }
}

extension SubscriptionStorage: DependencyKey {
  public static let liveValue = SubscriptionStorage(
    getSubscriptionStatus: {
      // TODO: Implement with Keychain storage
      return .free
    },
    saveSubscriptionStatus: { _ in
      // TODO: Implement with Keychain storage
    },
    clearSubscriptionStatus: {
      // TODO: Implement with Keychain storage
    }
  )
  
  public static let testValue = SubscriptionStorage(
    getSubscriptionStatus: { .free },
    saveSubscriptionStatus: { _ in },
    clearSubscriptionStatus: { }
  )
}

extension DependencyValues {
  public var storeKitClient: StoreKitClient {
    get { self[StoreKitClient.self] }
    set { self[StoreKitClient.self] = newValue }
  }
  
  public var subscriptionStorage: SubscriptionStorage {
    get { self[SubscriptionStorage.self] }
    set { self[SubscriptionStorage.self] = newValue }
  }
}


// MARK: - Logging Category Extension

public extension LoggingClient {
  enum SubscriptionCategory: String, CaseIterable {
    case subscription = "Subscription"
  }
}

extension LoggingClient.SubscriptionCategory {
  public var category: String {
    return self.rawValue
  }
}