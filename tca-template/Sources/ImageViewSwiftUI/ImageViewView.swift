import SwiftUI
import ComposableArchitecture
import CommonUI
import ImageViewCore
import ImageGenerationCore

#if canImport(UIKit)
import UIKit
#endif

public struct ImageViewView: View {
  let store: StoreOf<ImageView>
  @State private var downloadedImage: Image?
  @State private var downloadedImageData: Data?  // Store the actual image data
  @State private var isLoading = false
  @State private var loadError: String?

  public init(store: StoreOf<ImageView>) {
    self.store = store
  }
  
  public var body: some View {
    WithPerceptionTracking {
      ZStack {
        Color.black.ignoresSafeArea()
        
        VStack(spacing: 0) {
          // Navigation header
          headerSection
          
          // Main image display
          imageSection
          
          // Bottom actions
          bottomActions
        }
      }
      #if os(iOS)
      .navigationBarHidden(true)
      #endif
      .onAppear {
        store.send(.onAppear)
        loadImageFromURL()
      }
      .alert("图片已保存！", isPresented: .constant(store.showingSaveSuccess)) {
        Button("确定") {
          store.send(.dismissSaveSuccess)
        }
      } message: {
        Text("您的图片已成功保存到相册中。")
      }
    }
  }
  
  private var headerSection: some View {
    HStack {
      Button(action: { store.send(.backToTypeSelection) }) {
        Image(systemName: "chevron.left")
          .font(.title2)
          .foregroundColor(.white)
      }
      
      Spacer()
      
      VStack {
        Text("Your Generated Image")
          .font(.headline)
          .foregroundColor(.white)
        
        Text(store.generatedImage.templateName)
          .font(.caption)
          .foregroundColor(.white.opacity(0.7))
      }
      
      Spacer()
      
      Button(action: { store.send(.toggleImageDetails) }) {
        Image(systemName: "info.circle")
          .font(.title2)
          .foregroundColor(.white)
      }
    }
    .padding()
    .background(Color.black.opacity(0.3))
  }
  
  private var imageSection: some View {
    ZStack {
      // 实际图片显示
      if let downloadedImage = downloadedImage {
        downloadedImage
          .resizable()
          .aspectRatio(contentMode: .fit)
          .cornerRadius(20)
          .scaleEffect(store.zoomScale)
          .gesture(
            MagnificationGesture()
              .onChanged { value in
                store.send(.zoomChanged(value))
              }
          )
      } else if isLoading {
        // 加载中状态
        RoundedRectangle(cornerRadius: 20)
          .fill(Color.gray.opacity(0.3))
          .aspectRatio(3/4, contentMode: .fit)
          .overlay(
            VStack(spacing: 16) {
              ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                .scaleEffect(1.5)

              Text("Loading Image...")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.white)

              Text(store.generatedImage.templateName)
                .font(.caption)
                .foregroundColor(.white.opacity(0.8))
            }
          )
      } else if let loadError = loadError {
        // 错误状态
        RoundedRectangle(cornerRadius: 20)
          .fill(Color.red.opacity(0.3))
          .aspectRatio(3/4, contentMode: .fit)
          .overlay(
            VStack(spacing: 16) {
              Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 60))
                .foregroundColor(.red)

              Text("Failed to Load Image")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.white)

              Text(loadError)
                .font(.caption)
                .foregroundColor(.white.opacity(0.8))
                .multilineTextAlignment(.center)
                .padding(.horizontal)

              Button("Retry") {
                loadImageFromURL()
              }
              .foregroundColor(.white)
              .padding(.horizontal, 16)
              .padding(.vertical, 8)
              .background(Color.red.opacity(0.6))
              .cornerRadius(8)
            }
          )
      } else {
        // 默认占位符
        RoundedRectangle(cornerRadius: 20)
          .fill(
            LinearGradient(
              colors: [.accentPink, .accentPurple, .accentBlue],
              startPoint: .topLeading,
              endPoint: .bottomTrailing
            )
          )
          .aspectRatio(3/4, contentMode: .fit)
          .overlay(
            VStack {
              Image(systemName: "photo.fill")
                .font(.system(size: 60))
                .foregroundColor(.white.opacity(0.8))

              Text("Generated Image")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.white)

              Text(store.generatedImage.templateName)
                .font(.caption)
                .foregroundColor(.white.opacity(0.8))
            }
          )
      }
      
      // Image details overlay
      if store.showingImageDetails {
        VStack {
          Spacer()
          
          ModernDarkCard {
            VStack(alignment: .leading, spacing: 12) {
              Text("Image Details")
                .font(.headline)
                .foregroundColor(.white)
              
              detailRow("Template", store.generatedImage.templateName)
              detailRow("Generated", formatDate(store.generatedImage.generatedAt))
              detailRow("Processing Time", "\(Int(store.generatedImage.processingTime))s")
              detailRow("Image ID", String(store.generatedImage.id.prefix(8)))
            }
          }
          .padding()
        }
      }
    }
    .padding()
  }
  
  private var bottomActions: some View {
    VStack(spacing: 16) {
      // Error messages
      if let saveError = store.saveError {
        Text("Save Error: \(saveError)")
          .font(.caption)
          .foregroundColor(.red)
          .padding(.horizontal)
          .multilineTextAlignment(.center)
      }

      if let shareError = store.shareError {
        Text("Share Error: \(shareError)")
          .font(.caption)
          .foregroundColor(.red)
          .padding(.horizontal)
          .multilineTextAlignment(.center)
      }

      // Loading state message
      if downloadedImageData == nil && !isLoading {
        Text("Image must be loaded before saving")
          .font(.caption)
          .foregroundColor(.yellow)
          .padding(.horizontal)
          .multilineTextAlignment(.center)
      }
      
      // Action buttons
      HStack(spacing: 20) {
        // Save button
        PrimaryButton(
          title: store.isSaving ? "Saving..." : "Save",
          style: .outline,
          isLoading: store.isSaving,
          isDisabled: store.isSaving || downloadedImageData == nil
        ) {
          store.send(.saveImage(downloadedImageData))
        }
        .foregroundColor(.white)
        
        // Share button
        PrimaryButton(
          title: store.isSharing ? "Sharing..." : "Share",
          isLoading: store.isSharing,
          isDisabled: store.isSharing || downloadedImageData == nil
        ) {
          store.send(.shareImage(downloadedImageData))
        }
      }
      .padding(.horizontal, 40)
      
      // Back to selection
      Button("Create Another Image") {
        store.send(.backToTypeSelection)
      }
      .font(.body)
      .foregroundColor(.white.opacity(0.7))
      .padding(.top, 8)
    }
    .padding()
    .background(
      LinearGradient(
        colors: [Color.clear, Color.black.opacity(0.7)],
        startPoint: .top,
        endPoint: .bottom
      )
    )
  }
  
  private func detailRow(_ label: String, _ value: String) -> some View {
    HStack {
      Text(label)
        .font(.caption)
        .foregroundColor(.white.opacity(0.7))
      
      Spacer()
      
      Text(value)
        .font(.caption)
        .foregroundColor(.white)
    }
  }
  
  private func formatDate(_ date: Date) -> String {
    let formatter = DateFormatter()
    formatter.dateStyle = .short
    formatter.timeStyle = .short
    return formatter.string(from: date)
  }

  private func loadImageFromURL() {
    guard let urlString = store.generatedImage.imageUrl,
          let url = URL(string: urlString),
          downloadedImage == nil else {
      print("⚠️ [ImageView] No image URL available or image already loaded")
      return
    }

    print("🔄 [ImageView] Loading image from URL: \(urlString)")
    isLoading = true
    loadError = nil

    Task {
      do {
        let (data, _) = try await URLSession.shared.data(from: url)
        await MainActor.run {
          #if canImport(UIKit)
          if let uiImage = UIKit.UIImage(data: data) {
            self.downloadedImage = Image(uiImage: uiImage)
            self.downloadedImageData = data  // Store the raw image data
            print("✅ [ImageView] Image loaded successfully from: \(urlString)")
          } else {
            self.loadError = "Failed to create image from data"
            print("❌ [ImageView] Failed to create UIImage from data")
          }
          #else
          // For other platforms, just mark as loaded
          self.downloadedImage = Image(systemName: "photo.fill")
          self.downloadedImageData = data  // Store the data even on other platforms
          print("✅ [ImageView] Image placeholder loaded for: \(urlString)")
          #endif
          self.isLoading = false
        }
      } catch {
        await MainActor.run {
          self.loadError = error.localizedDescription
          self.isLoading = false
          print("❌ [ImageView] Failed to load image: \(error)")
        }
      }
    }
  }
}

#Preview {
  ImageViewView(
    store: Store(
      initialState: ImageView.State(
        generatedImage: GeneratedImage(
          id: "test-id",
          templateId: "romantic-wedding",
          templateName: "Romantic Wedding",
          imageData: Data(),
          generatedAt: Date(),
          processingTime: 35.0
        )
      )
    ) {
      ImageView()
    }
  )
}