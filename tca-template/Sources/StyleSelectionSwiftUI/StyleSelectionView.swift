import SwiftUI
import ComposableArchitecture
import StyleSelectionCore
import CommonUI

// MARK: - Style Selection View

public struct StyleSelectionView: View {
  let store: StoreOf<StyleSelection>
  @State private var selectedCategory: StyleCategory = .all
  
  public init(store: StoreOf<StyleSelection>) {
    self.store = store
  }
  
  public var body: some View {
    NavigationView {
      VStack(spacing: 0) {
        headerSection
        
        categoryFilterSection
        
        if store.isLoading {
          loadingSection
        } else {
          stylesGridSection
        }
        
        if store.canProceed {
          actionButtonSection
        }
      }
      .navigationTitle("选择风格")
      #if os(iOS)
      .navigationBarTitleDisplayMode(.large)
      #endif
      .onAppear {
        store.send(.onAppear)
      }
      .alert(
        item: Binding(
          get: { store.error.map(ErrorWrapper.init) },
          set: { _ in store.send(.dismissError) }
        )
      ) { errorWrapper in
        Alert(
          title: Text("提示"),
          message: Text(errorWrapper.error.localizedDescription),
          dismissButton: .default(Text("确定"))
        )
      }
      .sheet(isPresented: Binding(
        get: { store.isShowingCustomPrompt },
        set: { _ in store.send(.hideCustomPrompt) }
      )) {
        CustomPromptSheet(store: store)
      }
      .sheet(isPresented: Binding(
        get: { store.isShowingGenerationConfirmation },
        set: { _ in store.send(.hideGenerationConfirmation) }
      )) {
        GenerationConfirmationSheet(store: store)
      }
    }
  }
  
  // MARK: - Header Section
  
  private var headerSection: some View {
    VStack(spacing: 12) {
      Image(systemName: "paintbrush.pointed")
        .font(.system(size: 40))
        .foregroundStyle(
          LinearGradient(
            colors: [.pink, .purple],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
          )
        )
      
      VStack(spacing: 4) {
        Text("选择您喜欢的风格")
          .font(.title3)
          .fontWeight(.semibold)
        
        Text("AI将根据您的选择生成专属婚纱照")
          .font(.caption)
          .foregroundColor(.secondary)
      }
    }
    .padding(.vertical, 16)
    .padding(.horizontal, 20)
  }
  
  // MARK: - Category Filter Section
  
  private var categoryFilterSection: some View {
    ScrollView(.horizontal, showsIndicators: false) {
      HStack(spacing: 12) {
        ForEach(StyleCategory.allCases, id: \.self) { category in
          CategoryChip(
            category: category,
            isSelected: selectedCategory == category
          ) {
            selectedCategory = category
          }
        }
      }
      .padding(.horizontal, 20)
    }
    .padding(.vertical, 8)
  }
  
  // MARK: - Loading Section
  
  private var loadingSection: some View {
    VStack(spacing: 16) {
      ProgressView()
        .scaleEffect(1.2)
      
      Text("加载风格中...")
        .font(.subheadline)
        .foregroundColor(.secondary)
    }
    .frame(maxWidth: .infinity, maxHeight: .infinity)
  }
  
  // MARK: - Styles Grid Section
  
  private var stylesGridSection: some View {
    ScrollView {
      LazyVGrid(
        columns: Array(repeating: GridItem(.flexible(), spacing: 16), count: 2),
        spacing: 16
      ) {
        ForEach(filteredStyles) { style in
          StyleCard(
            style: style,
            isSelected: store.selectedStyle?.id == style.id
          ) {
            store.send(.styleSelected(style))
          }
        }
      }
      .padding(.horizontal, 20)
      .padding(.bottom, 100) // Space for action button
    }
  }
  
  // MARK: - Action Button Section
  
  private var actionButtonSection: some View {
    VStack(spacing: 0) {
      Divider()
      
      VStack(spacing: 16) {
        if let selectedStyle = store.selectedStyle {
          HStack {
            VStack(alignment: .leading, spacing: 4) {
              Text("已选择: \(selectedStyle.name)")
                .font(.headline)
              
              if selectedStyle.isPremium {
                HStack {
                  Image(systemName: "crown.fill")
                    .foregroundColor(.yellow)
                  Text("VIP专享")
                    .font(.caption)
                    .foregroundColor(.secondary)
                }
              }
            }
            
            Spacer()
          }
        }
        
        Button(action: {
          store.send(.showGenerationConfirmation)
        }) {
          HStack {
            Text("开始生成")
              .font(.headline)
            
            Spacer()
            
            Image(systemName: "arrow.right")
              .font(.headline)
          }
          .foregroundColor(.white)
          .padding(.horizontal, 24)
          .padding(.vertical, 16)
          .background(
            LinearGradient(
              colors: [.pink, .purple],
              startPoint: .leading,
              endPoint: .trailing
            )
          )
          .cornerRadius(12)
        }
      }
      .padding(.horizontal, 20)
      .padding(.vertical, 16)
      .background(Color.white)
    }
  }
  
  // MARK: - Computed Properties
  
  private var filteredStyles: [WeddingStyle] {
    let styles = store.availableStyles
    
    switch selectedCategory {
    case .all:
      return styles
    case .premium:
      return styles.filter { $0.isPremium }
    case .classic:
      return styles.filter { $0.tags.contains("经典") }
    case .romantic:
      return styles.filter { $0.tags.contains("浪漫") }
    case .modern:
      return styles.filter { $0.tags.contains("现代") }
    case .vintage:
      return styles.filter { $0.tags.contains("复古") }
    case .outdoor:
      return styles.filter { $0.tags.contains("户外") || $0.tags.contains("海滩") || $0.tags.contains("森林") }
    }
  }
}

// MARK: - Supporting Views

struct CategoryChip: View {
  let category: StyleCategory
  let isSelected: Bool
  let action: () -> Void
  
  var body: some View {
    Button(action: action) {
      HStack(spacing: 6) {
        Image(systemName: category.systemImage)
          .font(.caption)
        
        Text(category.displayName)
          .font(.caption)
          .fontWeight(.medium)
      }
      .padding(.horizontal, 12)
      .padding(.vertical, 8)
      .background(
        RoundedRectangle(cornerRadius: 20)
          .fill(isSelected ? Color.pink : Color.gray.opacity(0.1))
      )
      .foregroundColor(isSelected ? .white : .primary)
    }
  }
}

struct StyleCard: View {
  let style: WeddingStyle
  let isSelected: Bool
  let action: () -> Void
  
  var body: some View {
    Button(action: action) {
      VStack(spacing: 0) {
        // Preview Image
        ZStack {
          RoundedRectangle(cornerRadius: 12)
            .fill(
              LinearGradient(
                colors: [.pink.opacity(0.3), .purple.opacity(0.3)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
              )
            )
            .frame(height: 140)
          
          if style.isCustom {
            VStack(spacing: 8) {
              Image(systemName: "plus.circle")
                .font(.system(size: 32))
                .foregroundColor(.primary)
              
              Text("自定义")
                .font(.caption)
                .fontWeight(.medium)
            }
          } else {
            Image(systemName: "photo")
              .font(.system(size: 32))
              .foregroundColor(.primary)
          }
          
          // Premium Badge
          if style.isPremium {
            VStack {
              HStack {
                Spacer()
                
                HStack(spacing: 4) {
                  Image(systemName: "crown.fill")
                    .font(.caption2)
                  Text("VIP")
                    .font(.caption2)
                    .fontWeight(.bold)
                }
                .foregroundColor(.white)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(Color.yellow)
                .cornerRadius(8)
                .padding(8)
              }
              
              Spacer()
            }
          }
          
          // Selection Indicator
          if isSelected {
            VStack {
              Spacer()
              HStack {
                Spacer()
                
                Image(systemName: "checkmark.circle.fill")
                  .font(.title2)
                  .foregroundColor(.white)
                  .background(Color.pink)
                  .clipShape(Circle())
                  .padding(8)
              }
            }
          }
        }
        
        // Style Info
        VStack(alignment: .leading, spacing: 4) {
          Text(style.name)
            .font(.headline)
            .fontWeight(.semibold)
            .multilineTextAlignment(.leading)
          
          Text(style.description)
            .font(.caption)
            .foregroundColor(.secondary)
            .multilineTextAlignment(.leading)
            .lineLimit(2)
          
          // Tags
          if !style.tags.isEmpty {
            ScrollView(.horizontal, showsIndicators: false) {
              HStack(spacing: 4) {
                ForEach(style.tags.prefix(2), id: \.self) { tag in
                  Text(tag)
                    .font(.caption2)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color.gray.opacity(0.2))
                    .cornerRadius(4)
                }
              }
            }
          }
        }
        .padding(12)
        .frame(maxWidth: .infinity, alignment: .leading)
      }
      .background(
        RoundedRectangle(cornerRadius: 16)
          .fill(Color.white)
          .shadow(
            color: isSelected ? .pink.opacity(0.3) : .black.opacity(0.1),
            radius: isSelected ? 8 : 4,
            x: 0,
            y: 2
          )
      )
      .overlay(
        RoundedRectangle(cornerRadius: 16)
          .stroke(isSelected ? Color.pink : Color.clear, lineWidth: 2)
      )
    }
    .buttonStyle(PlainButtonStyle())
  }
}

struct CustomPromptSheet: View {
  let store: StoreOf<StyleSelection>
  
  var body: some View {
    NavigationView {
      VStack(spacing: 24) {
        VStack(spacing: 16) {
          Image(systemName: "wand.and.stars")
            .font(.system(size: 40))
            .foregroundStyle(
              LinearGradient(
                colors: [.pink, .purple],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
              )
            )
          
          VStack(spacing: 8) {
            Text("自定义风格")
              .font(.title2)
              .fontWeight(.bold)
            
            Text("描述您想要的婚纱照风格，AI将为您创造独特的作品")
              .font(.subheadline)
              .foregroundColor(.secondary)
              .multilineTextAlignment(.center)
          }
        }
        
        VStack(alignment: .leading, spacing: 8) {
          Text("风格描述")
            .font(.headline)
          
          TextEditor(text: Binding(
            get: { store.customPrompt },
            set: { store.send(.customPromptChanged($0)) }
          ))
            .frame(minHeight: 120)
            .padding(12)
            .background(Color.gray.opacity(0.1))
            .cornerRadius(12)
          
          Text("例如：海边日落，轻纱飘逸，温暖光线")
            .font(.caption)
            .foregroundColor(.secondary)
        }
        
        Spacer()
        
        Button("确认选择") {
          store.send(.hideCustomPrompt)
        }
        .font(.headline)
        .foregroundColor(.white)
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(
          LinearGradient(
            colors: [.pink, .purple],
            startPoint: .leading,
            endPoint: .trailing
          )
        )
        .cornerRadius(12)
        .disabled(store.customPrompt.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
      }
      .padding(20)
      .navigationTitle("自定义风格")
      #if os(iOS)
      .navigationBarTitleDisplayMode(.inline)
      #endif
      .toolbar {
        ToolbarItem(placement: .automatic) {
          Button("取消") {
            store.send(.hideCustomPrompt)
          }
        }
      }
    }
  }
}

// MARK: - Generation Confirmation Sheet

struct GenerationConfirmationSheet: View {
  let store: StoreOf<StyleSelection>

  var body: some View {
    NavigationView {
      VStack(spacing: 24) {
        // Header Section
        VStack(spacing: 12) {
          Image(systemName: "sparkles")
            .font(.system(size: 48))
            .foregroundColor(.pink)

          Text("确认生成")
            .font(.title2)
            .fontWeight(.bold)

          Text("即将为您生成专属的婚纱照片")
            .font(.subheadline)
            .foregroundColor(.secondary)
            .multilineTextAlignment(.center)
        }

        // Selected Style Section
        if let selectedStyle = store.selectedStyle {
          VStack(alignment: .leading, spacing: 16) {
            Text("选择的风格")
              .font(.headline)

            HStack(spacing: 16) {
              // Style Preview
              ZStack {
                RoundedRectangle(cornerRadius: 12)
                  .fill(
                    LinearGradient(
                      colors: [.pink.opacity(0.3), .purple.opacity(0.3)],
                      startPoint: .topLeading,
                      endPoint: .bottomTrailing
                    )
                  )
                  .frame(width: 80, height: 80)

                if selectedStyle.isCustom {
                  Image(systemName: "plus.circle")
                    .font(.system(size: 24))
                    .foregroundColor(.primary)
                } else {
                  Image(systemName: "photo")
                    .font(.system(size: 24))
                    .foregroundColor(.primary)
                }
              }

              // Style Details
              VStack(alignment: .leading, spacing: 4) {
                HStack {
                  Text(selectedStyle.name)
                    .font(.headline)

                  if selectedStyle.isPremium {
                    Image(systemName: "crown.fill")
                      .foregroundColor(.yellow)
                      .font(.caption)
                  }
                }

                Text(selectedStyle.description)
                  .font(.subheadline)
                  .foregroundColor(.secondary)
                  .lineLimit(2)

                if selectedStyle.isCustom && !store.customPrompt.isEmpty {
                  Text("自定义描述: \(store.customPrompt)")
                    .font(.caption)
                    .foregroundColor(.blue)
                    .lineLimit(3)
                }
              }

              Spacer()
            }
            .padding(16)
            .background(Color.gray.opacity(0.05))
            .cornerRadius(12)
          }
        }

        // Generation Info Section
        VStack(alignment: .leading, spacing: 12) {
          Text("生成信息")
            .font(.headline)

          VStack(spacing: 8) {
            HStack {
              Image(systemName: "clock")
                .foregroundColor(.blue)
              Text("预计时间: 30-60秒")
                .font(.subheadline)
              Spacer()
            }

            HStack {
              Image(systemName: "photo.on.rectangle")
                .foregroundColor(.green)
              Text("将生成高质量婚纱照片")
                .font(.subheadline)
              Spacer()
            }

            HStack {
              Image(systemName: "checkmark.circle")
                .foregroundColor(.orange)
              Text("可保存到相册")
                .font(.subheadline)
              Spacer()
            }
          }
          .padding(16)
          .background(Color.gray.opacity(0.05))
          .cornerRadius(12)
        }

        Spacer()

        // Action Buttons
        VStack(spacing: 12) {
          Button("确认生成") {
            store.send(.confirmGeneration)
          }
          .font(.headline)
          .foregroundColor(.white)
          .frame(maxWidth: .infinity)
          .padding(.vertical, 16)
          .background(
            LinearGradient(
              colors: [.pink, .purple],
              startPoint: .leading,
              endPoint: .trailing
            )
          )
          .cornerRadius(12)

          Button("取消") {
            store.send(.hideGenerationConfirmation)
          }
          .font(.subheadline)
          .foregroundColor(.secondary)
        }
      }
      .padding(20)
      .navigationTitle("确认生成")
      #if os(iOS)
      .navigationBarTitleDisplayMode(.inline)
      #endif
      .toolbar {
        ToolbarItem(placement: .automatic) {
          Button("取消") {
            store.send(.hideGenerationConfirmation)
          }
        }
      }
    }
  }
}

// MARK: - Error Wrapper

struct ErrorWrapper: Identifiable {
  let id = UUID()
  let error: StyleSelectionError
}

// MARK: - Previews

#Preview("Style Selection") {
  StyleSelectionView(
    store: Store(initialState: StyleSelection.State()) {
      StyleSelection()
    }
  )
}

#Preview("With Selection") {
  StyleSelectionView(
    store: Store(
      initialState: StyleSelection.State(
        selectedStyle: WeddingStyle.mockStyles.first
      )
    ) {
      StyleSelection()
    }
  )
}
