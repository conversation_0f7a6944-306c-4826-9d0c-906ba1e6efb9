import SwiftUI
import ComposableArchitecture
import NotificationSettingsCore
import CommonUI
import Perception

public struct NotificationSettingsView: View {
  @Perception.Bindable var store: StoreOf<NotificationSettings>
  
  public init(store: StoreOf<NotificationSettings>) {
    self.store = store
  }
  
  public var body: some View {
    WithPerceptionTracking {
      ScrollView {
        LazyVStack(spacing: 20) {
          // Permission status card
          permissionStatusCard
          
          // Notification preferences
          if store.hasNotificationPermission {
            notificationPreferencesSection
          }
          
          // Bottom spacing
          Spacer(minLength: 100)
        }
        .padding(.horizontal, 16)
        .padding(.top, 20)
      }
      .background(Color.gray.opacity(0.1))
      .navigationTitle("通知设置")
      #if os(iOS)
      .navigationBarTitleDisplayMode(.large)
      #endif
      .onAppear {
        store.send(.onAppear)
      }
      .alert(
        "错误",
        isPresented: Binding(
          get: { store.error != nil },
          set: { _ in store.send(.clearError) }
        )
      ) {
        Button("确定") {
          store.send(.clearError)
        }
        Button("打开设置") {
          store.send(.openSystemSettings)
        }
      } message: {
        Text(store.error ?? "")
      }
    }
  }
  
  // MARK: - Permission Status Card
  
  @ViewBuilder
  private var permissionStatusCard: some View {
    VStack(spacing: 16) {
      // Card title
      HStack {
        Text("通知权限")
          .font(.system(size: 18, weight: .semibold))
          .foregroundColor(.primary)
        Spacer()
      }
      
      // Permission status
      HStack(spacing: 12) {
        ZStack {
          Circle()
            .fill(permissionStatusColor)
            .frame(width: 32, height: 32)
          
          Image(systemName: permissionStatusIcon)
            .font(.system(size: 14, weight: .bold))
            .foregroundColor(.white)
        }
        
        VStack(alignment: .leading, spacing: 2) {
          Text("推送通知")
            .font(.system(size: 16, weight: .medium))
            .foregroundColor(.primary)
          
          Text(permissionStatusText)
            .font(.system(size: 14))
            .foregroundColor(permissionStatusColor)
        }
        
        Spacer()
        
        if store.isCheckingPermission {
          ProgressView()
            .scaleEffect(0.8)
        } else if !store.hasNotificationPermission {
          Button(action: {
            if store.hasNotificationPermission {
              store.send(.openSystemSettings)
            } else {
              store.send(.requestNotificationPermission)
            }
          }) {
            HStack(spacing: 4) {
              if store.isLoading {
                ProgressView()
                  .scaleEffect(0.8)
                  .progressViewStyle(CircularProgressViewStyle(tint: .white))
              }
              Text(store.isLoading ? "请求中..." : "开启")
            }
          }
          .font(.system(size: 14, weight: .semibold))
          .foregroundColor(.white)
          .padding(.horizontal, 16)
          .padding(.vertical, 8)
          .background(store.isLoading ? Color.gray : Color.blue)
          .cornerRadius(8)
          .disabled(store.isLoading)
        }
      }
      
      if !store.hasNotificationPermission {
        Divider()
        
        // Help text
        VStack(alignment: .leading, spacing: 8) {
          Text("为什么需要通知权限？")
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(.primary)
          
          Text("• 照片生成完成时及时通知您\n• 接收重要的应用更新信息\n• 获得个性化的使用提示")
            .font(.system(size: 13))
            .foregroundColor(.secondary)
            .lineSpacing(2)
        }
        
        // Manual settings button
        Button(action: {
          store.send(.openSystemSettings)
        }) {
          HStack {
            Image(systemName: "gear")
              .font(.system(size: 14))
            Text("在系统设置中手动开启")
              .font(.system(size: 14, weight: .medium))
          }
          .foregroundColor(.blue)
        }
      }
    }
    .padding(20)
    .background(Color.white)
    .cornerRadius(16)
  }
  
  // MARK: - Notification Preferences Section
  
  @ViewBuilder
  private var notificationPreferencesSection: some View {
    VStack(spacing: 0) {
      // Section title
      HStack {
        Text("通知类型")
          .font(.system(size: 18, weight: .semibold))
          .foregroundColor(.primary)
        Spacer()
      }
      .padding(.bottom, 12)
      
      // Preferences list
      VStack(spacing: 0) {
        NotificationToggleRow(
          icon: "checkmark.circle.fill",
          iconColor: .green,
          title: "生成完成",
          subtitle: "照片处理完成时通知",
          isOn: Binding(
            get: { store.enableGenerationComplete },
            set: { _ in store.send(.toggleGenerationComplete) }
          )
        )
        
        NotificationDivider()
        
        NotificationToggleRow(
          icon: "gift.fill",
          iconColor: .purple,
          title: "优惠活动",
          subtitle: "特价和促销活动通知",
          isOn: Binding(
            get: { store.enablePromotions },
            set: { _ in store.send(.togglePromotions) }
          )
        )
        
        NotificationDivider()
        
        NotificationToggleRow(
          icon: "lightbulb.fill",
          iconColor: .orange,
          title: "使用技巧",
          subtitle: "应用功能和使用建议",
          isOn: Binding(
            get: { store.enableTips },
            set: { _ in store.send(.toggleTips) }
          )
        )
        
        NotificationDivider()
        
        NotificationToggleRow(
          icon: "calendar.circle.fill",
          iconColor: .blue,
          title: "每周摘要",
          subtitle: "每周使用统计和精选内容",
          isOn: Binding(
            get: { store.enableWeeklyDigest },
            set: { _ in store.send(.toggleWeeklyDigest) }
          )
        )
      }
      .background(Color.white)
      .cornerRadius(16)
    }
  }
}

// MARK: - Helper Properties

extension NotificationSettingsView {
  private var permissionStatusColor: Color {
    store.hasNotificationPermission ? .green : .orange
  }
  
  private var permissionStatusIcon: String {
    store.hasNotificationPermission ? "checkmark" : "exclamationmark"
  }
  
  private var permissionStatusText: String {
    store.hasNotificationPermission ? "已开启" : "未开启"
  }
}

// MARK: - Notification Toggle Row Component

struct NotificationToggleRow: View {
  let icon: String
  let iconColor: Color
  let title: String
  let subtitle: String
  @Binding var isOn: Bool
  
  var body: some View {
    HStack(spacing: 12) {
      // Icon
      ZStack {
        RoundedRectangle(cornerRadius: 6)
          .fill(iconColor)
          .frame(width: 28, height: 28)
        
        Image(systemName: icon)
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(.white)
      }
      
      // Title and subtitle
      VStack(alignment: .leading, spacing: 2) {
        Text(title)
          .font(.system(size: 16, weight: .medium))
          .foregroundColor(.primary)
        
        Text(subtitle)
          .font(.system(size: 13))
          .foregroundColor(.secondary)
      }
      
      Spacer()
      
      // Toggle
      Toggle("", isOn: $isOn)
        .labelsHidden()
    }
    .padding(.horizontal, 16)
    .padding(.vertical, 12)
  }
}

struct NotificationDivider: View {
  var body: some View {
    Divider()
      .padding(.leading, 56) // Align with text after icon
  }
}

// MARK: - Preview

#Preview {
  NavigationView {
    NotificationSettingsView(
      store: Store(initialState: NotificationSettings.State()) {
        NotificationSettings()
      }
    )
  }
}
