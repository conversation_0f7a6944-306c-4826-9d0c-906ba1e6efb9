import SwiftUI
import Composable<PERSON>rchitecture
import PhotoUploadCore
import CommonUI
#if canImport(UIKit)
import UIKit
#endif

// MARK: - Photo Upload View

public struct PhotoUploadView: View {
  let store: StoreOf<PhotoUpload>
  
  public init(store: StoreOf<PhotoUpload>) {
    self.store = store
  }
  
  public var body: some View {
    WithPerceptionTracking {
      ScrollView {
        VStack(spacing: 24) {
          headerSection
          
          if store.hasSelectedPhotos {
            selectedPhotosSection
          }
          
          uploadOptionsSection
          
          if store.hasSelectedPhotos {
            actionButtonsSection
          }
          
          Spacer(minLength: 100)
        }
        .padding(.horizontal, 20)
        .padding(.top, 20)
      }
      .navigationTitle("选择照片")
      #if os(iOS)
      .navigationBarTitleDisplayMode(.large)
      #endif
      .toolbar {
        ToolbarItem(placement: .automatic) {
          if store.hasSelectedPhotos {
            Button("清空") {
              store.send(.clearAllPhotos)
            }
            .foregroundColor(.red)
          }
        }
      }
      .onAppear {
        store.send(.onAppear)
      }
      .alert(
        item: Binding(
          get: { store.error.map(ErrorWrapper.init) },
          set: { _ in store.send(.dismissError) }
        )
      ) { errorWrapper in
        Alert(
          title: Text("提示"),
          message: Text(errorWrapper.error.localizedDescription),
          primaryButton: .default(Text(buttonTextForError(errorWrapper.error))) {
            // Open settings if permission related
            #if os(iOS)
            if case .permissionDenied = errorWrapper.error,
               let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
              UIApplication.shared.open(settingsUrl)
            } else if case .cameraPermissionDenied = errorWrapper.error,
                      let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
              UIApplication.shared.open(settingsUrl)
            }
            #endif
          },
          secondaryButton: .cancel(Text("取消"))
        )
      }
      .sheet(isPresented: Binding(
        get: {
          let isShowing = store.isShowingImagePicker
          print("📸 ImagePicker isPresented: \(isShowing)")
          return isShowing
        },
        set: { newValue in
          print("📸 ImagePicker set to: \(newValue)")
          if !newValue {
            store.send(.imagePickerPresented(false))
          }
        }
      )) {
        ImagePickerView { images in
          print("📸 Images selected: \(images.count)")
          store.send(.photosSelected(images))
        }
      }
      .sheet(isPresented: Binding(
        get: { store.isShowingCamera },
        set: { _ in store.send(.cameraPresented(false)) }
      )) {
        CameraView { image in
          if let image = image {
            store.send(.photosSelected([PhotoItem(image: image)]))
          }
        }
      }
    }
  }
  
  // MARK: - Header Section
  
  private var headerSection: some View {
    VStack(spacing: 16) {
      Image(systemName: "photo.on.rectangle.angled")
        .font(.system(size: 60))
        .foregroundStyle(
          LinearGradient(
            colors: [.pink, .purple],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
          )
        )
      
      VStack(spacing: 8) {
        Text("上传您的照片")
          .font(.title2)
          .fontWeight(.bold)
        
        Text("选择1-5张照片，AI将为您生成精美的婚纱照")
          .font(.subheadline)
          .foregroundColor(.secondary)
          .multilineTextAlignment(.center)
      }
    }
    .padding(.vertical, 20)
  }
  
  // MARK: - Selected Photos Section
  
  private var selectedPhotosSection: some View {
    VStack(alignment: .leading, spacing: 16) {
      HStack {
        Text("已选择照片")
          .font(.headline)
        
        Spacer()
        
        Text("\(store.selectedImages.count)/\(store.maxSelectionCount)")
          .font(.caption)
          .foregroundColor(.secondary)
      }
      
      LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 3), spacing: 12) {
        ForEach(store.selectedImages) { photo in
          SelectedPhotoCard(photo: photo) {
            store.send(.removePhoto(photo))
          }
        }
      }
    }
    .padding(.horizontal, 4)
  }
  
  // MARK: - Upload Options Section
  
  private var uploadOptionsSection: some View {
    VStack(spacing: 16) {
      Text("照片上传方式")
        .font(.headline)
        .frame(maxWidth: .infinity, alignment: .leading)
      
      HStack(spacing: 16) {
        UploadOptionCard(
          title: "相册",
          subtitle: store.isAtMaxCapacity ? "已达上限" : "从相册选择",
          icon: "photo.on.rectangle",
          isEnabled: store.canAddMorePhotos
        ) {
          store.send(.selectFromPhotoLibrary)
        }

        UploadOptionCard(
          title: "相机",
          subtitle: store.isAtMaxCapacity ? "已达上限" : "拍摄新照片",
          icon: "camera",
          isEnabled: store.canAddMorePhotos
        ) {
          store.send(.selectFromCamera)
        }
      }
    }
  }
  
  // MARK: - Action Buttons Section
  
  private var actionButtonsSection: some View {
    VStack(spacing: 16) {
      Button(action: {
        store.send(.proceedToStyleSelection)
      }) {
        HStack {
          Text("下一步：选择风格")
            .font(.headline)

          Spacer()

          Image(systemName: "arrow.right")
            .font(.headline)
        }
        .foregroundColor(.white)
        .padding(.horizontal, 24)
        .padding(.vertical, 16)
        .background(
          LinearGradient(
            colors: store.canProceedToNext ? [.pink, .purple] : [.gray, .gray],
            startPoint: .leading,
            endPoint: .trailing
          )
        )
        .cornerRadius(12)
      }
      .disabled(!store.canProceedToNext)
      
      if store.isLoading {
        ProgressView("处理中...")
          .progressViewStyle(CircularProgressViewStyle())
      }
    }
  }

  // MARK: - Helper Functions

  private func buttonTextForError(_ error: PhotoUploadError) -> String {
    switch error {
    case .permissionDenied, .cameraPermissionDenied:
      return "设置"
    case .cameraNotAvailable:
      return "确定"
    default:
      return "确定"
    }
  }
}

// MARK: - Supporting Views

struct SelectedPhotoCard: View {
  let photo: PhotoItem
  let onRemove: () -> Void

  var body: some View {
    ZStack(alignment: .topTrailing) {
      photoImage
        .resizable()
        .aspectRatio(contentMode: .fill)
        .frame(height: 100)
        .clipped()
        .cornerRadius(12)
      
      Button(action: onRemove) {
        Image(systemName: "xmark.circle.fill")
          .font(.title3)
          .foregroundColor(.white)
          .background(Color.black.opacity(0.6))
          .clipShape(Circle())
      }
      .padding(8)
    }
  }

  private var photoImage: Image {
    #if canImport(UIKit)
    return Image(uiImage: photo.image)
    #else
    return Image(nsImage: photo.image)
    #endif
  }
}

struct UploadOptionCard: View {
  let title: String
  let subtitle: String
  let icon: String
  let isEnabled: Bool
  let action: () -> Void
  
  var body: some View {
    Button(action: action) {
      VStack(spacing: 12) {
        Image(systemName: icon)
          .font(.system(size: 32))
          .foregroundColor(isEnabled ? .primary : .secondary)
        
        VStack(spacing: 4) {
          Text(title)
            .font(.headline)
            .foregroundColor(isEnabled ? .primary : .secondary)
          
          Text(subtitle)
            .font(.caption)
            .foregroundColor(.secondary)
        }
      }
      .frame(maxWidth: .infinity)
      .padding(.vertical, 24)
      .background(
        RoundedRectangle(cornerRadius: 16)
          .fill(Color.gray.opacity(0.1))
          .overlay(
            RoundedRectangle(cornerRadius: 16)
              .stroke(isEnabled ? Color.clear : Color.gray.opacity(0.3), lineWidth: 1)
          )
      )
    }
    .disabled(!isEnabled)
  }
}

// MARK: - Helper Views

#if os(iOS)
struct ImagePickerView: UIViewControllerRepresentable {
  let onImagesPicked: ([PhotoItem]) -> Void
  
  func makeUIViewController(context: Context) -> UIImagePickerController {
    let picker = UIImagePickerController()
    picker.delegate = context.coordinator
    picker.sourceType = .photoLibrary
    picker.allowsEditing = false
    return picker
  }
  
  func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}
  
  func makeCoordinator() -> Coordinator {
    Coordinator(self)
  }
  
  class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
    let parent: ImagePickerView
    
    init(_ parent: ImagePickerView) {
      self.parent = parent
    }
    
    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
      if let image = info[.originalImage] as? UIImage {
        let photoItem = PhotoItem(image: image)
        // Dismiss first, then call the callback to avoid state conflicts
        picker.dismiss(animated: true) {
          self.parent.onImagesPicked([photoItem])
        }
      } else {
        picker.dismiss(animated: true)
      }
    }

    func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
      picker.dismiss(animated: true)
    }
  }
}
#else
// macOS placeholder implementation
struct ImagePickerView: View {
  let onImagesPicked: ([PhotoItem]) -> Void

  var body: some View {
    VStack {
      Text("图片选择功能在 macOS 上暂不可用")
        .foregroundColor(.secondary)

      Button("模拟选择图片") {
        // Create mock photo for macOS testing
        let mockPhoto = PhotoItem(image: NSImage(systemSymbolName: "photo", accessibilityDescription: nil)!)
        onImagesPicked([mockPhoto])
      }
    }
    .padding()
  }
}
#endif

#if os(iOS)
struct CameraView: UIViewControllerRepresentable {
  let onImageCaptured: (UIImage?) -> Void

  func makeUIViewController(context: Context) -> UIViewController {
    // Check if camera is available before creating the picker
    guard UIImagePickerController.isSourceTypeAvailable(.camera) else {
      // Return a view controller that shows an error message
      let alertController = UIAlertController(
        title: "相机不可用",
        message: "当前设备不支持相机功能，请使用真机测试相机功能。",
        preferredStyle: .alert
      )

      alertController.addAction(UIAlertAction(title: "确定", style: .default) { _ in
        self.onImageCaptured(nil)
      })

      return alertController
    }

    let picker = UIImagePickerController()
    picker.delegate = context.coordinator
    picker.sourceType = .camera
    picker.allowsEditing = false
    return picker
  }

  func updateUIViewController(_ uiViewController: UIViewController, context: Context) {}

  func makeCoordinator() -> Coordinator {
    Coordinator(self)
  }

  class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
    let parent: CameraView

    init(_ parent: CameraView) {
      self.parent = parent
    }

    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
      let image = info[.originalImage] as? UIImage
      // Dismiss first, then call the callback to avoid state conflicts
      picker.dismiss(animated: true) {
        self.parent.onImageCaptured(image)
      }
    }

    func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
      picker.dismiss(animated: true) {
        self.parent.onImageCaptured(nil)
      }
    }
  }
}
#else
// macOS placeholder implementation
struct CameraView: View {
  let onImageCaptured: (NSImage?) -> Void

  var body: some View {
    VStack {
      Text("相机功能在 macOS 上暂不可用")
        .foregroundColor(.secondary)

      Button("模拟拍照") {
        // Create mock photo for macOS testing
        let mockPhoto = NSImage(systemSymbolName: "camera", accessibilityDescription: nil)
        onImageCaptured(mockPhoto)
      }
    }
    .padding()
  }
}
#endif

// MARK: - Error Wrapper

struct ErrorWrapper: Identifiable {
  let id = UUID()
  let error: PhotoUploadError
}

// MARK: - Previews

#Preview("Empty State") {
  PhotoUploadView(
    store: Store(initialState: PhotoUpload.State()) {
      PhotoUpload()
    }
  )
}

#Preview("With Photos") {
  let mockPhotos: [PhotoItem] = {
    #if canImport(UIKit)
    return [
      PhotoItem(image: UIImage(systemName: "photo")!),
      PhotoItem(image: UIImage(systemName: "photo.fill")!)
    ]
    #else
    return [
      PhotoItem(image: NSImage(systemSymbolName: "photo", accessibilityDescription: nil)!),
      PhotoItem(image: NSImage(systemSymbolName: "photo.fill", accessibilityDescription: nil)!)
    ]
    #endif
  }()

  PhotoUploadView(
    store: Store(
      initialState: PhotoUpload.State(selectedImages: mockPhotos)
    ) {
      PhotoUpload()
    }
  )
}
