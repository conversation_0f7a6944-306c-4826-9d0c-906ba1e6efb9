import SwiftUI
import Composable<PERSON>rchitecture
import SubscriptionCore
import UserStateCore
import CommonUI
#if canImport(UIKit)
import UIKit
#endif

// MARK: - Subscription View

public struct SubscriptionView: View {
  let store: StoreOf<Subscription>
  
  public init(store: StoreOf<Subscription>) {
    self.store = store
  }
  
  public var body: some View {
    WithPerceptionTracking {
      NavigationView {
        ZStack {
          Color.appBackground.ignoresSafeArea()
          
          if store.isLoading {
            loadingView
          } else if let error = store.error {
            errorView(error)
          } else {
            mainContent
          }
        }
        #if os(iOS)
        .navigationBarTitleDisplayMode(.inline)
        #endif
        .toolbar {
          ToolbarItem(placement: .automatic) {
            Button("跳过") {
              store.send(.returnToPreviousFlow)
            }
            .foregroundColor(.secondary)
          }
        }
      }
      .onAppear {
        store.send(.onAppear)
      }
      .alert(
        "购买成功",
        isPresented: Binding(
          get: { store.showingSuccessAlert },
          set: { _ in store.send(.dismissSuccessAlert) }
        )
      ) {
        But<PERSON>("太棒了！") {
          store.send(.dismissSuccessAlert)
        }
      } message: {
        if let message = store.successMessage {
          Text(message)
        }
      }
    }
  }
  
  // MARK: - Loading View
  
  private var loadingView: some View {
    VStack(spacing: 20) {
      EnhancedLoadingView(message: "加载订阅信息...")
      
      Text("正在为您准备最佳方案")
        .font(.body)
        .foregroundColor(.secondary)
        .multilineTextAlignment(.center)
    }
    .padding()
  }
  
  // MARK: - Error View
  
  private func errorView(_ error: SubscriptionError) -> some View {
    VStack(spacing: 20) {
      Image(systemName: "exclamationmark.triangle.fill")
        .font(.system(size: 48))
        .foregroundColor(.orange)
      
      Text("加载失败")
        .font(.headline)
      
      Text(error.localizedDescription)
        .font(.body)
        .foregroundColor(.secondary)
        .multilineTextAlignment(.center)
      
      if let suggestion = error.recoverySuggestion {
        Text(suggestion)
          .font(.caption)
          .foregroundColor(.secondary)
          .multilineTextAlignment(.center)
      }
      
      VStack(spacing: 12) {
        PrimaryButton(
          title: "重试",
          action: { store.send(.loadProducts) }
        )
        
        Button("恢复购买") {
          store.send(.restorePurchases)
        }
        .foregroundColor(.primaryAccent)
      }
      .padding(.horizontal, 40)
    }
    .padding()
  }
  
  // MARK: - Main Content
  
  private var mainContent: some View {
    ScrollView {
      VStack(spacing: 32) {
        headerSection
        featuresSection
        subscriptionPlansSection
        
        if store.availableProducts.isEmpty {
          Text("暂无可用订阅方案")
            .foregroundColor(.secondary)
        }
        
        Spacer(minLength: 100)
      }
      .padding(.horizontal, 20)
      .padding(.top, 20)
    }
  }
  
  // MARK: - Header Section
  
  private var headerSection: some View {
    VStack(spacing: 16) {
      // Crown icon with gradient
      ZStack {
        Circle()
          .fill(
            LinearGradient(
              colors: [.accentPink.opacity(0.2), .accentPurple.opacity(0.2)],
              startPoint: .topLeading,
              endPoint: .bottomTrailing
            )
          )
          .frame(width: 100, height: 100)
        
        Image(systemName: "crown.fill")
          .font(.system(size: 48))
          .foregroundStyle(
            LinearGradient(
              colors: [.accentPink, .accentPurple],
              startPoint: .topLeading,
              endPoint: .bottomTrailing
            )
          )
      }
      
      VStack(spacing: 8) {
        Text("升级到 Pro")
          .font(.largeTitle)
          .fontWeight(.bold)

        if let templateId = store.selectedTemplate {
          Text("体验「\(templateId)」及更多高级风格")
            .font(.subheadline)
            .foregroundColor(.secondary)
            .multilineTextAlignment(.center)
        } else {
          Text("获得所有高级AI生成风格的完整访问权限")
            .font(.subheadline)
            .foregroundColor(.secondary)
            .multilineTextAlignment(.center)
        }
      }
    }
    .padding(.vertical, 20)
  }
  
  // MARK: - Features Section

  private var featuresSection: some View {
    VStack(alignment: .leading, spacing: 16) {
      Text("功能特权")
        .font(.headline)
        .fontWeight(.semibold)

      VStack(spacing: 12) {
        FeatureRow(
          icon: "wand.and.stars.inverse",
          title: "高级AI风格",
          description: "解锁 Artistic、Vintage、Elegant 等专业风格",
          iconColor: .accentPurple
        )

        FeatureRow(
          icon: "bolt.fill",
          title: "优先生成",
          description: "享受更快的图片生成速度，无需排队等待",
          iconColor: .accentBlue
        )

        FeatureRow(
          icon: "4k.tv.fill",
          title: "高分辨率输出",
          description: "获得4K超高清质量的婚纱照生成结果",
          iconColor: .accentOrange
        )

        FeatureRow(
          icon: "chart.bar.fill",
          title: "灵活用量",
          description: "单次购买或订阅模式，满足不同使用需求",
          iconColor: .accentPink
        )
      }
    }
  }
  
  // MARK: - Subscription Plans Section

  private var subscriptionPlansSection: some View {
    VStack(alignment: .leading, spacing: 24) {
      // Single Purchase Options
      let singlePurchaseProducts = store.availableProducts.filter { $0.isSinglePurchase }
      if !singlePurchaseProducts.isEmpty {
        VStack(alignment: .leading, spacing: 16) {
          Text("单次购买")
            .font(.headline)
            .fontWeight(.semibold)

          Text("轻量尝鲜，按需使用")
            .font(.subheadline)
            .foregroundColor(.secondary)

          VStack(spacing: 12) {
            ForEach(singlePurchaseProducts) { product in
              SubscriptionPlanCard(
                product: product,
                isSelected: false,
                isPurchasing: store.isPurchasing
              ) {
                store.send(.purchaseProduct(product))
              }
            }
          }
        }
      }

      // Subscription Options
      let subscriptionProducts = store.availableProducts.filter { $0.isSubscription }
      if !subscriptionProducts.isEmpty {
        VStack(alignment: .leading, spacing: 16) {
          Text("订阅方案")
            .font(.headline)
            .fontWeight(.semibold)

          Text("持续使用，享受更多优惠")
            .font(.subheadline)
            .foregroundColor(.secondary)

          VStack(spacing: 12) {
            ForEach(subscriptionProducts) { product in
              SubscriptionPlanCard(
                product: product,
                isSelected: false,
                isPurchasing: store.isPurchasing
              ) {
                store.send(.purchaseProduct(product))
              }
            }
          }
        }
      }

      // Restore purchases button
      Button(action: {
        store.send(.restorePurchases)
      }) {
        HStack {
          Image(systemName: "arrow.clockwise")
          Text("恢复购买")
        }
        .font(.subheadline)
        .foregroundColor(.primaryAccent)
      }
      .padding(.top, 16)
      .frame(maxWidth: .infinity)
    }
  }
}

// MARK: - Feature Row Component

private struct FeatureRow: View {
  let icon: String
  let title: String
  let description: String
  let iconColor: Color
  
  var body: some View {
    HStack(spacing: 16) {
      Image(systemName: icon)
        .font(.title2)
        .foregroundColor(iconColor)
        .frame(width: 32)
      
      VStack(alignment: .leading, spacing: 4) {
        Text(title)
          .font(.headline)
          .fontWeight(.medium)
        
        Text(description)
          .font(.caption)
          .foregroundColor(.secondary)
          .fixedSize(horizontal: false, vertical: true)
      }
      
      Spacer()
    }
    .padding(.vertical, 8)
  }
}

// MARK: - Subscription Plan Card Component

private struct SubscriptionPlanCard: View {
  let product: SubscriptionProduct
  let isSelected: Bool
  let isPurchasing: Bool
  let action: () -> Void
  
  @State private var isPressed = false
  
  var body: some View {
    Button(action: {
      // Haptic feedback
      #if canImport(UIKit)
      let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
      impactFeedback.impactOccurred()
      #endif
      action()
    }) {
      VStack(spacing: 16) {
        // Header with popular badge
        HStack {
          VStack(alignment: .leading, spacing: 4) {
            Text(product.displayName)
              .font(.headline)
              .fontWeight(.semibold)
              .foregroundColor(.primary)
            
            Text(product.description)
              .font(.caption)
              .foregroundColor(.secondary)
              .multilineTextAlignment(.leading)
          }
          
          Spacer()
          
          if product.isPopular {
            Text("最受欢迎")
              .font(.caption2)
              .fontWeight(.bold)
              .foregroundColor(.white)
              .padding(.horizontal, 8)
              .padding(.vertical, 4)
              .background(Color.accentPink)
              .cornerRadius(8)
          }
        }
        
        // Usage limits and pricing
        VStack(spacing: 8) {
          HStack {
            VStack(alignment: .leading, spacing: 2) {
              Text(product.price)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primary)

              if let duration = product.duration {
                Text("/ \(duration.displayName)")
                  .font(.caption)
                  .foregroundColor(.secondary)
              }
            }

            Spacer()

            if let duration = product.duration, let savings = duration.savingsMessage {
              Text(savings)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.green)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(Color.green.opacity(0.15))
                .cornerRadius(6)
            }
          }

          // Usage limits display
          HStack {
            Image(systemName: "wand.and.stars")
              .font(.caption)
              .foregroundColor(.accentPink)

            Text(product.generationLimit)
              .font(.caption)
              .fontWeight(.medium)
              .foregroundColor(.primary)

            Spacer()

            if product.isSinglePurchase {
              Text("单次购买")
                .font(.caption2)
                .fontWeight(.bold)
                .foregroundColor(.orange)
                .padding(.horizontal, 6)
                .padding(.vertical, 2)
                .background(Color.orange.opacity(0.15))
                .cornerRadius(4)
            }
          }
        }
        
        // Purchase button or loading
        if isPurchasing {
          HStack {
            ProgressView()
              .progressViewStyle(CircularProgressViewStyle(tint: .white))
              .scaleEffect(0.8)
            
            Text("处理中...")
              .font(.headline)
              .fontWeight(.semibold)
          }
          .foregroundColor(.white)
          .frame(maxWidth: .infinity)
          .padding(.vertical, 16)
          .background(Color.gray)
          .cornerRadius(12)
        } else {
          HStack {
            Text(product.isSinglePurchase ? "立即购买" : "立即订阅")
              .font(.headline)
              .fontWeight(.semibold)

            Image(systemName: "arrow.right")
              .font(.headline)
          }
          .foregroundColor(.white)
          .frame(maxWidth: .infinity)
          .padding(.vertical, 16)
          .background(
            LinearGradient(
              colors: [.accentPink, .accentPurple],
              startPoint: .leading,
              endPoint: .trailing
            )
          )
          .cornerRadius(12)
        }
      }
      .padding(20)
      .background(
        RoundedRectangle(cornerRadius: 16)
          .fill(Color.cardBackground)
          .overlay(
            RoundedRectangle(cornerRadius: 16)
              .stroke(
                product.isPopular ? Color.accentPink : Color.clear,
                lineWidth: product.isPopular ? 2 : 0
              )
          )
      )
      .shadow(
        color: .black.opacity(0.08),
        radius: product.isPopular ? 12 : 8,
        x: 0,
        y: product.isPopular ? 6 : 4
      )
    }
    .disabled(isPurchasing)
    .buttonStyle(PlainButtonStyle())
    .scaleEffect(isPressed ? 0.98 : 1.0)
    .animation(.easeInOut(duration: 0.15), value: isPressed)
    .simultaneousGesture(
      DragGesture(minimumDistance: 0)
        .onChanged { _ in
          withAnimation(.easeInOut(duration: 0.1)) {
            isPressed = true
          }
        }
        .onEnded { _ in
          withAnimation(.easeInOut(duration: 0.1)) {
            isPressed = false
          }
        }
    )
  }
}

// MARK: - Preview

#Preview("Default") {
  SubscriptionView(
    store: Store(initialState: Subscription.State()) {
      Subscription()
    }
  )
}

#Preview("With Products") {
  SubscriptionView(
    store: Store(initialState: Subscription.State(
      availableProducts: [
        SubscriptionProduct(
          id: "com.wenhaofree.bridal.single_basic",
          displayName: "单次生成",
          description: "1次高清图片生成",
          price: "¥1",
          priceValue: 1.00,
          currencyCode: "CNY",
          productType: .singlePurchase,
          usageLimits: UserStateCore.UsageLimits.singleUse
        ),
        SubscriptionProduct(
          id: "com.wenhaofree.bridal.sub_monthly_40",
          displayName: "高级月度订阅",
          description: "每月40次生成，解锁所有功能",
          price: "¥28",
          priceValue: 28.00,
          currencyCode: "CNY",
          productType: .subscription,
          duration: .monthly,
          usageLimits: UserStateCore.UsageLimits.monthlyPremium,
          isPopular: true
        ),
        SubscriptionProduct(
          id: "com.wenhaofree.bridal.sub_yearly_600",
          displayName: "年度订阅",
          description: "每年600次生成，享受最大优惠",
          price: "¥128",
          priceValue: 128.00,
          currencyCode: "CNY",
          productType: .subscription,
          duration: .yearly,
          usageLimits: UserStateCore.UsageLimits.yearlyStandard
        )
      ]
    )) {
      Subscription()
    }
  )
}