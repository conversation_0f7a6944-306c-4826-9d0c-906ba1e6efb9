import ComposableArchitecture
import Foundation
import SwiftUI
#if canImport(UIKit)
import UIKit
#endif

// MARK: - Photo Upload Feature

@Reducer
public struct PhotoUpload: Sendable {
  
  // MARK: - State
  
  @ObservableState
  public struct State: Equatable, Sendable {
    public var selectedImages: [PhotoItem] = []
    public var isShowingImagePicker = false
    public var isShowingCamera = false
    public var isLoading = false
    public var error: PhotoUploadError?
    public var sourceType: ImageSourceType = .photoLibrary
    public var maxSelectionCount = 5

    public init(
      selectedImages: [PhotoItem] = [],
      isShowingImagePicker: Bool = false,
      isShowingCamera: Bool = false,
      isLoading: Bool = false,
      error: PhotoUploadError? = nil,
      sourceType: ImageSourceType = .photoLibrary,
      maxSelectionCount: Int = 5
    ) {
      self.selectedImages = selectedImages
      self.isShowingImagePicker = isShowingImagePicker
      self.isShowingCamera = isShowingCamera
      self.isLoading = isLoading
      self.error = error
      self.sourceType = sourceType
      self.maxSelectionCount = maxSelectionCount
    }
    
    public var canAddMorePhotos: Bool {
      selectedImages.count < maxSelectionCount
    }
    
    public var hasSelectedPhotos: Bool {
      !selectedImages.isEmpty
    }

    public var canProceedToNext: Bool {
      hasSelectedPhotos && !isLoading && error == nil
    }

    public var isAtMaxCapacity: Bool {
      selectedImages.count >= maxSelectionCount
    }
  }
  
  // MARK: - Action
  
  public enum Action: BindableAction, Sendable {
    case binding(BindingAction<State>)
    case onAppear
    case selectFromPhotoLibrary
    case selectFromCamera
    case imagePickerPresented(Bool)
    case cameraPresented(Bool)
    case photosSelected([PhotoItem])
    case removePhoto(PhotoItem)
    case clearAllPhotos
    case proceedToStyleSelection
    case errorOccurred(PhotoUploadError)
    case dismissError
  }
  
  // MARK: - Dependencies
  
  @Dependency(\.photoLibraryClient) var photoLibraryClient
  @Dependency(\.cameraClient) var cameraClient

  // MARK: - Initializer

  public init() {}

  // MARK: - Reducer Body

  public var body: some Reducer<State, Action> {
    BindingReducer()
    
    Reduce { state, action in
      switch action {
      case .binding:
        return .none
        
      case .onAppear:
        return .run { send in
          // Check photo library permission
          let hasPermission = await photoLibraryClient.checkPermission()
          if !hasPermission {
            await send(.errorOccurred(.permissionDenied))
          }
        }
        
      case .selectFromPhotoLibrary:
        state.sourceType = .photoLibrary
        state.isShowingImagePicker = true
        return .none
        
      case .selectFromCamera:
        state.sourceType = .camera
        return .run { send in
          // First check if camera is available on the device
          let isAvailable = await cameraClient.isAvailable()
          guard isAvailable else {
            await send(.errorOccurred(.cameraNotAvailable))
            return
          }

          // Then check permissions
          let hasPermission = await cameraClient.checkPermission()
          if hasPermission {
            await send(.cameraPresented(true))
          } else {
            await send(.errorOccurred(.cameraPermissionDenied))
          }
        }
        
      case let .imagePickerPresented(isPresented):
        state.isShowingImagePicker = isPresented
        return .none
        
      case let .cameraPresented(isPresented):
        state.isShowingCamera = isPresented
        return .none
        
      case let .photosSelected(photos):
        print("📷 PhotoUpload: photosSelected called with \(photos.count) photos")
        print("📷 PhotoUpload: Current selected images: \(state.selectedImages.count)")

        let availableSlots = state.maxSelectionCount - state.selectedImages.count
        let photosToAdd = Array(photos.prefix(availableSlots))

        // Filter out duplicates
        let newPhotos = photosToAdd.filter { newPhoto in
          !state.selectedImages.contains { $0.id == newPhoto.id }
        }

        print("📷 PhotoUpload: Available slots: \(availableSlots), New photos to add: \(newPhotos.count)")

        // Check if we're at max capacity before adding
        if availableSlots == 0 && !photos.isEmpty {
          print("📷 PhotoUpload: Max capacity reached")
          state.error = .maxSelectionReached
          return .none
        }

        // Add new photos if any
        if !newPhotos.isEmpty {
          state.selectedImages.append(contentsOf: newPhotos)
          state.error = nil // Clear any previous errors
          print("📷 PhotoUpload: Added \(newPhotos.count) photos, total now: \(state.selectedImages.count)")
        }

        // Always close the picker/camera after selection
        state.isShowingImagePicker = false
        state.isShowingCamera = false
        print("📷 PhotoUpload: Closed picker/camera")

        return .none
        
      case let .removePhoto(photo):
        state.selectedImages.removeAll { $0.id == photo.id }
        return .none
        
      case .clearAllPhotos:
        state.selectedImages.removeAll()
        return .none
        
      case .proceedToStyleSelection:
        // Validate that we have selected photos before proceeding
        guard !state.selectedImages.isEmpty else {
          state.error = .imageProcessingFailed
          return .none
        }

        // This will be handled by parent reducer
        return .none
        
      case let .errorOccurred(error):
        state.error = error
        state.isLoading = false
        return .none
        
      case .dismissError:
        state.error = nil
        return .none
      }
    }
  }
}

// MARK: - Models

public struct PhotoItem: Identifiable, Equatable, @unchecked Sendable {
  public let id = UUID()
  #if canImport(UIKit)
  public let image: UIImage
  #else
  public let image: NSImage
  #endif
  public let originalSize: CGSize
  public let creationDate: Date?

  #if canImport(UIKit)
  public init(image: UIImage, originalSize: CGSize? = nil, creationDate: Date? = nil) {
    self.image = image
    self.originalSize = originalSize ?? image.size
    self.creationDate = creationDate
  }
  #else
  public init(image: NSImage, originalSize: CGSize? = nil, creationDate: Date? = nil) {
    self.image = image
    self.originalSize = originalSize ?? image.size
    self.creationDate = creationDate
  }
  #endif

  public var imageData: Data {
    #if canImport(UIKit)
    return image.jpegData(compressionQuality: 0.8) ?? Data()
    #else
    guard let tiffData = image.tiffRepresentation,
          let bitmapImage = NSBitmapImageRep(data: tiffData),
          let jpegData = bitmapImage.representation(using: .jpeg, properties: [.compressionFactor: 0.8]) else {
      return Data()
    }
    return jpegData
    #endif
  }

  public static func == (lhs: PhotoItem, rhs: PhotoItem) -> Bool {
    lhs.id == rhs.id
  }
}

public enum ImageSourceType: String, CaseIterable, Sendable {
  case photoLibrary = "photo_library"
  case camera = "camera"
  
  public var displayName: String {
    switch self {
    case .photoLibrary:
      return "相册"
    case .camera:
      return "相机"
    }
  }
  
  public var systemImage: String {
    switch self {
    case .photoLibrary:
      return "photo.on.rectangle"
    case .camera:
      return "camera"
    }
  }
}

public enum PhotoUploadError: Error, Equatable, LocalizedError, Sendable {
  case permissionDenied
  case cameraPermissionDenied
  case cameraNotAvailable
  case imageProcessingFailed
  case maxSelectionReached
  case networkError(String)
  
  public var errorDescription: String? {
    switch self {
    case .permissionDenied:
      return "需要访问相册权限才能选择照片"
    case .cameraPermissionDenied:
      return "需要相机权限才能拍照"
    case .cameraNotAvailable:
      return "相机不可用"
    case .imageProcessingFailed:
      return "图片处理失败"
    case .maxSelectionReached:
      return "最多只能选择5张照片"
    case .networkError(let message):
      return "网络错误: \(message)"
    }
  }
  
  public var recoverySuggestion: String? {
    switch self {
    case .permissionDenied, .cameraPermissionDenied:
      return "请在设置中允许应用访问相册和相机"
    case .cameraNotAvailable:
      return "请检查设备相机是否正常工作"
    case .imageProcessingFailed:
      return "请重新选择照片"
    case .maxSelectionReached:
      return "请删除一些照片后再添加"
    case .networkError:
      return "请检查网络连接后重试"
    }
  }
}

// MARK: - Dependencies

@DependencyClient
public struct PhotoLibraryClient: Sendable {
  public var checkPermission: @Sendable () async -> Bool = { false }
  public var requestPermission: @Sendable () async -> Bool = { false }
  public var selectPhotos: @Sendable (Int) async throws -> [PhotoItem] = { _ in [] }

  // Mock data helper
  public static func createMockPhotos(count: Int) -> [PhotoItem] {
    let mockImageNames = ["photo", "photo.fill", "camera", "camera.fill", "person.crop.square"]
    return (0..<count).compactMap { index in
      let imageName = mockImageNames[index % mockImageNames.count]
      #if canImport(UIKit)
      if let image = UIImage(systemName: imageName) {
        return PhotoItem(image: image, originalSize: CGSize(width: 300, height: 400))
      }
      #else
      if let image = NSImage(systemSymbolName: imageName, accessibilityDescription: nil) {
        return PhotoItem(image: image, originalSize: CGSize(width: 300, height: 400))
      }
      #endif
      return nil
    }
  }
}

extension PhotoLibraryClient: DependencyKey {
  public static let liveValue = PhotoLibraryClient(
    checkPermission: {
      // TODO: Implement with Photos framework
      return true
    },
    requestPermission: {
      // TODO: Implement with Photos framework
      return true
    },
    selectPhotos: { maxCount in
      // TODO: Implement with PHPickerViewController
      // For now, return mock photos for testing
      return PhotoLibraryClient.createMockPhotos(count: min(maxCount, 3))
    }
  )
  
  public static let testValue = PhotoLibraryClient(
    checkPermission: { true },
    requestPermission: { true },
    selectPhotos: { _ in [] }
  )
}

@DependencyClient
public struct CameraClient: Sendable {
  public var checkPermission: @Sendable () async -> Bool = { false }
  public var requestPermission: @Sendable () async -> Bool = { false }
  public var isAvailable: @Sendable () async -> Bool = { false }
  public var capturePhoto: @Sendable () async throws -> PhotoItem = {
    #if canImport(UIKit)
    return PhotoItem(image: UIImage(systemName: "photo")!)
    #else
    return PhotoItem(image: NSImage(systemSymbolName: "photo", accessibilityDescription: nil)!)
    #endif
  }
}

extension CameraClient: DependencyKey {
  public static let liveValue = CameraClient(
    checkPermission: {
      #if canImport(UIKit) && !targetEnvironment(simulator)
      // TODO: Implement with AVFoundation
      return true
      #else
      return false
      #endif
    },
    requestPermission: {
      #if canImport(UIKit) && !targetEnvironment(simulator)
      // TODO: Implement with AVFoundation
      return true
      #else
      return false
      #endif
    },
    isAvailable: {
      #if canImport(UIKit)
      return await MainActor.run {
        UIImagePickerController.isSourceTypeAvailable(.camera)
      }
      #else
      return false
      #endif
    },
    capturePhoto: {
      // TODO: Implement camera capture
      throw PhotoUploadError.cameraNotAvailable
    }
  )
  
  public static let testValue = CameraClient(
    checkPermission: { true },
    requestPermission: { true },
    isAvailable: { true },
    capturePhoto: {
      #if canImport(UIKit)
      return PhotoItem(image: UIImage(systemName: "photo")!)
      #else
      return PhotoItem(image: NSImage(systemSymbolName: "photo", accessibilityDescription: nil)!)
      #endif
    }
  )
}

extension DependencyValues {
  public var photoLibraryClient: PhotoLibraryClient {
    get { self[PhotoLibraryClient.self] }
    set { self[PhotoLibraryClient.self] = newValue }
  }
  
  public var cameraClient: CameraClient {
    get { self[CameraClient.self] }
    set { self[CameraClient.self] = newValue }
  }
}
