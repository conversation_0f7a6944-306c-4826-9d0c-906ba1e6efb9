import Foundation
import ComposableArchitecture
import LoggingClient
import AuthenticationClient
import UserStorageClient
import UserStateCore

// MARK: - Launch Screen Feature

@Reducer
public struct Launch: Sendable {
  @ObservableState
  public struct State: Equatable {
    public var isCheckingAuthentication = true
    public var authenticationError: String?
    
    public init() {}
  }
  
  public enum Action: Sendable {
    case onAppear
    case checkAuthenticationStatus
    case userIsAuthenticated(User, String) // user, token
    case userNotAuthenticated
    case authenticationCheckFailed(String)
  }
  
  @Dependency(\.loggingClient) var logger
  @Dependency(\.authenticationClient) var authenticationClient
  @Dependency(\.userStorageClient) var userStorageClient
  
  public init() {}
  
  public var body: some Reducer<State, Action> {
    Reduce { state, action in
      switch action {
      case .onAppear:
        logger.info(.app, "Launch screen appeared, checking authentication")
        return .send(.checkAuthenticationStatus)
        
      case .checkAuthenticationStatus:
        state.isCheckingAuthentication = true
        state.authenticationError = nil
        
        return .run { send in
          @Dependency(\.userStorageClient) var userStorageClient
          
          do {
            print("🚀 LaunchCore: Starting authentication check")
            
            // Check if user is logged in using Keychain storage
            let isLoggedIn = await userStorageClient.isUserLoggedIn()
            print("🔍 LaunchCore: isLoggedIn = \(isLoggedIn)")
            
            if isLoggedIn {
              // Try to load user data from Keychain
              if let (user, token) = try await userStorageClient.loadUser() {
                print("✅ LaunchCore: User data loaded from Keychain")
                print("   用户: \(user.displayName) (\(user.email))")
                print("   Token: \(token.prefix(20))...")
                
                // Update last login date
                try await userStorageClient.updateLastLoginDate()
                
                await send(.userIsAuthenticated(user, token))
              } else {
                print("❌ LaunchCore: Failed to load user data, clearing auth state")
                // Clean up invalid state
                try? await userStorageClient.deleteUser()
                await send(.userNotAuthenticated)
              }
            } else {
              print("❌ LaunchCore: No authentication found")
              await send(.userNotAuthenticated)
            }
          } catch {
            print("💥 LaunchCore: Error during auth check: \(error)")
            await send(.authenticationCheckFailed(error.localizedDescription))
          }
        }
        
      case .userIsAuthenticated(let user, _):
        logger.info(.app, "User has valid authentication: \(user.displayName)")
        state.isCheckingAuthentication = false
        return .none
        
      case .userNotAuthenticated:
        logger.info(.app, "User authentication not found")
        state.isCheckingAuthentication = false
        return .none
        
      case .authenticationCheckFailed(let error):
        logger.error(.app, "Authentication check failed: \(error)")
        state.isCheckingAuthentication = false
        state.authenticationError = error
        // Default to not authenticated on error
        return .send(.userNotAuthenticated)
      }
    }
  }
}