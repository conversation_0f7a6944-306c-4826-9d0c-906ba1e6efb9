import SwiftUI
import Composable<PERSON>rchitecture
import CommonUI
import ImageTypeSelectionCore
import Perception
#if canImport(UIKit)
import UIKit
#endif

public struct ImageTypeSelectionView: View {
  @Perception.Bindable var store: StoreOf<ImageTypeSelection>
  
  private let columns = [
    GridItem(.flexible(), spacing: 16),
    GridItem(.flexible(), spacing: 16)
  ]
  
  public init(store: StoreOf<ImageTypeSelection>) {
    self.store = store
  }
  
  public var body: some View {
    WithPerceptionTracking {
      ZStack {
        Color.appBackground.ignoresSafeArea()
        
        if store.isLoading {
          loadingView
        } else if let error = store.error {
          errorView(error)
        } else {
          mainContent
        }
      }
      .navigationTitle("选择您的风格")
      #if os(iOS)
      .navigationBarTitleDisplayMode(.large)
      #endif
      .onAppear {
        print("🎨 ImageTypeSelectionView appeared")
        store.send(.onAppear)
      }
      .sheet(isPresented: Binding(
        get: { store.isShowingGenerationConfirmation },
        set: { _ in store.send(.hideGenerationConfirmation) }
      )) {
        GenerationConfirmationSheet(store: store)
      }
    }
  }
  
  private var loadingView: some View {
    VStack(spacing: 20) {
      EnhancedLoadingView(message: "正在加载精美模板...")

      Text("为您准备精彩风格")
        .font(.body)
        .foregroundColor(.secondary)
        .multilineTextAlignment(.center)
    }
    .padding()
  }
  
  private func errorView(_ error: String) -> some View {
    VStack(spacing: 20) {
      Image(systemName: "exclamationmark.triangle.fill")
        .font(.system(size: 48))
        .foregroundColor(.orange)

      Text("无法加载模板")
        .font(.headline)

      Text(error)
        .font(.body)
        .foregroundColor(.secondary)
        .multilineTextAlignment(.center)

      PrimaryButton(
        title: "重试",
        action: { store.send(.refreshTemplates) }
      )
      .padding(.horizontal, 40)
    }
    .padding()
  }
  
  private var mainContent: some View {
    ScrollView {
      LazyVGrid(columns: columns, spacing: 20) {
        ForEach(store.templates, id: \.id) { template in
          TemplateCard(
            template: template,
            isGuestMode: store.isGuestMode
          ) {
            print("🎯 Action closure called for template: \(template.name)")
            store.send(.templateSelected(template))
          }
        }
      }
      .padding()
    }
    .refreshable {
      store.send(.refreshTemplates)
    }
  }
}

// MARK: - Template Card Component

private struct TemplateCard: View {
  let template: ImageTemplate
  let isGuestMode: Bool
  let action: () -> Void
  
  @State private var isPressed = false
  
  var body: some View {
    Button(action: {
      print("🎨 Template card button pressed: \(template.name)")
      
      // 添加触觉反馈
      #if canImport(UIKit)
      let impactFeedback = UIImpactFeedbackGenerator(style: .light)
      impactFeedback.impactOccurred()
      #endif
      
      // 始终调用action，让Core层处理premium逻辑
      print("✅ Calling action closure for: \(template.name)")
      action()
    }) {
      // 简化的卡片设计，避免复杂的组件冲突
      VStack(alignment: .leading, spacing: 12) {
        // Template preview image placeholder
        RoundedRectangle(cornerRadius: 12)
          .fill(
            LinearGradient(
              colors: gradientColors,
              startPoint: .topLeading,
              endPoint: .bottomTrailing
            )
          )
          .frame(height: 120)
          .overlay(
            VStack {
              Image(systemName: template.category.systemImage)
                .font(.system(size: 30))
                .foregroundColor(.white)
              
              if template.isPremium {
                Image(systemName: "crown.fill")
                  .font(.caption)
                  .foregroundColor(.yellow)
              }
            }
          )
        
        VStack(alignment: .leading, spacing: 6) {
          HStack {
            Text(template.name)
              .font(.headline)
              .fontWeight(.semibold)
              .lineLimit(1)
              .foregroundColor(.primary)
            
            Spacer()
            
            if template.isPremium {
              Text("专业版")
                .font(.caption2)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .padding(.horizontal, 6)
                .padding(.vertical, 2)
                .background(Color.orange)
                .cornerRadius(4)
            }
          }
          
          Text(template.description)
            .font(.caption)
            .foregroundColor(.secondary)
            .lineLimit(2)
          
          HStack {
            Text(template.category.rawValue)
              .font(.caption2)
              .fontWeight(.medium)
              .foregroundColor(.white)
              .padding(.horizontal, 6)
              .padding(.vertical, 2)
              .background(categoryColor)
              .cornerRadius(4)
            
            Spacer()
            
            HStack(spacing: 4) {
              Image(systemName: "clock")
                .font(.caption2)
              Text("\(Int(template.estimatedGenerationTime))s")
                .font(.caption2)
            }
            .foregroundColor(.secondary)
          }
        }
      }
      .padding()
      .background(Color.cardBackground)
      .cornerRadius(12)
      .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
    }
    .buttonStyle(PlainButtonStyle())
    .scaleEffect(isPressed ? 0.95 : 1.0)
    .animation(.easeInOut(duration: 0.15), value: isPressed)
    .simultaneousGesture(
      DragGesture(minimumDistance: 0)
        .onChanged { _ in
          withAnimation(.easeInOut(duration: 0.1)) {
            isPressed = true
          }
        }
        .onEnded { _ in
          withAnimation(.easeInOut(duration: 0.1)) {
            isPressed = false
          }
        }
    )
  }
  
  private var gradientColors: [Color] {
    switch template.category {
    case .wedding: return [.accentPink, .accentPurple]
    case .portrait: return [.accentBlue, .primaryAccent]
    case .fashion: return [.accentPurple, .accentPink]
    case .vintage: return [.accentOrange, .secondaryAccent]
    case .artistic: return [.accentOrange, .accentPink]
    case .elegant: return [.primaryAccent, .accentBlue]
    }
  }
  
  private var categoryColor: Color {
    switch template.category {
    case .wedding: return .accentPink
    case .portrait: return .accentBlue
    case .fashion: return .accentPurple
    case .vintage: return .accentOrange
    case .artistic: return .accentOrange
    case .elegant: return .primaryAccent
    }
  }
}

// MARK: - Generation Confirmation Sheet

struct GenerationConfirmationSheet: View {
  let store: StoreOf<ImageTypeSelection>

  var body: some View {
    NavigationView {
      VStack(spacing: 24) {
        // Header Section
        VStack(spacing: 12) {
          Image(systemName: "sparkles")
            .font(.system(size: 48))
            .foregroundColor(.pink)

          Text("确认生成")
            .font(.title2)
            .fontWeight(.bold)

          Text("即将为您生成专属的婚纱照片")
            .font(.subheadline)
            .foregroundColor(.secondary)
            .multilineTextAlignment(.center)
        }

        // Selected Template Section
        if let selectedTemplate = store.selectedTemplate {
          VStack(alignment: .leading, spacing: 16) {
            Text("选择的风格")
              .font(.headline)

            HStack(spacing: 16) {
              // Template Preview
              ZStack {
                RoundedRectangle(cornerRadius: 12)
                  .fill(
                    LinearGradient(
                      colors: gradientColors(for: selectedTemplate.category),
                      startPoint: .topLeading,
                      endPoint: .bottomTrailing
                    )
                  )
                  .frame(width: 80, height: 80)

                VStack {
                  Image(systemName: selectedTemplate.category.systemImage)
                    .font(.system(size: 24))
                    .foregroundColor(.white)

                  if selectedTemplate.isPremium {
                    Image(systemName: "crown.fill")
                      .font(.caption)
                      .foregroundColor(.yellow)
                  }
                }
              }

              // Template Details
              VStack(alignment: .leading, spacing: 4) {
                HStack {
                  Text(selectedTemplate.name)
                    .font(.headline)

                  if selectedTemplate.isPremium {
                    Image(systemName: "crown.fill")
                      .foregroundColor(.yellow)
                      .font(.caption)
                  }
                }

                Text(selectedTemplate.description)
                  .font(.subheadline)
                  .foregroundColor(.secondary)
                  .lineLimit(2)

                Text("分类: \(selectedTemplate.category.rawValue)")
                  .font(.caption)
                  .foregroundColor(.blue)
              }

              Spacer()
            }
            .padding(16)
            .background(Color.gray.opacity(0.05))
            .cornerRadius(12)
          }
        }

        // Generation Info Section
        VStack(alignment: .leading, spacing: 12) {
          Text("生成信息")
            .font(.headline)

          VStack(spacing: 8) {
            HStack {
              Image(systemName: "clock")
                .foregroundColor(.blue)
              Text("预计时间: 30-60秒")
                .font(.subheadline)
              Spacer()
            }

            HStack {
              Image(systemName: "photo.on.rectangle")
                .foregroundColor(.green)
              Text("将生成高质量婚纱照片")
                .font(.subheadline)
              Spacer()
            }

            HStack {
              Image(systemName: "checkmark.circle")
                .foregroundColor(.orange)
              Text("可保存到相册")
                .font(.subheadline)
              Spacer()
            }
          }
          .padding(16)
          .background(Color.gray.opacity(0.05))
          .cornerRadius(12)
        }

        Spacer()

        // Action Buttons
        VStack(spacing: 12) {
          Button("确认生成") {
            store.send(.confirmGeneration)
          }
          .font(.headline)
          .foregroundColor(.white)
          .frame(maxWidth: .infinity)
          .padding(.vertical, 16)
          .background(
            LinearGradient(
              colors: [.pink, .purple],
              startPoint: .leading,
              endPoint: .trailing
            )
          )
          .cornerRadius(12)

          Button("取消") {
            store.send(.hideGenerationConfirmation)
          }
          .font(.subheadline)
          .foregroundColor(.secondary)
        }
      }
      .padding(20)
      .navigationTitle("确认生成")
      #if os(iOS)
      .navigationBarTitleDisplayMode(.inline)
      #endif
      .toolbar {
        ToolbarItem(placement: .automatic) {
          Button("取消") {
            store.send(.hideGenerationConfirmation)
          }
        }
      }
    }
  }

  private func gradientColors(for category: ImageTemplate.Category) -> [Color] {
    switch category {
    case .wedding: return [.accentPink, .accentPurple]
    case .portrait: return [.accentBlue, .primaryAccent]
    case .fashion: return [.accentPurple, .accentPink]
    case .vintage: return [.accentOrange, .secondaryAccent]
    case .artistic: return [.accentOrange, .accentPink]
    case .elegant: return [.primaryAccent, .accentBlue]
    }
  }
}

#Preview {
  ImageTypeSelectionView(
    store: Store(initialState: ImageTypeSelection.State()) {
      ImageTypeSelection()
    }
  )
}