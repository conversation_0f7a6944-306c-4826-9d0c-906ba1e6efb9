import Foundation
import ComposableArchitecture
import UserNotifications
#if canImport(UIKit)
import UIKit
#endif

// MARK: - Notification Settings Feature

@Reducer
public struct NotificationSettings: Sendable {
  @ObservableState
  public struct State: Equatable, Sendable {
    public var isLoading = false
    public var error: String?
    
    // Notification permissions
    public var hasNotificationPermission = false
    public var isCheckingPermission = false
    
    // Notification preferences
    public var enableGenerationComplete = true
    public var enablePromotions = false
    public var enableTips = true
    public var enableWeeklyDigest = false
    
    // System settings
    public var canOpenSettings = true
    
    public init() {}
  }
  
  public enum Action: Sendable {
    case onAppear
    case checkNotificationPermission
    case notificationPermissionChecked(Bool)
    case requestNotificationPermission
    case notificationPermissionRequested(Bool)
    case openSystemSettings
    case systemSettingsOpened
    
    // Toggle preferences
    case toggleGenerationComplete
    case togglePromotions
    case toggleTips
    case toggleWeeklyDigest
    
    // Error handling
    case errorOccurred(String)
    case clearError
  }
  
  @Dependency(\.notificationClient) var notificationClient
  
  public init() {}
  
  public var body: some ReducerOf<Self> {
    Reduce { state, action in
      switch action {
      case .onAppear:
        return .send(.checkNotificationPermission)
        
      case .checkNotificationPermission:
        state.isCheckingPermission = true
        return .run { send in
          @Dependency(\.notificationClient) var notificationClient
          let hasPermission = await notificationClient.checkPermission()
          await send(.notificationPermissionChecked(hasPermission))
        }

      case .notificationPermissionChecked(let hasPermission):
        state.isCheckingPermission = false
        state.hasNotificationPermission = hasPermission
        return .none

      case .requestNotificationPermission:
        state.isLoading = true
        return .run { send in
          @Dependency(\.notificationClient) var notificationClient
          let granted = await notificationClient.requestPermission()
          await send(.notificationPermissionRequested(granted))
        }
        
      case .notificationPermissionRequested(let granted):
        state.isLoading = false
        state.hasNotificationPermission = granted
        if !granted {
          return .send(.errorOccurred("通知权限被拒绝，请在系统设置中手动开启"))
        }
        return .none
        
      case .openSystemSettings:
        return .run { send in
          @Dependency(\.notificationClient) var notificationClient
          _ = await notificationClient.openSystemSettings()
          await send(.systemSettingsOpened)
        }
        
      case .systemSettingsOpened:
        // Refresh permission status when user returns from settings
        return .send(.checkNotificationPermission)
        
      case .toggleGenerationComplete:
        state.enableGenerationComplete.toggle()
        return .none
        
      case .togglePromotions:
        state.enablePromotions.toggle()
        return .none
        
      case .toggleTips:
        state.enableTips.toggle()
        return .none
        
      case .toggleWeeklyDigest:
        state.enableWeeklyDigest.toggle()
        return .none
        
      case .errorOccurred(let error):
        state.error = error
        state.isLoading = false
        return .none
        
      case .clearError:
        state.error = nil
        return .none
      }
    }
  }
}

// MARK: - Notification Client

@DependencyClient
public struct NotificationClient: Sendable {
  public var checkPermission: @Sendable () async -> Bool = { false }
  public var requestPermission: @Sendable () async -> Bool = { false }
  public var openSystemSettings: @Sendable () async -> Bool = { false }
}

extension NotificationClient: DependencyKey {
  public static let liveValue = NotificationClient(
    checkPermission: {
      let center = UNUserNotificationCenter.current()
      let settings = await center.notificationSettings()
      return settings.authorizationStatus == .authorized
    },
    requestPermission: {
      let center = UNUserNotificationCenter.current()
      do {
        let granted = try await center.requestAuthorization(options: [.alert, .sound, .badge])
        return granted
      } catch {
        return false
      }
    },
    openSystemSettings: {
      #if canImport(UIKit)
      return await MainActor.run {
        guard let settingsUrl = URL(string: UIApplication.openSettingsURLString),
              UIApplication.shared.canOpenURL(settingsUrl) else {
          return false
        }
        UIApplication.shared.open(settingsUrl)
        return true
      }
      #else
      return false
      #endif
    }
  )
  
  public static let testValue = NotificationClient(
    checkPermission: { true },
    requestPermission: { true },
    openSystemSettings: { true }
  )
}

extension DependencyValues {
  public var notificationClient: NotificationClient {
    get { self[NotificationClient.self] }
    set { self[NotificationClient.self] = newValue }
  }
}
