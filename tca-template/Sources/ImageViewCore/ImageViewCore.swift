import Foundation
import ComposableArchitecture
import LoggingClient
import ImageGenerationCore

// MARK: - Image View Feature

@Reducer
public struct ImageView {
  @ObservableState
  public struct State: Equatable, Sendable {
    public var generatedImage: GeneratedImage
    public var isSaving = false
    public var isSharing = false
    public var saveError: String?
    public var shareError: String?
    public var showingSaveSuccess = false
    public var zoomScale: Double = 1.0
    public var showingImageDetails = false
    
    public init(generatedImage: GeneratedImage) {
      self.generatedImage = generatedImage
    }
  }
  
  public enum Action: Sendable {
    case onAppear
    case saveImage
    case shareImage
    case backToTypeSelection
    case zoomChanged(Double)
    case toggleImageDetails
    case saveCompleted
    case saveFailed(String)
    case shareCompleted
    case shareFailed(String)
    case dismissSaveSuccess
    case clearErrors
  }
  
  @Dependency(\.loggingClient) var logger
  
  public init() {}
  
  public var body: some Reducer<State, Action> {
    Reduce { state, action in
      switch action {
      case .onAppear:
        logger.info(.ui, "Image view appeared for image: \(state.generatedImage.id)")
        return .none
        
      case .saveImage:
        logger.info(.ui, "Starting image save process")
        state.isSaving = true
        state.saveError = nil
        
        return .run { [imageData = state.generatedImage.imageData] send in
          do {
            // Simulate save process
            try await Task.sleep(for: .seconds(0.5))
            
            // In real app, save to photo library
            try await saveImageToPhotoLibrary(imageData)
            
            await send(.saveCompleted)
          } catch {
            await send(.saveFailed(error.localizedDescription))
          }
        }
        
      case .shareImage:
        logger.info(.ui, "Starting image share process")
        state.isSharing = true
        state.shareError = nil
        
        return .run { [imageData = state.generatedImage.imageData] send in
          do {
            // Simulate share process
            try await Task.sleep(for: .seconds(0.5))
            
            // In real app, present share sheet
            try await presentShareSheet(imageData)
            
            await send(.shareCompleted)
          } catch {
            await send(.shareFailed(error.localizedDescription))
          }
        }
        
      case .backToTypeSelection:
        logger.info(.ui, "Returning to template selection from image view")
        return .none
        
      case .zoomChanged(let scale):
        state.zoomScale = scale
        return .none
        
      case .toggleImageDetails:
        state.showingImageDetails.toggle()
        return .none
        
      case .saveCompleted:
        logger.info(.ui, "Image saved successfully")
        state.isSaving = false
        state.showingSaveSuccess = true
        return .none
        
      case .saveFailed(let error):
        logger.error(.ui, "Image save failed: \(error)")
        state.isSaving = false
        state.saveError = error
        return .none
        
      case .shareCompleted:
        logger.info(.ui, "Image shared successfully")
        state.isSharing = false
        return .none
        
      case .shareFailed(let error):
        logger.error(.ui, "Image share failed: \(error)")
        state.isSharing = false
        state.shareError = error
        return .none
        
      case .dismissSaveSuccess:
        state.showingSaveSuccess = false
        return .none
        
      case .clearErrors:
        state.saveError = nil
        state.shareError = nil
        return .none
      }
    }
  }
}

// MARK: - Helper Functions

private func saveImageToPhotoLibrary(_ imageData: Data) async throws {
  // Mock implementation
  // In real app, use PHPhotoLibrary to save image
  print("Saving image to photo library")
  
  // Simulate potential failure
  if Double.random(in: 0...1) < 0.1 {
    throw ImageSaveError.photoLibraryAccessDenied
  }
}

private func presentShareSheet(_ imageData: Data) async throws {
  // Mock implementation
  // In real app, present UIActivityViewController
  print("Presenting share sheet")
  
  // Simulate potential failure
  if Double.random(in: 0...1) < 0.05 {
    throw ImageShareError.shareSheetFailed
  }
}

// MARK: - Error Types

public enum ImageSaveError: Error, LocalizedError, Sendable {
  case photoLibraryAccessDenied
  case diskSpaceInsufficient
  case unknown(String)
  
  public var errorDescription: String? {
    switch self {
    case .photoLibraryAccessDenied:
      return "Photo library access denied. Please enable permissions in Settings."
    case .diskSpaceInsufficient:
      return "Insufficient disk space to save image."
    case .unknown(let message):
      return "Failed to save image: \(message)"
    }
  }
}

public enum ImageShareError: Error, LocalizedError, Sendable {
  case shareSheetFailed
  case unknown(String)
  
  public var errorDescription: String? {
    switch self {
    case .shareSheetFailed:
      return "Failed to open share sheet. Please try again."
    case .unknown(let message):
      return "Failed to share image: \(message)"
    }
  }
}