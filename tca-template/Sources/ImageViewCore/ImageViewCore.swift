import Foundation
import ComposableArchitecture
import LoggingClient
import ImageGenerationCore

#if canImport(UIKit)
import UIKit
import Photos
#endif

// MARK: - Image View Feature

@Reducer
public struct ImageView {
  @ObservableState
  public struct State: Equatable, Sendable {
    public var generatedImage: GeneratedImage
    public var isSaving = false
    public var isSharing = false
    public var saveError: String?
    public var shareError: String?
    public var showingSaveSuccess = false
    public var zoomScale: Double = 1.0
    public var showingImageDetails = false
    
    public init(generatedImage: GeneratedImage) {
      self.generatedImage = generatedImage
    }
  }
  
  public enum Action: Sendable {
    case onAppear
    case saveImage(Data?)  // Pass the actual image data
    case shareImage(Data?)  // Pass the actual image data
    case backToTypeSelection
    case zoomChanged(Double)
    case toggleImageDetails
    case saveCompleted
    case saveFailed(String)
    case shareCompleted
    case shareFailed(String)
    case dismissSaveSuccess
    case clearErrors
  }
  
  @Dependency(\.loggingClient) var logger
  
  public init() {}
  
  public var body: some Reducer<State, Action> {
    Reduce { state, action in
      switch action {
      case .onAppear:
        logger.info(.ui, "Image view appeared for image: \(state.generatedImage.id)")
        return .none
        
      case .saveImage(let imageData):
        logger.info(.ui, "Starting image save process")
        state.isSaving = true
        state.saveError = nil

        return .run { send in
          do {
            guard let imageData = imageData else {
              await send(.saveFailed("No image data available"))
              return
            }

            // Save to photo library
            try await saveImageToPhotoLibrary(imageData)

            await send(.saveCompleted)
          } catch {
            await send(.saveFailed(error.localizedDescription))
          }
        }
        
      case .shareImage(let imageData):
        logger.info(.ui, "Starting image share process")
        state.isSharing = true
        state.shareError = nil

        return .run { send in
          do {
            guard let imageData = imageData else {
              await send(.shareFailed("No image data available"))
              return
            }

            // Present share sheet
            try await presentShareSheet(imageData)

            await send(.shareCompleted)
          } catch {
            await send(.shareFailed(error.localizedDescription))
          }
        }
        
      case .backToTypeSelection:
        logger.info(.ui, "Returning to template selection from image view")
        return .none
        
      case .zoomChanged(let scale):
        state.zoomScale = scale
        return .none
        
      case .toggleImageDetails:
        state.showingImageDetails.toggle()
        return .none
        
      case .saveCompleted:
        logger.info(.ui, "Image saved successfully")
        state.isSaving = false
        state.showingSaveSuccess = true
        return .none
        
      case .saveFailed(let error):
        logger.error(.ui, "Image save failed: \(error)")
        state.isSaving = false
        state.saveError = error
        return .none
        
      case .shareCompleted:
        logger.info(.ui, "Image shared successfully")
        state.isSharing = false
        return .none
        
      case .shareFailed(let error):
        logger.error(.ui, "Image share failed: \(error)")
        state.isSharing = false
        state.shareError = error
        return .none
        
      case .dismissSaveSuccess:
        state.showingSaveSuccess = false
        return .none
        
      case .clearErrors:
        state.saveError = nil
        state.shareError = nil
        return .none
      }
    }
  }
}

// MARK: - Helper Functions

private func saveImageToPhotoLibrary(_ imageData: Data) async throws {
  #if canImport(UIKit)
  print("🔄 [ImageView] Starting photo library save process")

  // Check photo library permission
  let authStatus = PHPhotoLibrary.authorizationStatus(for: .addOnly)

  switch authStatus {
  case .notDetermined:
    print("🔄 [ImageView] Requesting photo library permission")
    let newStatus = await PHPhotoLibrary.requestAuthorization(for: .addOnly)
    if newStatus != .authorized {
      print("❌ [ImageView] Photo library permission denied")
      throw ImageSaveError.photoLibraryAccessDenied
    }

  case .denied, .restricted:
    print("❌ [ImageView] Photo library access denied or restricted")
    throw ImageSaveError.photoLibraryAccessDenied

  case .authorized, .limited:
    print("✅ [ImageView] Photo library access authorized")
    break

  @unknown default:
    print("❌ [ImageView] Unknown photo library authorization status")
    throw ImageSaveError.photoLibraryAccessDenied
  }

  // Convert Data to UIImage
  guard let uiImage = UIImage(data: imageData) else {
    print("❌ [ImageView] Failed to create UIImage from data")
    throw ImageSaveError.unknown("Failed to create image from data")
  }

  print("🔄 [ImageView] Saving image to photo library")

  // Save to photo library
  try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
    PHPhotoLibrary.shared().performChanges({
      PHAssetChangeRequest.creationRequestForAsset(from: uiImage)
    }) { success, error in
      if success {
        print("✅ [ImageView] Image saved to photo library successfully")
        continuation.resume()
      } else {
        print("❌ [ImageView] Failed to save image: \(error?.localizedDescription ?? "Unknown error")")
        continuation.resume(throwing: error ?? ImageSaveError.unknown("Failed to save image"))
      }
    }
  }
  #else
  print("❌ [ImageView] Photo library saving not supported on this platform")
  throw ImageSaveError.unknown("Photo library saving not supported on this platform")
  #endif
}

private func presentShareSheet(_ imageData: Data) async throws {
  #if canImport(UIKit)
  print("🔄 [ImageView] Presenting share sheet")

  // Convert Data to UIImage
  guard let uiImage = UIImage(data: imageData) else {
    print("❌ [ImageView] Failed to create UIImage from data for sharing")
    throw ImageShareError.unknown("Failed to create image from data")
  }

  await MainActor.run {
    // Get the current window scene and root view controller
    guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
          let window = windowScene.windows.first,
          let rootViewController = window.rootViewController else {
      print("❌ [ImageView] Could not find root view controller")
      return
    }

    // Find the topmost view controller
    var topViewController = rootViewController
    while let presentedViewController = topViewController.presentedViewController {
      topViewController = presentedViewController
    }

    // Create and present activity view controller
    let activityViewController = UIActivityViewController(
      activityItems: [uiImage],
      applicationActivities: nil
    )

    // Configure for iPad
    if let popover = activityViewController.popoverPresentationController {
      popover.sourceView = topViewController.view
      popover.sourceRect = CGRect(x: topViewController.view.bounds.midX,
                                  y: topViewController.view.bounds.midY,
                                  width: 0, height: 0)
      popover.permittedArrowDirections = []
    }

    topViewController.present(activityViewController, animated: true) {
      print("✅ [ImageView] Share sheet presented successfully")
    }
  }
  #else
  print("❌ [ImageView] Share sheet not supported on this platform")
  throw ImageShareError.unknown("Share sheet not supported on this platform")
  #endif
}

// MARK: - Error Types

public enum ImageSaveError: Error, LocalizedError, Sendable {
  case photoLibraryAccessDenied
  case diskSpaceInsufficient
  case unknown(String)
  
  public var errorDescription: String? {
    switch self {
    case .photoLibraryAccessDenied:
      return "Photo library access denied. Please enable permissions in Settings."
    case .diskSpaceInsufficient:
      return "Insufficient disk space to save image."
    case .unknown(let message):
      return "Failed to save image: \(message)"
    }
  }
}

public enum ImageShareError: Error, LocalizedError, Sendable {
  case shareSheetFailed
  case unknown(String)
  
  public var errorDescription: String? {
    switch self {
    case .shareSheetFailed:
      return "Failed to open share sheet. Please try again."
    case .unknown(let message):
      return "Failed to share image: \(message)"
    }
  }
}