import Dependencies
import DependenciesMacros
import Foundation
import Security

// MARK: - Keychain Client

@DependencyClient
public struct KeychainClient: Sendable {
  public var save: @Sendable (_ key: String, _ data: Data) async throws -> Void
  public var load: @Sendable (_ key: String) async throws -> Data?
  public var delete: @Sendable (_ key: String) async throws -> Void
  public var deleteAll: @Sendable () async throws -> Void
}

// MARK: - Keychain Error

public enum KeychainError: Error, Equatable, LocalizedError {
  case itemNotFound
  case duplicateItem
  case invalidData
  case unexpectedStatus(OSStatus)
  case encodingError
  case decodingError
  
  public var errorDescription: String? {
    switch self {
    case .itemNotFound:
      return "Keychain item not found"
    case .duplicateItem:
      return "Keychain item already exists"
    case .invalidData:
      return "Invalid data provided"
    case .unexpectedStatus(let status):
      return "Unexpected keychain status: \(status)"
    case .encodingError:
      return "Failed to encode data"
    case .decodingError:
      return "Failed to decode data"
    }
  }
}

// MARK: - Live Implementation

extension KeychainClient: DependencyKey {
  public static let liveValue = Self(
    save: { key, data in
      let query: [String: Any] = [
        kSecClass as String: kSecClassGenericPassword,
        kSecAttrAccount as String: key,
        kSecAttrService as String: "com.bridal.app",
        kSecValueData as String: data,
        kSecAttrAccessible as String: kSecAttrAccessibleWhenUnlockedThisDeviceOnly
      ]
      
      // Delete existing item first
      SecItemDelete(query as CFDictionary)
      
      // Add new item
      let status = SecItemAdd(query as CFDictionary, nil)
      
      guard status == errSecSuccess else {
        if status == errSecDuplicateItem {
          throw KeychainError.duplicateItem
        }
        throw KeychainError.unexpectedStatus(status)
      }
    },
    
    load: { key in
      let query: [String: Any] = [
        kSecClass as String: kSecClassGenericPassword,
        kSecAttrAccount as String: key,
        kSecAttrService as String: "com.bridal.app",
        kSecReturnData as String: true,
        kSecMatchLimit as String: kSecMatchLimitOne
      ]
      
      var result: AnyObject?
      let status = SecItemCopyMatching(query as CFDictionary, &result)
      
      guard status == errSecSuccess else {
        if status == errSecItemNotFound {
          return nil
        }
        throw KeychainError.unexpectedStatus(status)
      }
      
      guard let data = result as? Data else {
        throw KeychainError.invalidData
      }
      
      return data
    },
    
    delete: { key in
      let query: [String: Any] = [
        kSecClass as String: kSecClassGenericPassword,
        kSecAttrAccount as String: key,
        kSecAttrService as String: "com.bridal.app"
      ]
      
      let status = SecItemDelete(query as CFDictionary)
      
      guard status == errSecSuccess || status == errSecItemNotFound else {
        throw KeychainError.unexpectedStatus(status)
      }
    },
    
    deleteAll: {
      let query: [String: Any] = [
        kSecClass as String: kSecClassGenericPassword,
        kSecAttrService as String: "com.bridal.app"
      ]
      
      let status = SecItemDelete(query as CFDictionary)
      
      guard status == errSecSuccess || status == errSecItemNotFound else {
        throw KeychainError.unexpectedStatus(status)
      }
    }
  )
}

// MARK: - Test Implementation

extension KeychainClient: TestDependencyKey {
  public static let testValue = Self(
    save: { _, _ in },
    load: { _ in nil },
    delete: { _ in },
    deleteAll: { }
  )
}

// MARK: - Dependency Registration

extension DependencyValues {
  public var keychainClient: KeychainClient {
    get { self[KeychainClient.self] }
    set { self[KeychainClient.self] = newValue }
  }
}

// MARK: - Convenience Extensions

public extension KeychainClient {
  func save<T: Codable>(_ value: T, forKey key: String) async throws {
    let encoder = JSONEncoder()
    let data = try encoder.encode(value)
    try await save(key, data)
  }
  
  func load<T: Codable>(_ type: T.Type, forKey key: String) async throws -> T? {
    guard let data = try await load(key) else { return nil }
    let decoder = JSONDecoder()
    return try decoder.decode(type, from: data)
  }
  
  func saveString(_ string: String, forKey key: String) async throws {
    guard let data = string.data(using: .utf8) else {
      throw KeychainError.encodingError
    }
    try await save(key, data)
  }
  
  func loadString(forKey key: String) async throws -> String? {
    guard let data = try await load(key) else { return nil }
    guard let string = String(data: data, encoding: .utf8) else {
      throw KeychainError.decodingError
    }
    return string
  }
}

// MARK: - Keychain Keys

public enum KeychainKeys {
  public static let accessToken = "access_token"
  public static let refreshToken = "refresh_token"
  public static let userID = "user_id"
  public static let userEmail = "user_email"
  public static let userDisplayName = "user_display_name"
  public static let authProvider = "auth_provider"
  public static let subscriptionStatus = "subscription_status"
  public static let lastLoginDate = "last_login_date"
}
