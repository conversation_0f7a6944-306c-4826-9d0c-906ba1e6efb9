import AuthenticationClient
import ComposableArchitecture
import LoginCore
import SwiftUI
import UserStateSwiftUI
#if canImport(UIKit)
import UIKit
#endif

public struct LoginView: View {
  @Perception.Bindable public var store: StoreOf<Login>
  @State private var isPrivacyPolicyAccepted = false

  public init(store: StoreOf<Login>) {
    self.store = store
  }

  public var body: some View {
    WithPerceptionTracking {
      ScrollView {
        VStack(spacing: 40) {
          Spacer(minLength: 60)

          // Header Section
          VStack(spacing: 24) {
            // App Icon with enhanced design
            ZStack {
              Circle()
                .fill(
                  LinearGradient(
                    colors: [.pink.opacity(0.1), .purple.opacity(0.1)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                  )
                )
                .frame(width: 120, height: 120)
                .shadow(color: .pink.opacity(0.2), radius: 20, x: 0, y: 8)

              Image(systemName: "person.crop.circle.badge.plus")
                .font(.system(size: 50, weight: .light))
                .foregroundStyle(
                  LinearGradient(
                    colors: [.pink, .purple],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                  )
                )
            }

            // Welcome Text
            VStack(spacing: 12) {
              Text("欢迎来到婚纱助手")
                .font(.title)
                .fontWeight(.bold)
                .foregroundColor(.primary)

              Text("使用Apple ID安全快速登录\n享受个性化婚纱设计服务")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .lineSpacing(4)
            }
          }

          // Login Section
          VStack(spacing: 24) {
            // Privacy Policy Checkbox (moved up)
            privacyPolicySection

            // Apple Sign In Button (simplified)
            if #available(iOS 13.0, macOS 10.15, *) {
              QuickAppleSignInButton(
                onSignIn: { credential in
                  print("🍎 LoginView: Apple ID登录凭证获取成功")
                  // 调用 AuthenticationClient 的 signInWithApple 方法
                  store.send(.appleSignIn(credential))
                },
                onError: { error in
                  print("🍎 LoginView: Apple ID登录失败: \(error.localizedDescription)")
                  store.send(.loginResponse(.failure(error)))
                }
              )
              .disabled(!isPrivacyPolicyAccepted)
              .opacity(isPrivacyPolicyAccepted ? 1.0 : 0.6)
              .animation(.easeInOut(duration: 0.2), value: isPrivacyPolicyAccepted)
            }
          }
          .padding(.horizontal, 32)

          Spacer(minLength: 40)
        }
      }
      .navigationTitle("登录")
      #if os(iOS)
      .navigationBarTitleDisplayMode(.inline)
      #endif
      .background(
        LinearGradient(
          colors: [
            Color.white,
            Color.white.opacity(0.95),
            Color.pink.opacity(0.02)
          ],
          startPoint: .top,
          endPoint: .bottom
        )
        .ignoresSafeArea()
      )
    }
  }

  // MARK: - Privacy Policy Section

  @ViewBuilder
  private var privacyPolicySection: some View {
    VStack(spacing: 16) {
      // Checkbox with agreement text
      HStack(alignment: .top, spacing: 12) {
        Button(action: {
          withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
            isPrivacyPolicyAccepted.toggle()
          }
        }) {
          ZStack {
            RoundedRectangle(cornerRadius: 6)
              .stroke(
                isPrivacyPolicyAccepted ? Color.pink : Color.gray.opacity(0.5),
                lineWidth: 2
              )
              .frame(width: 24, height: 24)
              .background(
                RoundedRectangle(cornerRadius: 6)
                  .fill(isPrivacyPolicyAccepted ? Color.pink : Color.clear)
              )

            if isPrivacyPolicyAccepted {
              Image(systemName: "checkmark")
                .font(.system(size: 14, weight: .bold))
                .foregroundColor(.white)
                .scaleEffect(isPrivacyPolicyAccepted ? 1.0 : 0.5)
                .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isPrivacyPolicyAccepted)
            }
          }
        }
        .buttonStyle(PlainButtonStyle())

        // Agreement text with clickable links
        VStack(alignment: .leading, spacing: 4) {
          HStack(alignment: .top, spacing: 0) {
            Text("我已阅读并同意")
              .font(.footnote)
              .foregroundColor(.secondary)

            Button("《隐私政策》") {
              openPrivacyPolicy()
            }
            .font(.footnote)
            .foregroundColor(.pink)
            .buttonStyle(PlainButtonStyle())

            Text("和")
              .font(.footnote)
              .foregroundColor(.secondary)

            Button("《用户协议》") {
              openUserAgreement()
            }
            .font(.footnote)
            .foregroundColor(.pink)
            .buttonStyle(PlainButtonStyle())
          }
        }

        Spacer()
      }
      .padding(.horizontal, 4)

      // Security notice
      HStack(spacing: 8) {
        Image(systemName: "lock.shield")
          .font(.caption)
          .foregroundColor(.green)

        Text("您的隐私和数据安全是我们的首要关注")
          .font(.caption2)
          .foregroundColor(.secondary)
          .multilineTextAlignment(.leading)

        Spacer()
      }
      .padding(.horizontal, 4)
    }
    .padding(.vertical, 8)
  }

  // MARK: - Helper Methods

  private func openPrivacyPolicy() {
    print("📄 打开隐私政策")
    // TODO: 实现隐私政策页面导航
    // 可以使用 SafariView 或者应用内 WebView
    #if canImport(UIKit)
    if let url = URL(string: "https://example.com/privacy-policy") {
      UIApplication.shared.open(url)
    }
    #endif
  }

  private func openUserAgreement() {
    print("📄 打开用户协议")
    // TODO: 实现用户协议页面导航
    #if canImport(UIKit)
    if let url = URL(string: "https://example.com/user-agreement") {
      UIApplication.shared.open(url)
    }
    #endif
  }
}

#Preview {
  NavigationView {
    LoginView(
      store: Store(initialState: Login.State()) {
        Login()
      }
    )
  }
}
