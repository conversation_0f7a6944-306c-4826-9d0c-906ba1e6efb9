import Dependencies
import DependenciesMacros
import Foundation
import CommonUI
import LoggingClient
import NetworkClient

public struct AuthenticationResponse: Equatable, Sendable {
  public var token: String
  public var twoFactorRequired: Bool
  public var user: AuthenticatedUser?

  public init(
    token: String,
    twoFactorRequired: Bool,
    user: AuthenticatedUser? = nil
  ) {
    self.token = token
    self.twoFactorRequired = twoFactorRequired
    self.user = user
  }
}

public struct AuthenticatedUser: Equatable, Sendable {
  public let id: String
  public let email: String
  public let displayName: String
  public let avatarURL: String?
  public let authProvider: AuthProvider

  public init(
    id: String,
    email: String,
    displayName: String,
    avatarURL: String? = nil,
    authProvider: AuthProvider
  ) {
    self.id = id
    self.email = email
    self.displayName = displayName
    self.avatarURL = avatarURL
    self.authProvider = authProvider
  }
}

public enum AuthProvider: String, Equatable, Sendable {
  case email = "email"
  case apple = "apple"
}

public struct AppleIDCredential: Equatable, Sendable {
  public let userID: String
  public let email: String?
  public let fullName: PersonNameComponents?
  public let identityToken: Data?
  public let authorizationCode: Data?

  public init(
    userID: String,
    email: String?,
    fullName: PersonNameComponents?,
    identityToken: Data?,
    authorizationCode: Data?
  ) {
    self.userID = userID
    self.email = email
    self.fullName = fullName
    self.identityToken = identityToken
    self.authorizationCode = authorizationCode
  }
}

public enum AuthenticationError: Equatable, LocalizedError, Sendable {
  case invalidUserPassword
  case invalidTwoFactor
  case invalidIntermediateToken
  case appleSignInFailed
  case appleSignInCancelled
  case networkError
  case invalidCredentials

  public var errorDescription: String? {
    switch self {
    case .invalidUserPassword:
      return "Unknown user or invalid password."
    case .invalidTwoFactor:
      return "Invalid second factor (try 1234)"
    case .invalidIntermediateToken:
      return "404!! What happened to your token there bud?!?!"
    case .appleSignInFailed:
      return "Apple Sign In failed. Please try again."
    case .appleSignInCancelled:
      return "Apple Sign In was cancelled."
    case .networkError:
      return "Network error. Please check your connection."
    case .invalidCredentials:
      return "Invalid credentials provided."
    }
  }
}

@DependencyClient
public struct AuthenticationClient: Sendable {
  public var login:
    @Sendable (_ email: String, _ password: String) async throws -> AuthenticationResponse
  public var twoFactor:
    @Sendable (_ code: String, _ token: String) async throws -> AuthenticationResponse
  public var signInWithApple:
    @Sendable (_ appleIDCredential: AppleIDCredential) async throws -> AuthenticationResponse
  public var logout:
    @Sendable () async throws -> Void
}

extension AuthenticationClient: TestDependencyKey {
  public static let testValue = Self()
}

extension AuthenticationClient: DependencyKey {
  public static let liveValue = Self(
    login: { email, password in
      @Dependency(\.loggingClient) var logger
      
      // Mock implementation - always succeed for testing
      logger.info(.authentication, "Mock email login started for: \(email)")
      try await Task.sleep(for: .seconds(AppConstants.Network.mockDelayMedium))

      // Generate a more realistic display name based on email
      let displayName = generateDisplayName(from: email)
      let userId = "\(AppConstants.Authentication.emailUserIDPrefix)\(UUID().uuidString.prefix(AppConstants.Authentication.userIDLength))"

      logger.info(.authentication, "Mock email login successful for: \(displayName)")

      // Return mock successful authentication with detailed user info
      return AuthenticationResponse(
        token: "\(AppConstants.Authentication.mockTokenPrefix)-\(Date().timeIntervalSince1970)",
        twoFactorRequired: false,
        user: AuthenticatedUser(
          id: userId,
          email: email,
          displayName: displayName,
          avatarURL: nil,
          authProvider: .email
        )
      )
    },
    twoFactor: { token, code in
      @Dependency(\.loggingClient) var logger
      
      // Mock two-factor implementation
      logger.info(.authentication, "Two-factor authentication started")
      try await Task.sleep(for: .seconds(AppConstants.Network.mockDelayShort))
      logger.info(.authentication, "Two-factor authentication successful")
      
      return AuthenticationResponse(
        token: AppConstants.Authentication.mock2FAToken,
        twoFactorRequired: false,
        user: AuthenticatedUser(
          id: AppConstants.Authentication.mock2FAUserID,
          email: AppConstants.Authentication.default2FAEmail,
          displayName: "2FA User",
          authProvider: .email
        )
      )
    },
    signInWithApple: { credential in
      // 简化实现，直接在这里处理所有逻辑
      print("🚀 AuthenticationClient.signInWithApple 开始执行")

      // Mock implementation - always succeed for testing
      print("ℹ️ Apple Sign In started")
      try await Task.sleep(for: .seconds(AppConstants.Network.mockDelayMedium))

      let displayName = credential.fullName?.formatted() ?? "Apple User"
      print("ℹ️ Apple Sign In successful for: \(displayName)")

      // 异步调用 Apple OAuth API 保存数据到数据库
      // 使用简化的方式，直接调用网络请求
      Task.detached {
        await callAppleOAuthAPI(credential: credential)
      }

      // Return mock successful Apple authentication
      return AuthenticationResponse(
        token: AppConstants.Authentication.mockAppleToken,
        twoFactorRequired: false,
        user: AuthenticatedUser(
          id: AppConstants.Authentication.mockAppleUserID,
          email: credential.email ?? AppConstants.Authentication.defaultAppleEmail,
          displayName: displayName,
          avatarURL: nil,
          authProvider: .apple
        )
      )
    },
    logout: {
      @Dependency(\.loggingClient) var logger
      
      // Mock implementation - in real app would clear tokens
      logger.info(.authentication, "User logout started")
      try await Task.sleep(for: .seconds(AppConstants.Network.mockDelayShort))
      logger.info(.authentication, "User logout completed")
    }
  )
}

// MARK: - Helper Functions

private func generateDisplayName(from email: String) -> String {
  // Extract username from email and create a friendly display name
  let username = email.components(separatedBy: "@").first ?? "用户"

  // Common email patterns to friendly names
  let commonPatterns: [String: String] = [
    "admin": "管理员",
    "test": "测试用户",
    "demo": "演示用户",
    "user": "普通用户",
    "guest": "访客用户"
  ]

  // Check for common patterns
  for (pattern, friendlyName) in commonPatterns {
    if username.lowercased().contains(pattern) {
      return friendlyName
    }
  }

  // Generate a friendly name based on username
  if username.count > 2 {
    return "\(username.capitalized)用户"
  } else {
    return "邮箱用户"
  }
}

extension DependencyValues {
  public var authenticationClient: AuthenticationClient {
    get { self[AuthenticationClient.self] }
    set { self[AuthenticationClient.self] = newValue }
  }
}

// MARK: - Apple OAuth API Models

/// Apple OAuth 登录请求数据 - 支持首次和非首次登录
public struct AppleOAuthRequest: Codable, Sendable {
  public let identityToken: String
  public let platform: String
  public let userInfo: UserInfo?  // 可选的用户信息（首次授权时提供）
  public let realEmail: String?   // 可选的真实邮箱（客户端获取的真实邮箱）

  public init(identityToken: String, platform: String = "ios", userInfo: UserInfo? = nil, realEmail: String? = nil) {
    self.identityToken = identityToken
    self.platform = platform
    self.userInfo = userInfo
    self.realEmail = realEmail
  }

  // 确保 JSON 字段名与后端 API 匹配
  private enum CodingKeys: String, CodingKey {
    case identityToken = "identity_token"
    case platform = "platform"
    case userInfo = "user_info"
    case realEmail = "real_email"
  }
}

/// 用户信息结构（首次授权时使用）
public struct UserInfo: Codable, Sendable {
  public let firstName: String?
  public let lastName: String?
  public let email: String?  // 可选的真实邮箱

  public init(firstName: String?, lastName: String?, email: String? = nil) {
    self.firstName = firstName
    self.lastName = lastName
    self.email = email
  }

  private enum CodingKeys: String, CodingKey {
    case firstName = "firstName"
    case lastName = "lastName"
    case email = "email"
  }
}

/// Apple OAuth 登录响应数据 - 匹配后端实际返回格式
public struct AppleOAuthResponse: Codable, Sendable {
  public let accessToken: String
  public let tokenType: String
  public let user: AppleOAuthUser
  public let isNewUser: Bool
  public let trialStatus: TrialStatus?

  private enum CodingKeys: String, CodingKey {
    case accessToken = "access_token"
    case tokenType = "token_type"
    case user = "user"
    case isNewUser = "is_new_user"
    case trialStatus = "trial_status"
  }
}

/// Apple OAuth 用户数据
public struct AppleOAuthUser: Codable, Sendable {
  public let id: String
  public let email: String
  public let isActive: Bool
  public let isSuperuser: Bool
  public let fullName: String
  public let platform: String
  public let createdAt: String
  public let lastLogin: String
  public let authProvider: String
  public let providerUserId: String
  public let avatarUrl: String?

  private enum CodingKeys: String, CodingKey {
    case id = "id"
    case email = "email"
    case isActive = "is_active"
    case isSuperuser = "is_superuser"
    case fullName = "full_name"
    case platform = "platform"
    case createdAt = "created_at"
    case lastLogin = "last_login"
    case authProvider = "auth_provider"
    case providerUserId = "provider_user_id"
    case avatarUrl = "avatar_url"
  }
}

/// 试用状态
public struct TrialStatus: Codable, Sendable {
  public let hasTrial: Bool
  public let trialCredits: Int
  public let remainingCredits: Int
  public let usedCredits: Int
  public let trialExhausted: Bool
  public let createdAt: String

  private enum CodingKeys: String, CodingKey {
    case hasTrial = "has_trial"
    case trialCredits = "trial_credits"
    case remainingCredits = "remaining_credits"
    case usedCredits = "used_credits"
    case trialExhausted = "trial_exhausted"
    case createdAt = "created_at"
  }
}

// MARK: - 简化的 Apple OAuth API 调用
@Sendable
func callAppleOAuthAPI(credential: AppleIDCredential) async {
  print("🔧 开始简化的 Apple OAuth API 调用")

  do {
    // 构建请求数据
    guard let identityTokenData = credential.identityToken,
          let identityTokenString = String(data: identityTokenData, encoding: .utf8) else {
      print("❌ Identity token 无效")
      return
    }

    print("✅ Identity token 获取成功，长度: \(identityTokenString.count)")

    // 检查是否有用户信息（首次授权时提供）
    var userInfo: UserInfo? = nil
    if let fullName = credential.fullName {
      let firstName = fullName.givenName
      let lastName = fullName.familyName

      // 只有当至少有一个姓名字段时才创建 userInfo
      if (firstName != nil && !firstName!.isEmpty) || (lastName != nil && !lastName!.isEmpty) {
        userInfo = UserInfo(firstName: firstName, lastName: lastName, email: credential.email)
        print("✅ 检测到用户信息:")
        print("   姓: \(firstName ?? "无")")
        print("   名: \(lastName ?? "无")")
        print("   邮箱: \(credential.email ?? "无")")
        print("   这是首次授权登录")
      } else {
        print("ℹ️ 用户信息为空，这是非首次登录")
      }
    } else {
      print("ℹ️ 没有用户信息，这是非首次登录")
    }

    // 获取真实邮箱（如果可用）
    let realEmail = credential.email
    if let email = realEmail {
      print("✅ 检测到真实邮箱: \(email)")
    } else {
      print("ℹ️ 未提供真实邮箱")
    }

    let requestData = AppleOAuthRequest(
      identityToken: identityTokenString,
      platform: "ios",
      userInfo: userInfo,
      realEmail: realEmail
    )

    // 构建 URL
    let baseURL = "http://localhost:8000"
    let path = "/api/v1/oauth/apple/login"
    let fullURL = "\(baseURL)\(path)"

    print("🔧 构建网络请求...")
    print("   Base URL: \(baseURL)")
    print("   Path: \(path)")
    print("   Full URL: \(fullURL)")

    guard let url = URL(string: fullURL) else {
      print("❌ URL 构建失败")
      return
    }

    // 构建请求
    var request = URLRequest(url: url)
    request.httpMethod = "POST"
    request.setValue("application/json", forHTTPHeaderField: "Content-Type")
    request.setValue("*/*", forHTTPHeaderField: "Accept")
    request.setValue("localhost:8000", forHTTPHeaderField: "Host")
    request.setValue("keep-alive", forHTTPHeaderField: "Connection")

    // 序列化请求数据
    let jsonData = try JSONEncoder().encode(requestData)
    request.httpBody = jsonData

    // 打印请求详情
    print("✅ 网络请求构建成功")
    print("🔍 请求详情:")
    print("   URL: \(request.url?.absoluteString ?? "无")")
    print("   Method: \(request.httpMethod ?? "无")")
    print("   Headers: \(request.allHTTPHeaderFields ?? [:])")
    print("   包含用户信息: \(userInfo != nil ? "是（首次登录）" : "否（非首次登录）")")
    if let bodyData = request.httpBody,
       let bodyString = String(data: bodyData, encoding: .utf8) {
      print("   Body: \(bodyString)")
    }

    print("🍎 开始调用 Apple OAuth API")

    // 执行网络请求
    let (data, response) = try await URLSession.shared.data(for: request)

    print("✅ 网络请求成功")
    print("   响应数据长度: \(data.count)")

    if let httpResponse = response as? HTTPURLResponse {
      print("   HTTP 状态码: \(httpResponse.statusCode)")
      print("   响应头: \(httpResponse.allHeaderFields)")
    }

    if let responseString = String(data: data, encoding: .utf8) {
      print("   响应内容: \(responseString)")
    }

    // 尝试解析响应
    let apiResponse = try JSONDecoder().decode(AppleOAuthResponse.self, from: data)
    print("✅ JSON 解析成功")

    print("✅ Apple OAuth API 调用成功")
    print("   Access Token: \(apiResponse.accessToken)")
    print("   Token Type: \(apiResponse.tokenType)")
    print("   用户数据: id=\(apiResponse.user.id), email=\(apiResponse.user.email)")
    print("   用户姓名: \(apiResponse.user.fullName)")
    print("   是否新用户: \(apiResponse.isNewUser)")

    // 存储 access_token 到 UserDefaults 和 Keychain（后续请求需要用到）
    UserDefaults.standard.set(apiResponse.accessToken, forKey: "apple_access_token")
    UserDefaults.standard.set(apiResponse.tokenType, forKey: "apple_token_type")

    // 同时存储到 Keychain，供其他 Client 使用
    // 注意：这里是简化版本，实际应用中应该使用 KeychainClient
    let keychainQuery: [String: Any] = [
      kSecClass as String: kSecClassGenericPassword,
      kSecAttrAccount as String: "auth_token",
      kSecValueData as String: apiResponse.accessToken.data(using: .utf8)!
    ]

    // 先删除旧的token
    SecItemDelete(keychainQuery as CFDictionary)

    // 添加新的token
    let status = SecItemAdd(keychainQuery as CFDictionary, nil)
    if status == errSecSuccess {
      print("✅ Access Token 已存储到 UserDefaults 和 Keychain")
    } else {
      print("⚠️ Access Token 存储到 Keychain 失败，状态码: \(status)")
      print("✅ Access Token 已存储到 UserDefaults")
    }

  } catch {
    print("❌ Apple OAuth API 调用失败: \(error.localizedDescription)")
    print("   错误类型: \(type(of: error))")
    if let decodingError = error as? DecodingError {
      print("   解码错误详情: \(decodingError)")
    }
  }
}

// MARK: - Access Token 管理
public struct AccessTokenManager {
  /// 获取存储的 Apple Access Token（优先从UserDefaults获取）
  public static func getAppleAccessToken() -> String? {
    return UserDefaults.standard.string(forKey: "apple_access_token")
  }

  /// 从Keychain获取Access Token（供其他Client使用）
  public static func getKeychainAccessToken() -> String? {
    let query: [String: Any] = [
      kSecClass as String: kSecClassGenericPassword,
      kSecAttrAccount as String: "auth_token",
      kSecReturnData as String: true,
      kSecMatchLimit as String: kSecMatchLimitOne
    ]

    var result: AnyObject?
    let status = SecItemCopyMatching(query as CFDictionary, &result)

    if status == errSecSuccess,
       let data = result as? Data,
       let token = String(data: data, encoding: .utf8) {
      return token
    }

    return nil
  }

  /// 获取存储的 Token Type
  public static func getAppleTokenType() -> String? {
    return UserDefaults.standard.string(forKey: "apple_token_type") ?? "bearer"
  }

  /// 获取完整的 Authorization Header 值（优先使用Apple token）
  public static func getAuthorizationHeader() -> String? {
    // 优先使用Apple token
    if let token = getAppleAccessToken(),
       let tokenType = getAppleTokenType() {
      return "\(tokenType) \(token)"
    }

    // 备用：从Keychain获取
    if let token = getKeychainAccessToken() {
      return "bearer \(token)"
    }

    return nil
  }

  /// 清除存储的 Token
  public static func clearTokens() {
    // 清除UserDefaults
    UserDefaults.standard.removeObject(forKey: "apple_access_token")
    UserDefaults.standard.removeObject(forKey: "apple_token_type")

    // 清除Keychain
    let query: [String: Any] = [
      kSecClass as String: kSecClassGenericPassword,
      kSecAttrAccount as String: "auth_token"
    ]
    SecItemDelete(query as CFDictionary)
  }

  /// 检查是否有有效的 Token
  public static func hasValidToken() -> Bool {
    return getAppleAccessToken() != nil || getKeychainAccessToken() != nil
  }
}

// MARK: - Apple OAuth API Client

/// Apple OAuth API 客户端
public struct AppleOAuthAPIClient: Sendable {
  public init() {}

  /// 调用 Apple OAuth 登录 API
  public func loginWithApple(_ credential: AppleIDCredential) async throws {
    @Dependency(\.networkClient) var networkClient
    @Dependency(\.loggingClient) var logger

    logger.info(.authentication, "🔧 AppleOAuthAPIClient: 开始处理 Apple ID 凭证")
    logger.info(.authentication, "   用户ID: \(credential.userID)")
    logger.info(.authentication, "   邮箱: \(credential.email ?? "未提供")")

    // 确保有 identity token
    guard let identityToken = credential.identityToken,
          let tokenString = String(data: identityToken, encoding: .utf8) else {
      logger.warning(.authentication, "❌ Apple ID credential missing identity token")
      return // 静默失败，不影响主登录流程
    }

    logger.info(.authentication, "✅ Identity token 获取成功，长度: \(tokenString.count)")

    // 检查是否有用户信息（首次授权时提供）
    var userInfo: UserInfo? = nil
    if let fullName = credential.fullName {
      let firstName = fullName.givenName
      let lastName = fullName.familyName

      // 只有当至少有一个姓名字段时才创建 userInfo
      if (firstName != nil && !firstName!.isEmpty) || (lastName != nil && !lastName!.isEmpty) {
        userInfo = UserInfo(firstName: firstName, lastName: lastName, email: credential.email)
        logger.info(.authentication, "✅ 检测到用户信息:")
        logger.info(.authentication, "   姓: \(firstName ?? "无")")
        logger.info(.authentication, "   名: \(lastName ?? "无")")
        logger.info(.authentication, "   邮箱: \(credential.email ?? "无")")
        logger.info(.authentication, "   这是首次授权登录")
      } else {
        logger.info(.authentication, "ℹ️ 用户信息为空，这是非首次登录")
      }
    } else {
      logger.info(.authentication, "ℹ️ 没有用户信息，这是非首次登录")
    }

    // 获取真实邮箱（如果可用）
    let realEmail = credential.email
    if let email = realEmail {
      logger.info(.authentication, "✅ 检测到真实邮箱: \(email)")
    } else {
      logger.info(.authentication, "ℹ️ 未提供真实邮箱")
    }

    // 构建请求数据
    let requestData = AppleOAuthRequest(
      identityToken: tokenString,
      platform: "ios",
      userInfo: userInfo,
      realEmail: realEmail
    )

    do {
      // 构建网络请求
      logger.info(.authentication, "🔧 构建网络请求...")
      logger.info(.authentication, "   Base URL: \(APIEndpoints.baseURL)")
      logger.info(.authentication, "   Path: \(APIEndpoints.appleOAuth)")
      logger.info(.authentication, "   Full URL: \(APIEndpoints.baseURL + APIEndpoints.appleOAuth)")

      let request = try RequestBuilder(baseURL: URL(string: APIEndpoints.baseURL)!)
        .path(APIEndpoints.appleOAuth)
        .method(.POST)
        .contentTypeJSON()
        .acceptJSON()
        .jsonBody(requestData)
        .timeout(10.0) // 较短的超时时间，避免影响用户体验
        .build()

      logger.info(.authentication, "✅ 网络请求构建成功")
      logger.info(.authentication, "🍎 开始调用 Apple OAuth API")

      // 直接执行网络请求（现在依赖已经正确传递）
      logger.info(.authentication, "🔍 开始执行网络请求...")
      let responseData = try await networkClient.request(request)
      logger.info(.authentication, "✅ 网络请求成功，响应数据长度: \(responseData.count)")

      let response = try JSONDecoder().decode(AppleOAuthResponse.self, from: responseData)
      logger.info(.authentication, "✅ JSON 解析成功")

      logger.info(.authentication, "✅ Apple OAuth API 调用成功")
      logger.info(.authentication, "   Access Token: \(response.accessToken)")
      logger.info(.authentication, "   用户数据: id=\(response.user.id), email=\(response.user.email)")
      logger.info(.authentication, "   用户姓名: \(response.user.fullName)")
      logger.info(.authentication, "   是否新用户: \(response.isNewUser)")

      // 存储 access_token 到 UserDefaults 和 Keychain（后续请求需要用到）
      UserDefaults.standard.set(response.accessToken, forKey: "apple_access_token")
      UserDefaults.standard.set(response.tokenType, forKey: "apple_token_type")

      // 同时存储到 Keychain，供其他 Client 使用
      // 使用简化的Keychain存储方式
      let keychainQuery: [String: Any] = [
        kSecClass as String: kSecClassGenericPassword,
        kSecAttrAccount as String: "auth_token",
        kSecValueData as String: response.accessToken.data(using: .utf8)!
      ]

      // 先删除旧的token
      SecItemDelete(keychainQuery as CFDictionary)

      // 添加新的token
      let status = SecItemAdd(keychainQuery as CFDictionary, nil)
      if status == errSecSuccess {
        logger.info(.authentication, "✅ Access Token 已存储到 UserDefaults 和 Keychain")
      } else {
        logger.warning(.authentication, "⚠️ Access Token 存储到 Keychain 失败，状态码: \(status)")
        logger.info(.authentication, "✅ Access Token 已存储到 UserDefaults")
      }

      logger.info(.authentication, "🍎 Apple OAuth API 调用完成")

    } catch {
      logger.error(.authentication, "❌ Apple OAuth API 请求构建失败: \(error.localizedDescription)")
      // 静默失败，不影响主登录流程
    }
  }
}

// MARK: - RequestBuilder JSON Extension

private extension RequestBuilder {
  func jsonBody<T: Encodable>(_ body: T) -> Self {
    do {
      let jsonData = try JSONEncoder().encode(body)
      return self.body(jsonData)
    } catch {
      // 如果编码失败，返回不带 body 的 builder
      return self
    }
  }
}
