import Foundation
import ComposableArchitecture
import LoginCore
import LaunchCore
import MainTabCore
import SubscriptionCore
import UserStateCore
import UserStorageClient
import LoggingClient

// MARK: - Bridal AI App Feature

@Reducer
public enum AppFeature {
  case launch(Launch)
  case login(Login)
  case mainTab(MainTab)  // 新的TabBar主页面

  public static var body: some ReducerOf<Self> {
    Reduce<State, Action> { state, action in
      @Dependency(\.loggingClient) var logger
      @Dependency(\.userStorageClient) var userStorageClient
      
      switch action {
      // Launch Screen Logic
      case .launch(.checkAuthenticationStatus):
        logger.info(.app, "🚀 AppCore: Checking authentication status")
        return .none
        
      case .launch(.userIsAuthenticated(let user, _)):
        logger.info(.app, "✅ AppCore: User authenticated (\(user.displayName)), navigating to main tab")
        print("🔄 AppCore: Auto-login successful for user: \(user.email)")
        print("📱 Starting main tab interface")
        state = .mainTab(MainTab.State(user: user))
        return .none
        
      case .launch(.userNotAuthenticated):
        logger.info(.app, "❌ AppCore: User not authenticated, showing login options")
        print("🔄 AppCore: Transitioning from launch to login state")
        state = .login(Login.State())
        return .none

      // Login Flow - Navigate to main tab after successful authentication
      case .login(.loginResponse(.success(let response))):
        logger.info(.app, "Login successful, navigating to main tab")

        // 从登录响应中获取用户数据并传递给MainTab
        if let authenticatedUser = response.user {
          // 先创建默认的MainTab状态，然后异步加载用户数据
          state = .mainTab(MainTab.State())

          return .run { send in
            // 等待一小段时间，确保LoginCore完成数据保存
            try await Task.sleep(nanoseconds: 100_000_000) // 0.1秒

            do {
              // 从Keychain加载完整的用户数据（包括最新的订阅状态）
              if let (user, _) = try await userStorageClient.loadUser() {
                print("✅ AppCore: 从Keychain加载用户数据成功，更新MainTab")
                print("   用户: \(user.displayName) (\(user.email))")
                print("   订阅状态: \(user.subscriptionStatus.displayName)")

                // 发送action到MainTab来设置用户数据
                await send(.mainTab(.setUserData(user)))
              } else {
                print("⚠️ AppCore: 无法从Keychain加载用户数据，使用登录响应数据")
                // 如果无法从Keychain加载，使用登录响应中的数据，但要获取正确的订阅状态
                let currentSubscriptionStatus = UserPersistenceService.restoreSubscriptionStatus() ?? .free
                print("🔄 AppCore: 为fallback用户恢复订阅状态: \(currentSubscriptionStatus.displayName)")

                let fallbackUser = User(
                  id: authenticatedUser.id,
                  email: authenticatedUser.email,
                  displayName: authenticatedUser.displayName,
                  avatarURL: authenticatedUser.avatarURL,
                  createdAt: Date(),
                  subscriptionStatus: currentSubscriptionStatus // 使用恢复的订阅状态
                )
                await send(.mainTab(.setUserData(fallbackUser)))
              }
            } catch {
              print("❌ AppCore: 加载用户数据失败: \(error)")
              // 即使失败也要尝试设置基本用户数据，但要获取正确的订阅状态
              let currentSubscriptionStatus = UserPersistenceService.restoreSubscriptionStatus() ?? .free
              print("🔄 AppCore: 为错误恢复用户恢复订阅状态: \(currentSubscriptionStatus.displayName)")

              let fallbackUser = User(
                id: authenticatedUser.id,
                email: authenticatedUser.email,
                displayName: authenticatedUser.displayName,
                avatarURL: authenticatedUser.avatarURL,
                createdAt: Date(),
                subscriptionStatus: currentSubscriptionStatus // 使用恢复的订阅状态
              )
              await send(.mainTab(.setUserData(fallbackUser)))
            }
          }
        } else {
          print("⚠️ AppCore: 登录成功但没有用户数据，创建默认MainTab")
          state = .mainTab(MainTab.State())
          return .none
        }

      // MainTab Actions - Handle logout from profile
      case .mainTab(.profile(.logoutButtonTapped)):
        logger.info(.app, "User logout initiated from profile")
        // Return to login screen after logout
        state = .login(Login.State())
        return .none
        
      case .mainTab(.profile(.deleteAccountCompleted)):
        logger.info(.app, "Account deletion completed, returning to login")
        // Return to login screen after account deletion
        state = .login(Login.State())
        return .none

      default:
        return .none
      }
    }
    .ifCaseLet(\.launch, action: \.launch) {
      Launch()
    }
    .ifCaseLet(\.login, action: \.login) {
      Login()
    }
    .ifCaseLet(\.mainTab, action: \.mainTab) {
      MainTab()
    }
  }
}

extension AppFeature.State: Equatable {}
