import SwiftUI
import ComposableArchitecture
import CommonUI
import ImageGenerationCore
import ImageTypeSelectionCore

public struct ImageGenerationView: View {
  let store: StoreOf<ImageGeneration>
  
  public init(store: StoreOf<ImageGeneration>) {
    self.store = store
  }
  
  public var body: some View {
    WithPerceptionTracking {
      ZStack {
        // Background
        LinearGradient(
          colors: [Color.appBackground, Color.secondaryAccent.opacity(0.3)],
          startPoint: .top,
          endPoint: .bottom
        )
        .ignoresSafeArea()
        
        VStack(spacing: 30) {
          // Header
          headerSection
          
          Spacer()
          
          // Main generation content
          if store.generationStatus == .failed {
            errorSection
          } else if store.generationStatus == .completed {
            completedSection
          } else {
            generatingSection
          }
          
          Spacer()
          
          // Bottom actions
          bottomActions
        }
        .padding()
      }
      #if os(iOS)
      .navigationBarHidden(true)
      #endif
      .onAppear {
        store.send(.onAppear)
      }
    }
  }
  
  private var headerSection: some View {
    VStack(spacing: 12) {
      HStack {
        Button(action: { store.send(.backToTypeSelection) }) {
          Image(systemName: "chevron.left")
            .font(.title2)
            .foregroundColor(.primaryAccent)
        }
        
        Spacer()
        
        VStack {
          Text("正在生成您的图片")
            .font(.headline)
            .fontWeight(.semibold)

          Text(store.template.name)
            .font(.subheadline)
            .foregroundColor(.secondary)
        }
        
        Spacer()
        
        // Placeholder for balance
        Color.clear
          .frame(width: 30)
      }
    }
  }
  
  private var generatingSection: some View {
    VStack(spacing: 30) {
      // Animated generation visual
      ZStack {
        // Background circle
        Circle()
          .stroke(Color.gray.opacity(0.2), lineWidth: 8)
          .frame(width: 200, height: 200)
        
        // Progress circle
        Circle()
          .trim(from: 0, to: store.progress)
          .stroke(
            AngularGradient(
              colors: [.accentPink, .accentPurple, .accentBlue, .accentPink],
              center: .center
            ),
            style: StrokeStyle(lineWidth: 8, lineCap: .round)
          )
          .frame(width: 200, height: 200)
          .rotationEffect(.degrees(-90))
          .animation(.easeInOut(duration: 0.5), value: store.progress)
        
        // Center content
        VStack(spacing: 8) {
          Image(systemName: "wand.and.stars")
            .font(.system(size: 40))
            .foregroundStyle(Color.brandGradient)
          
          Text("\(Int(store.progress * 100))%")
            .font(.title)
            .fontWeight(.bold)
            .foregroundColor(.primaryAccent)
        }
      }
      
      // Status and time
      VStack(spacing: 12) {
        Text(store.generationStatus.message)
          .font(.headline)
          .multilineTextAlignment(.center)
        
        if store.estimatedTimeRemaining > 0 {
          HStack(spacing: 6) {
            Image(systemName: "clock")
              .font(.caption)
            Text("预计还需 \(Int(store.estimatedTimeRemaining)) 秒")
              .font(.caption)
          }
          .foregroundColor(.secondary)
        }
      }
    }
  }
  
  private var completedSection: some View {
    VStack(spacing: 20) {
      Image(systemName: "checkmark.circle.fill")
        .font(.system(size: 60))
        .foregroundColor(.green)

      Text("图片生成成功！")
        .font(.title2)
        .fontWeight(.semibold)
        .multilineTextAlignment(.center)

      Text("已为您生成 \(store.generatedImages.count) 张精美图片")
        .font(.body)
        .foregroundColor(.secondary)
        .multilineTextAlignment(.center)

      // Image grid for generated images
      if !store.generatedImages.isEmpty {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 8), count: 2), spacing: 8) {
          ForEach(store.generatedImages, id: \.id) { image in
            GeneratedImageThumbnail(image: image)
          }
        }
        .padding(.horizontal, 20)
      }
    }
  }
  
  private var errorSection: some View {
    VStack(spacing: 20) {
      // 调试信息
      let _ = print("🔍 [UI] Error section - Current error: \(store.error?.localizedDescription ?? "nil")")
      let _ = print("🔍 [UI] Error section - Error type: \(String(describing: store.error))")
      let _ = print("🔍 [UI] Error section - Is monthly limit: \(store.error == .monthlyLimitReached)")

      // 根据错误类型显示不同的图标和颜色
      if store.error == .monthlyLimitReached {
        // 月度限制专用UI
        Image(systemName: "creditcard.fill")
          .font(.system(size: 80))
          .foregroundColor(.accentPink)
      } else {
        // 通用错误UI
        Image(systemName: "exclamationmark.triangle.fill")
          .font(.system(size: 80))
          .foregroundColor(.orange)
      }

      // 根据错误类型显示不同的标题
      if store.error == .monthlyLimitReached {
        Text("需要充值")
          .font(.title2)
          .fontWeight(.semibold)
      } else {
        Text("生成失败")
          .font(.title2)
          .fontWeight(.semibold)
      }

      // 显示错误信息
      if let error = store.error {
        Text(error.localizedDescription)
          .font(.body)
          .foregroundColor(.secondary)
          .multilineTextAlignment(.center)
          .padding(.horizontal, 20)
      }

      // 根据错误类型显示不同的按钮
      VStack(spacing: 12) {
        if store.error == .monthlyLimitReached {
          // 月度限制专用按钮
          PrimaryButton(
            title: "💎 立即充值",
            action: {
              // TODO: 跳转到充值页面
              print("跳转到充值页面")
            }
          )

          PrimaryButton(
            title: "🎯 升级会员",
            style: .outline,
            action: {
              // TODO: 跳转到会员页面
              print("跳转到会员页面")
            }
          )
        } else {
          // 通用错误按钮
          if store.error?.isRetryable == true {
            PrimaryButton(
              title: "重试",
              action: { store.send(.retryGeneration) }
            )
          }
        }

        PrimaryButton(
          title: "选择其他风格",
          style: .outline,
          action: { store.send(.backToTypeSelection) }
        )
      }
      .padding(.horizontal, 40)
    }
  }
  
  private var bottomActions: some View {
    VStack(spacing: 12) {
      if store.generationStatus == .completed {
        // Completed state - automatically navigate or show view button
        Text("正在跳转到您的图片...")
          .font(.caption)
          .foregroundColor(.secondary)
      } else if store.generationStatus != .failed {
        // Generating state - show cancel option
        Button("取消") {
          store.send(.cancelGeneration)
          store.send(.backToTypeSelection)
        }
        .font(.body)
        .foregroundColor(.secondary)
      }
    }
  }
}

// MARK: - Generated Image Thumbnail Component

private struct GeneratedImageThumbnail: View {
  let image: GeneratedImage
  @State private var downloadedImage: Image?
  @State private var isLoading = false


  var body: some View {
    VStack(spacing: 8) {
      // Image display
      RoundedRectangle(cornerRadius: 12)
        .fill(Color.gray.opacity(0.1))
        .aspectRatio(1, contentMode: .fit)
        .overlay {
          if let downloadedImage = downloadedImage {
            downloadedImage
              .resizable()
              .aspectRatio(contentMode: .fill)
              .clipped()
          } else if isLoading {
            VStack(spacing: 8) {
              ProgressView()
                .scaleEffect(0.8)
              Text("加载中...")
                .font(.caption2)
                .foregroundColor(.secondary)
            }
          } else {
            VStack(spacing: 4) {
              Image(systemName: "photo")
                .font(.title2)
                .foregroundColor(.gray)
              Text("等待加载")
                .font(.caption2)
                .foregroundColor(.secondary)
            }
          }
        }
        .overlay(
          RoundedRectangle(cornerRadius: 12)
            .stroke(Color.accentPink.opacity(0.3), lineWidth: 1)
        )


      // Download button
      if downloadedImage != nil, let urlString = image.imageUrl, let url = URL(string: urlString) {
        ShareLink(item: url) {
          HStack(spacing: 4) {
            Image(systemName: "square.and.arrow.up")
              .font(.caption)
            Text("分享图片")
              .font(.caption)
          }
          .foregroundColor(.accentPink)
          .padding(.horizontal, 8)
          .padding(.vertical, 4)
          .background(Color.accentPink.opacity(0.1))
          .cornerRadius(8)
        }
      }

      Text("图片 \(image.id.suffix(1))")
        .font(.caption2)
        .foregroundColor(.secondary)
    }
    .onAppear {
      loadImageFromURL()
    }
  }

  private func loadImageFromURL() {
    guard let urlString = image.imageUrl,
          let url = URL(string: urlString),
          downloadedImage == nil else { return }

    isLoading = true

    Task {
      do {
        let (data, _) = try await URLSession.shared.data(from: url)
        await MainActor.run {
          #if canImport(UIKit)
          if let uiImage = UIKit.UIImage(data: data) {
            self.downloadedImage = Image(uiImage: uiImage)
            print("✅ [ImageGeneration] Image loaded successfully from: \(urlString)")
          } else {
            print("❌ [ImageGeneration] Failed to create UIImage from data")
          }
          #else
          // For other platforms, just mark as loaded
          self.downloadedImage = Image(systemName: "photo.fill")
          print("✅ [ImageGeneration] Image placeholder loaded for: \(urlString)")
          #endif
          self.isLoading = false
        }
      } catch {
        await MainActor.run {
          print("❌ [ImageGeneration] Failed to load image: \(error)")
          self.isLoading = false
        }
      }
    }
  }


}

#Preview {
  NavigationView {
    ImageGenerationView(
      store: Store(
        initialState: ImageGeneration.State(
          template: ImageTemplate(
            id: "test",
            name: "Romantic Wedding",
            description: "Beautiful romantic style",
            category: .wedding,
            prompt: "Dreamy romantic wedding style, soft pastel colors, fresh flowers, ethereal lighting, flowing fabric, gentle breeze, fairy-tale atmosphere, delicate petals, romantic garden setting, soft focus, magical ambiance, tender expression, graceful pose, enchanting beauty, cinematic quality, artistic composition"
          )
        ),
        reducer: {
          ImageGeneration()
        }
      )
    )
  }
}