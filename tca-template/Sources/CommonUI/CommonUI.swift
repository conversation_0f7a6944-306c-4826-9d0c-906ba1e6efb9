import ComposableArchitecture
import Swift<PERSON>
#if canImport(UIKit)
import UIKit
#endif

// MARK: - Common UI Components

/// A reusable button component with enhanced styling and animations
public struct PrimaryButton: View {
  let title: String
  let action: () -> Void
  let isLoading: Bool
  let isDisabled: Bool
  let style: ButtonStyle

  @State private var isPressed = false

  public enum ButtonStyle {
    case primary
    case secondary
    case outline

    var backgroundColor: Color {
      switch self {
      case .primary: return .clear
      case .secondary: return .surfaceBackground
      case .outline: return .clear
      }
    }

    var foregroundColor: Color {
      switch self {
      case .primary: return .white
      case .secondary: return .primaryAccent
      case .outline: return .primaryAccent
      }
    }

    var borderColor: Color {
      switch self {
      case .primary: return .clear
      case .secondary: return .clear
      case .outline: return .primaryAccent
      }
    }
  }

  public init(
    title: String,
    style: ButtonStyle = .primary,
    isLoading: Bool = false,
    isDisabled: Bool = false,
    action: @escaping () -> Void
  ) {
    self.title = title
    self.style = style
    self.isLoading = isLoading
    self.isDisabled = isDisabled
    self.action = action
  }

  public var body: some View {
    Button(action: {
      // Haptic feedback
      #if canImport(UIKit)
      let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
      impactFeedback.impactOccurred()
      #endif
      action()
    }) {
      HStack(spacing: 8) {
        if isLoading {
          ProgressView()
            .progressViewStyle(CircularProgressViewStyle(tint: style.foregroundColor))
            .scaleEffect(0.8)
        }

        Text(title)
          .font(.headline)
          .fontWeight(.semibold)
      }
      .foregroundColor(isDisabled ? .secondary : style.foregroundColor)
      .frame(maxWidth: .infinity)
      .padding(.vertical, AppConstants.UI.buttonPadding)
      .background(
        Group {
          if style == .primary {
            Color.brandGradient
          } else {
            style.backgroundColor
          }
        }
      )
      .overlay(
        RoundedRectangle(cornerRadius: AppConstants.UI.buttonCornerRadius)
          .stroke(style.borderColor, lineWidth: style == .outline ? AppConstants.UI.outlineWidth : 0)
      )
      .cornerRadius(AppConstants.UI.buttonCornerRadius)
      .scaleEffect(isPressed ? AppConstants.UI.pressedButtonScale : 1.0)
      .opacity(isDisabled ? 0.6 : 1.0)
      .shadow(
        color: style == .primary ? .pink.opacity(0.3) : .clear,
        radius: isPressed ? AppConstants.UI.pressedShadowRadius : AppConstants.UI.buttonShadowRadius,
        x: 0,
        y: isPressed ? 2 : 5
      )
    }
    .disabled(isDisabled || isLoading)
    .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
      withAnimation(.easeInOut(duration: AppConstants.UI.shortAnimationDuration)) {
        isPressed = pressing
      }
    }, perform: {})
    .animation(.easeInOut(duration: AppConstants.UI.standardAnimationDuration), value: isPressed)
  }
}

/// A reusable text field component with consistent styling
public struct StyledTextField: View {
  let title: String
  @Binding var text: String
  let placeholder: String
  let isSecure: Bool
  
  public init(
    title: String,
    text: Binding<String>,
    placeholder: String = "",
    isSecure: Bool = false
  ) {
    self.title = title
    self._text = text
    self.placeholder = placeholder
    self.isSecure = isSecure
  }
  
  public var body: some View {
    VStack(alignment: .leading, spacing: 8) {
      Text(title)
        .font(.headline)
        .foregroundColor(.primary)
      
      Group {
        if isSecure {
          SecureField(placeholder, text: $text)
        } else {
          TextField(placeholder, text: $text)
        }
      }
      .textFieldStyle(RoundedBorderTextFieldStyle())
      #if os(iOS)
      .textInputAutocapitalization(.never)
      .autocorrectionDisabled(true)
      #endif
    }
  }
}

/// A reusable card component for content sections
public struct ContentCard<Content: View>: View {
  let content: Content
  
  public init(@ViewBuilder content: () -> Content) {
    self.content = content()
  }
  
  public var body: some View {
    VStack {
      content
    }
    .padding()
    .background(
      RoundedRectangle(cornerRadius: AppConstants.UI.buttonCornerRadius)
        .fill(systemBackgroundColor)
        .shadow(color: .black.opacity(0.1), radius: 5, x: 0, y: 2)
    )
  }
}

/// A reusable loading view
public struct LoadingView: View {
  let message: String
  
  public init(message: String = "Loading...") {
    self.message = message
  }
  
  public var body: some View {
    VStack(spacing: 16) {
      ProgressView()
        .progressViewStyle(CircularProgressViewStyle())
        .scaleEffect(1.5)

      Text(message)
        .font(.body)
        .foregroundColor(.secondary)
    }
    .frame(maxWidth: .infinity, maxHeight: .infinity)
    .background(systemBackgroundColor)
  }
}

/// A reusable error view
public struct ErrorView: View {
  let error: Error
  let retryAction: (() -> Void)?
  
  public init(error: Error, retryAction: (() -> Void)? = nil) {
    self.error = error
    self.retryAction = retryAction
  }
  
  public var body: some View {
    VStack(spacing: 16) {
      Image(systemName: "exclamationmark.triangle")
        .font(.system(size: 48))
        .foregroundColor(.red)
      
      Text("Something went wrong")
        .font(.headline)
      
      Text(error.localizedDescription)
        .font(.body)
        .foregroundColor(.secondary)
        .multilineTextAlignment(.center)
      
      if let retryAction = retryAction {
        Button("Try Again", action: retryAction)
          .buttonStyle(.borderedProminent)
      }
    }
    .padding()
    .frame(maxWidth: .infinity, maxHeight: .infinity)
    .background(systemBackgroundColor)
  }
}

/// A reusable empty state view
public struct EmptyStateView: View {
  let title: String
  let message: String
  let systemImage: String
  let actionTitle: String?
  let action: (() -> Void)?
  
  public init(
    title: String,
    message: String,
    systemImage: String = "tray",
    actionTitle: String? = nil,
    action: (() -> Void)? = nil
  ) {
    self.title = title
    self.message = message
    self.systemImage = systemImage
    self.actionTitle = actionTitle
    self.action = action
  }
  
  public var body: some View {
    VStack(spacing: 16) {
      Image(systemName: systemImage)
        .font(.system(size: 48))
        .foregroundColor(.secondary)
      
      Text(title)
        .font(.headline)
      
      Text(message)
        .font(.body)
        .foregroundColor(.secondary)
        .multilineTextAlignment(.center)
      
      if let actionTitle = actionTitle, let action = action {
        Button(actionTitle, action: action)
          .buttonStyle(.borderedProminent)
      }
    }
    .padding()
    .frame(maxWidth: .infinity, maxHeight: .infinity)
    .background(systemBackgroundColor)
  }
}





// MARK: - View Extensions

public extension View {
  /// Applies a consistent navigation style
  func navigationStyle() -> some View {
    #if os(iOS)
    return self
      .navigationBarTitleDisplayMode(.large)
    #else
    return self
    #endif
  }

  /// Applies a consistent form style
  func formStyle() -> some View {
    self
      .background(systemGroupedBackgroundColor)
  }

  /// Applies modern background inspired by the reference design
  func modernBackground() -> some View {
    self
      .background(
        LinearGradient(
          colors: [Color.appBackground, Color.secondaryAccent.opacity(0.3)],
          startPoint: .topLeading,
          endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
      )
  }
}

// MARK: - Color Extensions

public extension Color {
  // Modern Brand Colors - inspired by the reference design
  static let primaryAccent = Color(red: 0.15, green: 0.2, blue: 0.3) // Dark navy from reference
  static let secondaryAccent = Color(red: 0.8, green: 0.9, blue: 0.7) // Soft mint green
  static let accentPink = Color(red: 1.0, green: 0.4, blue: 0.7) // Vibrant pink
  static let accentPurple = Color(red: 0.6, green: 0.4, blue: 0.9) // Modern purple
  static let accentBlue = Color(red: 0.3, green: 0.7, blue: 1.0) // Sky blue
  static let accentOrange = Color(red: 1.0, green: 0.6, blue: 0.3) // Warm orange
  static let successColor = Color.green
  static let warningColor = Color.orange
  static let errorColor = Color.red

  // Background Colors - inspired by the reference image
  static let appBackground = Color(red: 0.91, green: 0.96, blue: 0.85) // Light green background like reference
  static let darkCardBackground = Color(red: 0.12, green: 0.15, blue: 0.25) // Dark navy card like reference

  // Additional colors from reference design
  static let lightGreen = Color(red: 0.7, green: 0.9, blue: 0.6) // Light green tag
  static let lightPurple = Color(red: 0.8, green: 0.7, blue: 0.9) // Light purple tag
  static let lightBlue = Color(red: 0.7, green: 0.85, blue: 1.0) // Light blue tag
  static let lightYellow = Color(red: 1.0, green: 0.9, blue: 0.6) // Light yellow tag

  // Gradient Colors - updated with new brand colors
  static let brandGradient = LinearGradient(
    colors: [.accentPink, .accentPurple],
    startPoint: .leading,
    endPoint: .trailing
  )

  static let softGradient = LinearGradient(
    colors: [.accentPink.opacity(0.3), .accentPurple.opacity(0.3)],
    startPoint: .topLeading,
    endPoint: .bottomTrailing
  )

  // Semantic Colors
  #if canImport(UIKit)
  static let cardBackground = Color(UIColor.systemBackground)
  static let surfaceBackground = Color(UIColor.secondarySystemBackground)
  static let borderColor = Color(UIColor.separator)
  #else
  static let cardBackground = Color.white
  static let surfaceBackground = Color.gray.opacity(0.1)
  static let borderColor = Color.gray.opacity(0.3)
  #endif
}

// MARK: - Enhanced Card Component

/// A reusable card component with enhanced styling and animations
public struct EnhancedCardView<Content: View>: View {
  let content: Content
  let style: CardStyle
  let isInteractive: Bool

  @State private var isPressed = false

  public enum CardStyle {
    case elevated
    case flat
    case outlined
    case gradient

    var backgroundColor: Color {
      switch self {
      case .elevated, .flat: return .cardBackground
      case .outlined: return .cardBackground
      case .gradient: return .clear
      }
    }

    var shadowRadius: CGFloat {
      switch self {
      case .elevated: return 8
      case .flat: return 0
      case .outlined: return 2
      case .gradient: return 12
      }
    }

    var shadowOpacity: Double {
      switch self {
      case .elevated: return 0.15
      case .flat: return 0
      case .outlined: return 0.05
      case .gradient: return 0.3
      }
    }
  }

  public init(
    style: CardStyle = .elevated,
    isInteractive: Bool = false,
    @ViewBuilder content: () -> Content
  ) {
    self.style = style
    self.isInteractive = isInteractive
    self.content = content()
  }

  public var body: some View {
    content
      .padding()
      .background(
        Group {
          if style == .gradient {
            Color.softGradient
          } else {
            style.backgroundColor
          }
        }
      )
      .overlay(
        RoundedRectangle(cornerRadius: AppConstants.UI.cardCornerRadius)
          .stroke(style == .outlined ? Color.borderColor : Color.clear, lineWidth: AppConstants.UI.borderWidth)
      )
      .cornerRadius(AppConstants.UI.cardCornerRadius)
      .scaleEffect(isPressed && isInteractive ? AppConstants.UI.pressedCardScale : 1.0)
      .shadow(
        color: .black.opacity(style.shadowOpacity),
        radius: isPressed && isInteractive ? style.shadowRadius * 0.7 : style.shadowRadius,
        x: 0,
        y: isPressed && isInteractive ? 2 : 4
      )
      .animation(.easeInOut(duration: AppConstants.UI.standardAnimationDuration), value: isPressed)
      .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
        if isInteractive {
          withAnimation(.easeInOut(duration: AppConstants.UI.shortAnimationDuration)) {
            isPressed = pressing
          }
        }
      }, perform: {})
  }
}

// MARK: - Enhanced Loading Indicator

/// A beautiful loading indicator with gradient animation
public struct EnhancedLoadingView: View {
  let message: String
  let progress: Double?

  @State private var rotationAngle: Double = 0
  @State private var scale: CGFloat = 1.0

  public init(message: String = "加载中...", progress: Double? = nil) {
    self.message = message
    self.progress = progress
  }

  public var body: some View {
    VStack(spacing: 20) {
      ZStack {
        // Background circle
        Circle()
          .stroke(Color.gray.opacity(0.2), lineWidth: 4)
          .frame(width: 60, height: 60)

        // Progress circle
        Circle()
          .trim(from: 0, to: progress ?? 1.0)
          .stroke(
            AngularGradient(
              colors: [.pink, .purple, .pink],
              center: .center,
              startAngle: .degrees(0),
              endAngle: .degrees(360)
            ),
            style: StrokeStyle(lineWidth: 4, lineCap: .round)
          )
          .frame(width: 60, height: 60)
          .rotationEffect(.degrees(rotationAngle))
          .animation(.linear(duration: 2).repeatForever(autoreverses: false), value: rotationAngle)

        // Center icon
        Image(systemName: "wand.and.stars")
          .font(.title2)
          .foregroundStyle(Color.brandGradient)
          .scaleEffect(scale)
          .animation(.easeInOut(duration: 1).repeatForever(autoreverses: true), value: scale)
      }

      VStack(spacing: 8) {
        Text(message)
          .font(.headline)
          .foregroundColor(.primary)

        if let progress = progress {
          Text("\(Int(progress * 100))%")
            .font(.caption)
            .foregroundColor(.secondary)
        }
      }
    }
    .onAppear {
      rotationAngle = 360
      scale = 1.2
    }
  }
}

// MARK: - System Colors

private var systemBackgroundColor: Color {
  #if canImport(UIKit)
  return Color(UIColor.systemBackground)
  #else
  return Color(.windowBackgroundColor)
  #endif
}

private var systemGroupedBackgroundColor: Color {
  #if canImport(UIKit)
  return Color(UIColor.systemGroupedBackground)
  #else
  return Color(.controlBackgroundColor)
  #endif
}

// MARK: - Font Extensions

public extension Font {
  // Modern typography inspired by reference design
  static let customLargeTitle = Font.system(size: 34, weight: .bold, design: .rounded)
  static let customTitle = Font.system(size: 28, weight: .bold, design: .rounded)
  static let customHeadline = Font.system(size: 20, weight: .semibold, design: .rounded)
  static let customSubheadline = Font.system(size: 16, weight: .medium, design: .rounded)
  static let customBody = Font.system(size: 16, weight: .regular, design: .rounded)
  static let customCaption = Font.system(size: 12, weight: .medium, design: .rounded)

  // Additional modern font styles
  static let modernTitle = Font.system(size: 32, weight: .heavy, design: .rounded)
  static let modernSubtitle = Font.system(size: 18, weight: .medium, design: .rounded)
  static let modernButton = Font.system(size: 16, weight: .semibold, design: .rounded)
}

// MARK: - Modern Card Component (inspired by reference design)

/// A modern dark card component similar to the reference image
public struct ModernDarkCard<Content: View>: View {
  let content: Content
  let cornerRadius: CGFloat

  public init(cornerRadius: CGFloat = 24, @ViewBuilder content: () -> Content) {
    self.cornerRadius = cornerRadius
    self.content = content()
  }

  public var body: some View {
    content
      .padding(20)
      .background(
        RoundedRectangle(cornerRadius: cornerRadius)
          .fill(Color.darkCardBackground)
          .shadow(color: .black.opacity(0.15), radius: 12, x: 0, y: 4)
      )
  }
}

// MARK: - Colorful Tag Component (inspired by reference design)

/// A colorful tag component similar to the reference image
public struct ColorfulTag: View {
  let text: String
  let backgroundColor: Color
  let textColor: Color

  public init(text: String, backgroundColor: Color, textColor: Color = .black) {
    self.text = text
    self.backgroundColor = backgroundColor
    self.textColor = textColor
  }

  public var body: some View {
    Text(text)
      .font(.customCaption)
      .fontWeight(.semibold)
      .foregroundColor(textColor)
      .padding(.horizontal, 12)
      .padding(.vertical, 6)
      .background(
        RoundedRectangle(cornerRadius: 12)
          .fill(backgroundColor)
      )
  }
}
