import Foundation

// MARK: - App Constants

public enum AppConstants {
  
  // MARK: - Authentication
  public enum Authentication {
    public static let mockTokenPrefix = "mock-email-token"
    public static let mock2FAToken = "mock-2fa-token"
    public static let mockAppleToken = "mock-apple-access-token"
    public static let mockAppleUserID = "mock-apple-user-456"
    public static let default2FAEmail = "<EMAIL>"
    public static let defaultAppleEmail = "<EMAIL>"
    public static let emailUserIDPrefix = "email_"
    public static let mock2FAUserID = "mock-2fa-user"
    public static let userIDLength = 8
  }
  
  // MARK: - UI Constants
  public enum UI {
    public static let buttonCornerRadius: CGFloat = 12
    public static let cardCornerRadius: CGFloat = 16
    public static let modernCardCornerRadius: CGFloat = 24
    public static let buttonPadding: CGFloat = 16
    public static let cardPadding: CGFloat = 20
    
    // Animation Durations
    public static let shortAnimationDuration: Double = 0.1
    public static let standardAnimationDuration: Double = 0.2
    public static let loadingAnimationDuration: Double = 1.0
    public static let rotationAnimationDuration: Double = 2.0
    
    // Scale Effects
    public static let pressedButtonScale: CGFloat = 0.95
    public static let pressedCardScale: CGFloat = 0.98
    public static let loadingIconScale: CGFloat = 1.2
    public static let progressViewScale: CGFloat = 0.8
    
    // Shadow Properties
    public static let elevatedShadowRadius: CGFloat = 8
    public static let gradientShadowRadius: CGFloat = 12
    public static let buttonShadowRadius: CGFloat = 10
    public static let pressedShadowRadius: CGFloat = 5
    
    // Line Widths
    public static let outlineWidth: CGFloat = 2
    public static let strokeWidth: CGFloat = 4
    public static let borderWidth: CGFloat = 1
  }
  
  // MARK: - User Defaults Keys
  public enum UserDefaults {
    public static let userAuthToken = "user_auth_token"
    public static let userEmail = "user_email"
    public static let userDisplayName = "user_display_name"
    public static let guestUsageCount = "guest_usage_count"
    public static let hasCompletedOnboarding = "has_completed_onboarding"
  }
  
  // MARK: - Network
  public enum Network {
    public static let requestTimeoutInterval: TimeInterval = 30.0
    public static let mockDelayShort: TimeInterval = 0.5
    public static let mockDelayMedium: TimeInterval = 1.0
    public static let mockDelayLong: TimeInterval = 2.0
  }
  
  // MARK: - Guest Usage Limits
  public enum GuestLimits {
    public static let maxImageGenerations = 3
    public static let maxStyleSelections = 5
    public static let maxPhotoUploads = 10
  }
  
  // MARK: - File Management
  public enum Files {
    public static let maxImageSizeMB = 10
    public static let supportedImageFormats = ["jpg", "jpeg", "png", "heic"]
    public static let maxImagesPerGeneration = 5
  }
  
  // MARK: - Validation
  public enum Validation {
    public static let minPasswordLength = 8
    public static let maxPasswordLength = 128
    public static let minEmailLength = 5
    public static let maxEmailLength = 254
    public static let maxDisplayNameLength = 50
  }
  
  // MARK: - App Info
  public enum App {
    public static let name = "Bridal AI"
    public static let bundleIdentifier = "com.bridal.app"
    public static let version = "1.0.0"
    public static let buildNumber = "1"
  }
  
  // MARK: - Localization Keys
  public enum LocalizationKeys {
    // Tab Titles
    public static let homeTab = "home_tab"
    public static let createTab = "create_tab"
    public static let galleryTab = "gallery_tab"
    public static let profileTab = "profile_tab"
    
    // Button Titles
    public static let getStarted = "get_started"
    public static let skipLogin = "skip_login"
    public static let login = "login"
    public static let logout = "logout"
    public static let tryAgain = "try_again"
    public static let cancel = "cancel"
    public static let confirm = "confirm"
    public static let save = "save"
    public static let delete = "delete"
    
    // Creation Flow
    public static let selectPhotos = "select_photos"
    public static let chooseStyle = "choose_style"
    public static let aiGeneration = "ai_generation"
    
    // Messages
    public static let loginSuccessful = "login_successful"
    public static let logoutCompleted = "logout_completed"
    public static let accountDeleted = "account_deleted"
    public static let somethingWentWrong = "something_went_wrong"
    public static let loading = "loading"
    public static let emptyState = "empty_state"
  }
}

// MARK: - Error Domain Constants

public enum ErrorDomains {
  public static let authentication = "com.bridal.app.authentication"
  public static let network = "com.bridal.app.network"
  public static let validation = "com.bridal.app.validation"
  public static let fileManagement = "com.bridal.app.filemanagement"
  public static let userState = "com.bridal.app.userstate"
}

// MARK: - Notification Names

public extension Notification.Name {
  static let userDidLogin = Notification.Name("userDidLogin")
  static let userDidLogout = Notification.Name("userDidLogout")
  static let authenticationFailed = Notification.Name("authenticationFailed")
  static let imageGenerationCompleted = Notification.Name("imageGenerationCompleted")
  static let subscriptionStatusChanged = Notification.Name("subscriptionStatusChanged")
}

// MARK: - API Endpoints (for future real implementation)

public enum APIEndpoints {
  // 基础配置
  public static let baseURL = "http://localhost:8000"  // 开发环境
  // public static let baseURL = "https://api.bridal.app"  // 生产环境

  // 认证相关
  public static let login = "/auth/login"
  public static let logout = "/auth/logout"
  public static let register = "/auth/register"
  public static let twoFactor = "/auth/2fa"
  public static let appleSignIn = "/auth/apple"

  // Apple OAuth 专用端点
  public static let appleOAuth = "/api/v1/oauth/apple/login"

  // AI 和图片相关
  public static let generateImage = "/ai/generate"
  public static let uploadPhoto = "/upload/photo"

  // 用户相关
  public static let getUserProfile = "/user/profile"
  public static let updateProfile = "/user/profile"
  public static let deleteAccount = "/user/delete"

  // 完整的 Apple OAuth URL
  public static var appleOAuthURL: String {
    return baseURL + appleOAuth
  }
}