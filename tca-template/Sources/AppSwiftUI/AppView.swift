import SwiftUI
import Composable<PERSON>rchitecture
import AppCore
import LaunchSwiftUI
import LoginSwiftUI
import MainTabSwiftUI

public struct AppView: View {
  let store: StoreOf<AppFeature>
  
  public init(store: StoreOf<AppFeature>) {
    self.store = store
  }
  
  public var body: some View {
    WithPerceptionTracking {
      Group {
        switch store.case {
        case .launch(let launchStore):
          LaunchView(store: launchStore)
            .transition(.opacity)
          
        case .login(let loginStore):
          NavigationView {
            LoginView(store: loginStore)
          }
          .transition(.move(edge: .trailing))
          
        case .mainTab(let mainTabStore):
          MainTabView(store: mainTabStore)
            .transition(.move(edge: .bottom))
        }
      }
    }
  }
}