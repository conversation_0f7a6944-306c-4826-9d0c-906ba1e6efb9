import ComposableArchitecture
import Foundation
import SwiftUI
#if canImport(UIKit)
import UIKit
#endif

// MARK: - AI Generation Feature

@Reducer
public struct AIGeneration: Sendable {

  // MARK: - State

  @ObservableState
  public struct State: Equatable, Sendable {
    public var generationRequest: GenerationRequest?
    public var generationStatus: GenerationStatus = .idle
    public var progress: Double = 0.0
    public var estimatedTimeRemaining: TimeInterval = 0
    public var generatedImages: [GeneratedImage] = []
    public var error: AIGenerationError?
    public var isRetrying = false

    public init(
      generationRequest: GenerationRequest? = nil,
      generationStatus: GenerationStatus = .idle,
      progress: Double = 0.0,
      estimatedTimeRemaining: TimeInterval = 0,
      generatedImages: [GeneratedImage] = [],
      error: AIGenerationError? = nil,
      isRetrying: Bool = false
    ) {
      self.generationRequest = generationRequest
      self.generationStatus = generationStatus
      self.progress = progress
      self.estimatedTimeRemaining = estimatedTimeRemaining
      self.generatedImages = generatedImages
      self.error = error
      self.isRetrying = isRetrying
    }
    
    public var isGenerating: Bool {
      switch generationStatus {
      case .preparing, .processing, .finalizing:
        return true
      default:
        return false
      }
    }
    
    public var canRetry: Bool {
      switch generationStatus {
      case .failed:
        return true
      default:
        return false
      }
    }
    
    public var hasResults: Bool {
      !generatedImages.isEmpty
    }
  }
  
  // MARK: - Action
  
  public enum Action: BindableAction, Sendable {
    case binding(BindingAction<State>)
    case startGeneration(GenerationRequest)
    case cancelGeneration
    case retryGeneration
    case progressUpdated(Double, TimeInterval)
    case statusChanged(GenerationStatus)
    case generationCompleted([GeneratedImage])
    case generationFailed(AIGenerationError)
    case selectImage(GeneratedImage)
    case saveImage(GeneratedImage)
    case shareImage(GeneratedImage)
    case proceedToGallery
    case dismissError
  }
  
  // MARK: - Dependencies
  
  @Dependency(\.aiGenerationClient) var aiGenerationClient

  // MARK: - Initializer

  public init() {}

  // MARK: - Reducer Body

  public var body: some Reducer<State, Action> {
    BindingReducer()
    
    Reduce { state, action in
      switch action {
      case .binding:
        return .none
        
      case let .startGeneration(request):
        state.generationRequest = request
        state.generationStatus = .preparing
        state.progress = 0.0
        state.error = nil
        state.generatedImages = []
        
        return .run { send in
          do {
            // Start generation process
            await send(.statusChanged(.preparing))
            
            let generationId = try await aiGenerationClient.startGeneration(request)
            
            // Monitor progress
            for await update in aiGenerationClient.monitorProgress(generationId) {
              await send(.progressUpdated(update.progress, update.estimatedTimeRemaining))
              await send(.statusChanged(update.status))
              
              if update.status == .completed {
                let images = try await aiGenerationClient.getResults(generationId)
                await send(.generationCompleted(images))
                break
              } else if update.status == .failed {
                throw AIGenerationError.generationFailed("Generation process failed")
              }
            }
          } catch {
            await send(.generationFailed(.networkError(error.localizedDescription)))
          }
        }
        
      case .cancelGeneration:
        state.generationStatus = .cancelled
        return .run { _ in
          // Cancel the generation request
          await aiGenerationClient.cancelGeneration()
        }
        
      case .retryGeneration:
        guard let request = state.generationRequest else {
          return .none
        }
        state.isRetrying = true
        return .send(.startGeneration(request))
        
      case let .progressUpdated(progress, timeRemaining):
        state.progress = progress
        state.estimatedTimeRemaining = timeRemaining
        return .none
        
      case let .statusChanged(status):
        state.generationStatus = status
        state.isRetrying = false
        return .none
        
      case let .generationCompleted(images):
        state.generatedImages = images
        state.generationStatus = .completed
        state.progress = 1.0
        return .none
        
      case let .generationFailed(error):
        state.error = error
        state.generationStatus = .failed
        state.isRetrying = false
        return .none
        
      case .selectImage:
        // Handle image selection for further processing
        return .none
        
      case let .saveImage(image):
        return .run { send in
          do {
            try await aiGenerationClient.saveToPhotoLibrary(image)
            // Show success feedback
          } catch {
            await send(.generationFailed(.saveFailed(error.localizedDescription)))
          }
        }
        
      case .shareImage:
        // This will be handled by the view
        return .none
        
      case .proceedToGallery:
        // This will be handled by parent reducer
        return .none
        
      case .dismissError:
        state.error = nil
        return .none
      }
    }
  }
}

// MARK: - Models

public struct GenerationRequest: Equatable, Sendable {
  public let photos: [PhotoData]
  public let style: StyleData
  public let customPrompt: String?
  public let quality: ImageQuality
  public let aspectRatio: AspectRatio
  
  public init(
    photos: [PhotoData],
    style: StyleData,
    customPrompt: String? = nil,
    quality: ImageQuality = .standard,
    aspectRatio: AspectRatio = .portrait
  ) {
    self.photos = photos
    self.style = style
    self.customPrompt = customPrompt
    self.quality = quality
    self.aspectRatio = aspectRatio
  }
}

public struct PhotoData: Equatable, Sendable {
  public let id: UUID
  public let imageData: Data
  public let originalSize: CGSize
  
  public init(id: UUID = UUID(), imageData: Data, originalSize: CGSize) {
    self.id = id
    self.imageData = imageData
    self.originalSize = originalSize
  }
}

public struct StyleData: Equatable, Sendable {
  public let id: UUID
  public let name: String
  public let prompt: String
  public let isPremium: Bool
  
  public init(id: UUID, name: String, prompt: String, isPremium: Bool = false) {
    self.id = id
    self.name = name
    self.prompt = prompt
    self.isPremium = isPremium
  }
}

public struct GeneratedImage: Identifiable, Equatable, Sendable {
  public let id = UUID()
  public let imageData: Data
  public let thumbnailData: Data
  public let size: CGSize
  public let createdAt: Date
  public let style: StyleData
  public let quality: ImageQuality
  
  public init(
    imageData: Data,
    thumbnailData: Data,
    size: CGSize,
    createdAt: Date = Date(),
    style: StyleData,
    quality: ImageQuality
  ) {
    self.imageData = imageData
    self.thumbnailData = thumbnailData
    self.size = size
    self.createdAt = createdAt
    self.style = style
    self.quality = quality
  }
  
  public static func == (lhs: GeneratedImage, rhs: GeneratedImage) -> Bool {
    lhs.id == rhs.id
  }
}

public enum GenerationStatus: String, Equatable, Sendable {
  case idle = "idle"
  case preparing = "preparing"
  case processing = "processing"
  case finalizing = "finalizing"
  case completed = "completed"
  case failed = "failed"
  case cancelled = "cancelled"
  
  public var displayName: String {
    switch self {
    case .idle:
      return "待开始"
    case .preparing:
      return "准备中"
    case .processing:
      return "生成中"
    case .finalizing:
      return "完善中"
    case .completed:
      return "已完成"
    case .failed:
      return "生成失败"
    case .cancelled:
      return "已取消"
    }
  }
  
  public var systemImage: String {
    switch self {
    case .idle:
      return "clock"
    case .preparing:
      return "gear"
    case .processing:
      return "wand.and.stars"
    case .finalizing:
      return "sparkles"
    case .completed:
      return "checkmark.circle"
    case .failed:
      return "exclamationmark.triangle"
    case .cancelled:
      return "xmark.circle"
    }
  }
}

public enum ImageQuality: String, CaseIterable, Sendable {
  case standard = "standard"
  case high = "high"
  case ultra = "ultra"
  
  public var displayName: String {
    switch self {
    case .standard:
      return "标准"
    case .high:
      return "高清"
    case .ultra:
      return "超高清"
    }
  }
  
  public var resolution: CGSize {
    switch self {
    case .standard:
      return CGSize(width: 512, height: 768)
    case .high:
      return CGSize(width: 1024, height: 1536)
    case .ultra:
      return CGSize(width: 2048, height: 3072)
    }
  }
}

public enum AspectRatio: String, CaseIterable, Sendable {
  case portrait = "3:4"
  case square = "1:1"
  case landscape = "4:3"
  
  public var displayName: String {
    switch self {
    case .portrait:
      return "竖版"
    case .square:
      return "方形"
    case .landscape:
      return "横版"
    }
  }
  
  public var ratio: CGFloat {
    switch self {
    case .portrait:
      return 3.0 / 4.0
    case .square:
      return 1.0
    case .landscape:
      return 4.0 / 3.0
    }
  }
}

public enum AIGenerationError: Error, Equatable, LocalizedError, Sendable {
  case networkError(String)
  case generationFailed(String)
  case invalidRequest
  case quotaExceeded
  case premiumRequired
  case saveFailed(String)
  
  public var errorDescription: String? {
    switch self {
    case .networkError(let message):
      return "网络错误: \(message)"
    case .generationFailed(let message):
      return "生成失败: \(message)"
    case .invalidRequest:
      return "请求参数无效"
    case .quotaExceeded:
      return "今日生成次数已用完"
    case .premiumRequired:
      return "此功能需要VIP会员"
    case .saveFailed(let message):
      return "保存失败: \(message)"
    }
  }
  
  public var recoverySuggestion: String? {
    switch self {
    case .networkError:
      return "请检查网络连接后重试"
    case .generationFailed:
      return "请重新选择照片和风格后重试"
    case .invalidRequest:
      return "请检查输入参数"
    case .quotaExceeded:
      return "升级VIP获得更多生成次数"
    case .premiumRequired:
      return "升级VIP解锁高级功能"
    case .saveFailed:
      return "请检查相册权限"
    }
  }
}

// MARK: - Progress Update

public struct ProgressUpdate: Sendable {
  public let progress: Double
  public let status: GenerationStatus
  public let estimatedTimeRemaining: TimeInterval
  
  public init(progress: Double, status: GenerationStatus, estimatedTimeRemaining: TimeInterval) {
    self.progress = progress
    self.status = status
    self.estimatedTimeRemaining = estimatedTimeRemaining
  }
}

// MARK: - Dependencies

@DependencyClient
public struct AIGenerationClient: Sendable {
  public var startGeneration: @Sendable (GenerationRequest) async throws -> String = { _ in "" }
  public var monitorProgress: @Sendable (String) -> AsyncStream<ProgressUpdate> = { _ in
    AsyncStream { continuation in continuation.finish() }
  }
  public var getResults: @Sendable (String) async throws -> [GeneratedImage] = { _ in [] }
  public var cancelGeneration: @Sendable () async -> Void = {}
  public var saveToPhotoLibrary: @Sendable (GeneratedImage) async throws -> Void = { _ in }

  // Mock data helper
  public static func createMockGeneratedImages() -> [GeneratedImage] {
    let weddingStyles = [
      ("heart.fill", "浪漫经典", "romantic classic wedding style"),
      ("star.fill", "星空梦幻", "starry dreamy wedding style"),
      ("crown.fill", "皇室优雅", "royal elegant wedding style"),
      ("sparkles", "仙境奇幻", "fairy tale wedding style"),
      ("camera.macro", "复古艺术", "vintage artistic wedding style"),
      ("photo.artframe", "现代简约", "modern minimalist wedding style"),
      ("gift.fill", "甜蜜浪漫", "sweet romantic wedding style"),
      ("balloon.fill", "清新自然", "fresh natural wedding style")
    ]

    return weddingStyles.enumerated().compactMap { (index, styleInfo) in
      let (_, styleName, prompt) = styleInfo
      let mockImageData = Data("mock_image_data_\(index)".utf8)
      let mockThumbnailData = Data("mock_thumbnail_data_\(index)".utf8)

      let styleData = StyleData(
        id: UUID(),
        name: styleName,
        prompt: prompt
      )

      return GeneratedImage(
        imageData: mockImageData,
        thumbnailData: mockThumbnailData,
        size: CGSize(width: 512, height: 768),
        createdAt: Date(),
        style: styleData,
        quality: .high
      )
    }
  }
}

extension AIGenerationClient: DependencyKey {
  public static let liveValue = AIGenerationClient(
    startGeneration: { request in
      // Mock implementation for testing
      print("🤖 AI Generation: Starting generation with \(request.photos.count) photos")
      return UUID().uuidString
    },
    monitorProgress: { generationId in
      // TODO: Implement progress monitoring
      AsyncStream { continuation in
        Task {
          // Mock progress updates
          let updates = [
            ProgressUpdate(progress: 0.1, status: .preparing, estimatedTimeRemaining: 30),
            ProgressUpdate(progress: 0.3, status: .processing, estimatedTimeRemaining: 20),
            ProgressUpdate(progress: 0.7, status: .processing, estimatedTimeRemaining: 10),
            ProgressUpdate(progress: 0.9, status: .finalizing, estimatedTimeRemaining: 5),
            ProgressUpdate(progress: 1.0, status: .completed, estimatedTimeRemaining: 0)
          ]
          
          for update in updates {
            try? await Task.sleep(nanoseconds: 2_000_000_000) // 2 seconds
            continuation.yield(update)
          }
          
          continuation.finish()
        }
      }
    },
    getResults: { generationId in
      // Mock implementation - return generated wedding photos
      print("🤖 AI Generation: Fetching results for \(generationId)")
      return AIGenerationClient.createMockGeneratedImages()
    },
    cancelGeneration: {
      // TODO: Implement cancellation
    },
    saveToPhotoLibrary: { image in
      // TODO: Implement photo library saving
    }
  )
  
  public static let testValue = AIGenerationClient(
    startGeneration: { _ in "test-id" },
    monitorProgress: { _ in
      AsyncStream { continuation in
        continuation.yield(ProgressUpdate(progress: 1.0, status: .completed, estimatedTimeRemaining: 0))
        continuation.finish()
      }
    },
    getResults: { _ in [] },
    cancelGeneration: {},
    saveToPhotoLibrary: { _ in }
  )
}

extension DependencyValues {
  public var aiGenerationClient: AIGenerationClient {
    get { self[AIGenerationClient.self] }
    set { self[AIGenerationClient.self] = newValue }
  }
}
