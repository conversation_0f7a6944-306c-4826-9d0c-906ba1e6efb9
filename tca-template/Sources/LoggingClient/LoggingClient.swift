import Dependencies
import DependenciesMacros
import Foundation
import os.log

public enum LogLevel: String, CaseIterable, Sendable {
  case debug = "DEBUG"
  case info = "INFO"
  case warning = "WARNING"
  case error = "ERROR"
  case critical = "CRITICAL"
  
  public var osLogType: OSLogType {
    switch self {
    case .debug: return .debug
    case .info: return .info
    case .warning: return .default
    case .error: return .error
    case .critical: return .fault
    }
  }
  
  public var emoji: String {
    switch self {
    case .debug: return "🔍"
    case .info: return "ℹ️"
    case .warning: return "⚠️"
    case .error: return "❌"
    case .critical: return "🚨"
    }
  }
}

public enum LogCategory: String, CaseIterable, Sendable {
  case authentication = "Authentication"
  case userState = "UserState"
  case network = "Network"
  case ui = "UI"
  case aiGeneration = "AIGeneration"
  case photoUpload = "PhotoUpload"
  case profile = "Profile"
  case app = "App"
  case general = "General"
  
  public var subsystem: String {
    return "com.bridal.app"
  }
}

@DependencyClient
public struct LoggingClient: Sendable {
  public var log: @Sendable (LogLevel, LogCategory, String, String, StaticString, UInt) -> Void
  public var debug: @Sendable (LogCategory, String, String, StaticString, UInt) -> Void
  public var info: @Sendable (LogCategory, String, String, StaticString, UInt) -> Void
  public var warning: @Sendable (LogCategory, String, String, StaticString, UInt) -> Void
  public var error: @Sendable (LogCategory, String, String, StaticString, UInt) -> Void
  public var critical: @Sendable (LogCategory, String, String, StaticString, UInt) -> Void
}

extension LoggingClient: TestDependencyKey {
  public static let testValue = Self(
    log: { level, category, message, file, function, line in
      print("[\(level.rawValue)] [\(category.rawValue)] \(message)")
    },
    debug: { category, message, file, function, line in
      print("[DEBUG] [\(category.rawValue)] \(message)")
    },
    info: { category, message, file, function, line in
      print("[INFO] [\(category.rawValue)] \(message)")
    },
    warning: { category, message, file, function, line in
      print("[WARNING] [\(category.rawValue)] \(message)")
    },
    error: { category, message, file, function, line in
      print("[ERROR] [\(category.rawValue)] \(message)")
    },
    critical: { category, message, file, function, line in
      print("[CRITICAL] [\(category.rawValue)] \(message)")
    }
  )
}

extension LoggingClient: DependencyKey {
  public static let liveValue = Self(
    log: { level, category, message, file, function, line in
      let logger = Logger(subsystem: category.subsystem, category: category.rawValue)
      let formattedMessage = "\(level.emoji) \(message)"
      logger.log(level: level.osLogType, "\(formattedMessage, privacy: .public)")
    },
    debug: { category, message, file, function, line in
      let logger = Logger(subsystem: category.subsystem, category: category.rawValue)
      logger.debug("🔍 \(message, privacy: .public)")
    },
    info: { category, message, file, function, line in
      let logger = Logger(subsystem: category.subsystem, category: category.rawValue)
      logger.info("ℹ️ \(message, privacy: .public)")
    },
    warning: { category, message, file, function, line in
      let logger = Logger(subsystem: category.subsystem, category: category.rawValue)
      logger.warning("⚠️ \(message, privacy: .public)")
    },
    error: { category, message, file, function, line in
      let logger = Logger(subsystem: category.subsystem, category: category.rawValue)
      logger.error("❌ \(message, privacy: .public)")
    },
    critical: { category, message, file, function, line in
      let logger = Logger(subsystem: category.subsystem, category: category.rawValue)
      logger.critical("🚨 \(message, privacy: .public)")
    }
  )
}

extension DependencyValues {
  public var loggingClient: LoggingClient {
    get { self[LoggingClient.self] }
    set { self[LoggingClient.self] = newValue }
  }
}

// MARK: - Convenience Extensions

public extension LoggingClient {
  func debug(
    _ category: LogCategory = .general,
    _ message: @autoclosure () -> String,
    file: String = #fileID,
    function: StaticString = #function,
    line: UInt = #line
  ) {
    self.debug(category, message(), file, function, line)
  }
  
  func info(
    _ category: LogCategory = .general,
    _ message: @autoclosure () -> String,
    file: String = #fileID,
    function: StaticString = #function,
    line: UInt = #line
  ) {
    self.info(category, message(), file, function, line)
  }
  
  func warning(
    _ category: LogCategory = .general,
    _ message: @autoclosure () -> String,
    file: String = #fileID,
    function: StaticString = #function,
    line: UInt = #line
  ) {
    self.warning(category, message(), file, function, line)
  }
  
  func error(
    _ category: LogCategory = .general,
    _ message: @autoclosure () -> String,
    file: String = #fileID,
    function: StaticString = #function,
    line: UInt = #line
  ) {
    self.error(category, message(), file, function, line)
  }
  
  func critical(
    _ category: LogCategory = .general,
    _ message: @autoclosure () -> String,
    file: String = #fileID,
    function: StaticString = #function,
    line: UInt = #line
  ) {
    self.critical(category, message(), file, function, line)
  }
}