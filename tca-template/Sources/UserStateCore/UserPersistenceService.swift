import Foundation
import Security

/// 用户登录状态持久化服务
public struct UserPersistenceService {
  
  // MARK: - Keys
  private enum Keys {
    static let userInfo = "bridal_app_user_info"
    static let authToken = "bridal_app_auth_token"
    static let loginTimestamp = "bridal_app_login_timestamp"
    static let subscriptionStatus = "bridal_app_subscription_status"
    static let subscriptionTimestamp = "bridal_app_subscription_timestamp"
  }
  
  // MARK: - Public Methods
  
  /// 保存用户登录状态
  public static func saveUserSession(user: User, token: String) {
    print("💾 保存用户登录状态: \(user.displayName)")
    
    // 保存用户信息到UserDefaults
    if let userData = try? JSONEncoder().encode(user) {
      UserDefaults.standard.set(userData, forKey: Keys.userInfo)
      print("✅ 用户信息已保存到UserDefaults")
    }
    
    // 保存认证令牌到Keychain（更安全）
    saveTokenToKeychain(token: token)
    
    // 保存登录时间戳
    UserDefaults.standard.set(Date().timeIntervalSince1970, forKey: Keys.loginTimestamp)
    
    UserDefaults.standard.synchronize()
    print("✅ 用户登录状态持久化完成")
  }
  
  /// 恢复用户登录状态
  public static func restoreUserSession() -> (user: User, token: String)? {
    print("🔄 尝试恢复用户登录状态")

    // 检查登录是否过期（7天有效期）
    let loginTimestamp = UserDefaults.standard.double(forKey: Keys.loginTimestamp)
    let currentTime = Date().timeIntervalSince1970
    let sevenDaysInSeconds: TimeInterval = 7 * 24 * 60 * 60

    if currentTime - loginTimestamp > sevenDaysInSeconds {
      print("⚠️ 登录状态已过期，清除本地数据")
      clearUserSession()
      return nil
    }

    // 恢复用户信息
    guard let userData = UserDefaults.standard.data(forKey: Keys.userInfo),
          let user = try? JSONDecoder().decode(User.self, from: userData) else {
      print("❌ 无法恢复用户信息")
      return nil
    }

    // 恢复认证令牌
    guard let token = getTokenFromKeychain() else {
      print("❌ 无法恢复认证令牌")
      return nil
    }

    // 恢复最新的订阅状态（重要：这里更新用户对象的订阅状态）
    let currentSubscriptionStatus = restoreSubscriptionStatus() ?? .free
    print("🔄 恢复用户时更新订阅状态: \(user.subscriptionStatus.displayName) -> \(currentSubscriptionStatus.displayName)")

    // 创建包含最新订阅状态的用户对象
    let updatedUser = User(
      id: user.id,
      email: user.email,
      displayName: user.displayName,
      avatarURL: user.avatarURL,
      createdAt: user.createdAt,
      subscriptionStatus: currentSubscriptionStatus
    )

    print("✅ 成功恢复用户登录状态: \(updatedUser.displayName)")
    print("   最终订阅状态: \(updatedUser.subscriptionStatus.displayName)")
    return (user: updatedUser, token: token)
  }
  
  /// 清除用户登录状态
  public static func clearUserSession() {
    print("🗑️ 清除用户登录状态")
    
    UserDefaults.standard.removeObject(forKey: Keys.userInfo)
    UserDefaults.standard.removeObject(forKey: Keys.loginTimestamp)
    removeTokenFromKeychain()
    
    UserDefaults.standard.synchronize()
    print("✅ 用户登录状态已清除")
  }

  // MARK: - Subscription Status Persistence

  /// 保存用户订阅状态
  public static func saveSubscriptionStatus(_ status: SubscriptionStatus) {
    print("💾 保存用户订阅状态: \(status.displayName)")

    if let statusData = try? JSONEncoder().encode(status) {
      UserDefaults.standard.set(statusData, forKey: Keys.subscriptionStatus)
      UserDefaults.standard.set(Date().timeIntervalSince1970, forKey: Keys.subscriptionTimestamp)
      UserDefaults.standard.synchronize()
      print("✅ 订阅状态已保存到本地存储")
    } else {
      print("❌ 订阅状态序列化失败")
    }
  }

  /// 恢复用户订阅状态
  public static func restoreSubscriptionStatus() -> SubscriptionStatus? {
    print("🔄 尝试恢复用户订阅状态")

    guard let statusData = UserDefaults.standard.data(forKey: Keys.subscriptionStatus),
          let status = try? JSONDecoder().decode(SubscriptionStatus.self, from: statusData) else {
      print("❌ 无法恢复订阅状态数据")
      return nil
    }

    // 检查订阅状态是否仍然有效
    if status.isActive {
      print("✅ 成功恢复有效的订阅状态: \(status.displayName)")
      return status
    } else {
      print("⚠️ 订阅状态已过期，清除本地数据")
      clearSubscriptionStatus()
      return .expired
    }
  }

  /// 清除用户订阅状态
  public static func clearSubscriptionStatus() {
    print("🗑️ 清除用户订阅状态")

    UserDefaults.standard.removeObject(forKey: Keys.subscriptionStatus)
    UserDefaults.standard.removeObject(forKey: Keys.subscriptionTimestamp)
    UserDefaults.standard.synchronize()

    print("✅ 订阅状态已清除")
  }
  
  /// 检查是否有有效的登录状态
  public static func hasValidSession() -> Bool {
    let loginTimestamp = UserDefaults.standard.double(forKey: Keys.loginTimestamp)
    let currentTime = Date().timeIntervalSince1970
    let sevenDaysInSeconds: TimeInterval = 7 * 24 * 60 * 60
    
    let hasUserData = UserDefaults.standard.data(forKey: Keys.userInfo) != nil
    let hasToken = getTokenFromKeychain() != nil
    let isNotExpired = (currentTime - loginTimestamp) <= sevenDaysInSeconds
    
    return hasUserData && hasToken && isNotExpired
  }
  
  // MARK: - Private Keychain Methods
  
  private static func saveTokenToKeychain(token: String) {
    let tokenData = token.data(using: .utf8)!
    
    let query: [String: Any] = [
      kSecClass as String: kSecClassGenericPassword,
      kSecAttrAccount as String: Keys.authToken,
      kSecValueData as String: tokenData
    ]
    
    // 删除旧的令牌
    SecItemDelete(query as CFDictionary)
    
    // 添加新的令牌
    let status = SecItemAdd(query as CFDictionary, nil)
    
    if status == errSecSuccess {
      print("✅ 认证令牌已保存到Keychain")
    } else {
      print("❌ 保存认证令牌到Keychain失败: \(status)")
    }
  }
  
  private static func getTokenFromKeychain() -> String? {
    let query: [String: Any] = [
      kSecClass as String: kSecClassGenericPassword,
      kSecAttrAccount as String: Keys.authToken,
      kSecReturnData as String: true,
      kSecMatchLimit as String: kSecMatchLimitOne
    ]
    
    var result: AnyObject?
    let status = SecItemCopyMatching(query as CFDictionary, &result)
    
    if status == errSecSuccess,
       let tokenData = result as? Data,
       let token = String(data: tokenData, encoding: .utf8) {
      print("✅ 从Keychain恢复认证令牌")
      return token
    } else {
      print("❌ 从Keychain获取认证令牌失败: \(status)")
      return nil
    }
  }
  
  private static func removeTokenFromKeychain() {
    let query: [String: Any] = [
      kSecClass as String: kSecClassGenericPassword,
      kSecAttrAccount as String: Keys.authToken
    ]
    
    let status = SecItemDelete(query as CFDictionary)
    
    if status == errSecSuccess || status == errSecItemNotFound {
      print("✅ 认证令牌已从Keychain删除")
    } else {
      print("❌ 从Keychain删除认证令牌失败: \(status)")
    }
  }
}

// MARK: - User Codable Extension

extension User: Codable {
  enum CodingKeys: String, CodingKey {
    case id, email, displayName, avatarURL, createdAt, subscriptionStatus
  }
  
  public func encode(to encoder: Encoder) throws {
    var container = encoder.container(keyedBy: CodingKeys.self)
    try container.encode(id, forKey: .id)
    try container.encode(email, forKey: .email)
    try container.encode(displayName, forKey: .displayName)
    try container.encodeIfPresent(avatarURL, forKey: .avatarURL)
    try container.encode(createdAt, forKey: .createdAt)
    try container.encode(subscriptionStatus, forKey: .subscriptionStatus)
  }
  
  public init(from decoder: Decoder) throws {
    let container = try decoder.container(keyedBy: CodingKeys.self)
    id = try container.decode(String.self, forKey: .id)
    email = try container.decode(String.self, forKey: .email)
    displayName = try container.decode(String.self, forKey: .displayName)
    avatarURL = try container.decodeIfPresent(String.self, forKey: .avatarURL)
    createdAt = try container.decode(Date.self, forKey: .createdAt)
    subscriptionStatus = try container.decode(SubscriptionStatus.self, forKey: .subscriptionStatus)
  }
}

