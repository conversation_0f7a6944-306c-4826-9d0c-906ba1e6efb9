import ComposableArchitecture
import Foundation
import AuthenticationClient
import QuotaClient

// MARK: - User State Management

@Reducer
public struct UserState: Sendable {
  @ObservableState
  public struct State: Equatable, Sendable {
    public var authenticationStatus: AuthenticationStatus = .guest
    public var user: User?
    public var subscriptionStatus: SubscriptionStatus = .free
    public var guestUsageStats: GuestUsageStats = GuestUsageStats()
    public var usageTracker: UsageTracker = UsageTracker()
    public var quotaInfo: QuotaInfo?
    public var lastQuotaSync: Date?
    public var shouldPromptLogin: Bool = false
    public var isLoading: Bool = false
    public var error: String?

    public init() {}
    
    // Computed properties for UI
    public var isGuest: Bool {
      authenticationStatus == .guest
    }
    
    public var isAuthenticated: Bool {
      authenticationStatus == .authenticated
    }
    
    public var canGenerateImages: Bool {
      // Use server-side quota info if available
      if let quota = quotaInfo {
        return quota.canGenerate
      }
      
      // Fallback to legacy logic
      switch authenticationStatus {
      case .guest:
        return guestUsageStats.remainingGenerations > 0
      case .authenticated:
        switch subscriptionStatus {
        case .free:
          return false
        case .premium(_, let limits):
          return usageTracker.canGenerate(with: limits)
        case .expired:
          return false
        }
      }
    }
    
    public var displayName: String {
      if let user = user {
        return user.displayName
      } else {
        return "游客用户"
      }
    }
    
    public var avatarInitials: String {
      if let user = user {
        let components = user.displayName.components(separatedBy: " ")
        let initials = components.compactMap { $0.first }.prefix(2)
        return String(initials).uppercased()
      } else {
        return "游"
      }
    }
  }
  
  public enum Action: BindableAction, Sendable {
    case binding(BindingAction<State>)
    case onAppear
    case loadStoredUser
    case storedUserLoaded(User, String) // user, token
    case incrementGuestUsage(UsageType)
    case checkLoginPromptThreshold
    case promptLogin
    case dismissLoginPrompt
    case signInWithApple(AppleIDCredential)
    case signInWithEmail
    case signInWithEmailCredentials(email: String, password: String)
    case loginSucceeded(User, String) // user, token
    case loginFailed(String)
    case logout
    case logoutCompleted
    case resetGuestUsage
    case restoreUserSession
    case userSessionRestored(User, String)
    case userSessionRestorationFailed
    // Usage tracking actions
    case updateSubscriptionStatus(SubscriptionStatus)
    case consumeGeneration
    case addSinglePurchaseCredits(Int)
    case resetUsageIfNeeded
    // Quota management actions
    case syncQuota
    case quotaSynced(QuotaInfo)
    case quotaSyncFailed(String)
    case consumeServerQuota
    case serverQuotaConsumed(QuotaInfo)
    case serverQuotaConsumptionFailed(String)
  }
  
  @Dependency(\.authenticationClient) var authenticationClient
  @Dependency(\.quotaClient) var quotaClient

  public init() {}
  
  public var body: some ReducerOf<Self> {
    BindingReducer()

    Reduce<State, Action> { state, action in
      switch action {
      case .binding:
        return .none
        
      case .onAppear:
        // Try to load stored user on app launch and sync quota
        return .merge(
          .send(.loadStoredUser),
          .send(.syncQuota)
        )

      case .loadStoredUser:
        // Try to load stored user from persistence
        return .send(.restoreUserSession)

      case let .storedUserLoaded(user, _):
        state.authenticationStatus = .authenticated
        state.user = user
        state.shouldPromptLogin = false
        state.guestUsageStats = GuestUsageStats() // Reset guest stats

        print("🔄 已从存储加载用户:")
        print("   用户ID: \(user.id)")
        print("   显示名: \(user.displayName)")
        print("   订阅状态: \(user.subscriptionStatus.displayName)")

        return .none
        
      case let .incrementGuestUsage(usageType):
        guard state.isGuest else { return .none }
        
        switch usageType {
        case .imageGeneration:
          state.guestUsageStats.totalGenerations += 1
          state.guestUsageStats.remainingGenerations = max(0, state.guestUsageStats.remainingGenerations - 1)
        case .imageDownload:
          state.guestUsageStats.totalDownloads += 1
        case .styleSelection:
          state.guestUsageStats.totalStyleSelections += 1
        }
        
        return .send(.checkLoginPromptThreshold)
        
      case .checkLoginPromptThreshold:
        guard state.isGuest else { return .none }
        
        let stats = state.guestUsageStats
        let shouldPrompt = stats.totalGenerations >= GuestUsageStats.loginPromptThreshold ||
                          stats.remainingGenerations <= 1
        
        if shouldPrompt && !state.shouldPromptLogin {
          return .send(.promptLogin)
        }
        
        return .none
        
      case .promptLogin:
        state.shouldPromptLogin = true
        return .none
        
      case .dismissLoginPrompt:
        state.shouldPromptLogin = false
        return .none

      case let .signInWithApple(credential):
        state.isLoading = true
        state.error = nil

        return .run { send in
          do {
            print("🍎 开始Apple ID登录")

            // Use AuthenticationClient for Apple login
            let response = try await self.authenticationClient.signInWithApple(credential)

            if let authenticatedUser = response.user {
              // 从服务端获取用户的真实订阅状态
              let subscriptionStatus = await fetchUserSubscriptionStatus(userId: authenticatedUser.id) ?? .free

              let user = User(
                id: authenticatedUser.id,
                email: authenticatedUser.email,
                displayName: authenticatedUser.displayName,
                avatarURL: authenticatedUser.avatarURL,
                createdAt: Date(),
                subscriptionStatus: subscriptionStatus
              )

              print("✅ Apple ID登录成功: \(user.displayName)")
              await send(.loginSucceeded(user, response.token))
            } else {
              print("❌ Apple ID登录失败: 用户信息为空")
              await send(.loginFailed("Apple ID登录失败：用户信息获取失败"))
            }
          } catch {
            print("❌ Apple ID登录异常: \(error.localizedDescription)")
            await send(.loginFailed(error.localizedDescription))
          }
        }

      case .signInWithEmail:
        // This will be handled by the parent view to show email login form
        return .none

      case let .signInWithEmailCredentials(email, password):
        state.isLoading = true
        state.error = nil

        return .run { [authClient = self.authenticationClient] send in
          do {
            print("📧 开始邮箱登录: \(email)")

            // Use AuthenticationClient for consistent login flow
            let response = try await authClient.login(email: email, password: password)

            if let authenticatedUser = response.user {
              // 从服务端获取用户的真实订阅状态
              let subscriptionStatus = await fetchUserSubscriptionStatus(userId: authenticatedUser.id) ?? .free

              let user = User(
                id: authenticatedUser.id,
                email: authenticatedUser.email,
                displayName: authenticatedUser.displayName,
                avatarURL: authenticatedUser.avatarURL,
                createdAt: Date(),
                subscriptionStatus: subscriptionStatus
              )

              print("✅ 邮箱登录成功: \(user.displayName)")
              await send(.loginSucceeded(user, response.token))
            } else {
              print("❌ 邮箱登录失败: 用户信息为空")
              await send(.loginFailed("登录失败：用户信息获取失败"))
            }
          } catch {
            print("❌ 邮箱登录异常: \(error.localizedDescription)")
            await send(.loginFailed(error.localizedDescription))
          }
        }

      case let .loginSucceeded(user, token):
        state.authenticationStatus = .authenticated
        state.user = user
        state.subscriptionStatus = user.subscriptionStatus
        state.isLoading = false
        state.error = nil
        state.shouldPromptLogin = false
        state.guestUsageStats = GuestUsageStats() // Reset guest stats
        state.usageTracker = UsageTracker() // Reset usage tracker

        print("🎉 用户登录状态已更新:")
        print("   认证状态: \(state.authenticationStatus)")
        print("   用户ID: \(user.id)")
        print("   显示名: \(user.displayName)")
        print("   订阅状态: \(user.subscriptionStatus.displayName)")
        print("   访问令牌: \(token.prefix(20))...")

        // 保存用户登录状态到本地存储
        UserPersistenceService.saveUserSession(user: user, token: token)
        
        // Sync quota after successful login
        return .send(.syncQuota)

      case let .loginFailed(errorMessage):
        state.isLoading = false
        state.error = errorMessage
        state.authenticationStatus = .guest
        print("❌ 登录失败: \(errorMessage)")
        return .none

      case .logout:
        return .run { send in
          do {
            try await self.authenticationClient.logout()
            await send(.logoutCompleted)
          } catch {
            // Handle logout error if needed
            await send(.logoutCompleted)
          }
        }

      case .logoutCompleted:
        state.authenticationStatus = .guest
        state.user = nil
        state.shouldPromptLogin = false
        state.guestUsageStats = GuestUsageStats() // Reset to fresh guest state

        // 清除本地存储的用户数据
        UserPersistenceService.clearUserSession()
        return .none
        
      case .resetGuestUsage:
        state.guestUsageStats = GuestUsageStats()
        return .none

      case .restoreUserSession:
        return .run { send in
          if let (user, token) = UserPersistenceService.restoreUserSession() {
            await send(.userSessionRestored(user, token))
          } else {
            await send(.userSessionRestorationFailed)
          }
        }

      case let .userSessionRestored(user, _):
        state.authenticationStatus = .authenticated
        state.user = user
        state.subscriptionStatus = user.subscriptionStatus
        state.shouldPromptLogin = false
        state.guestUsageStats = GuestUsageStats() // Reset guest stats
        state.usageTracker = UsageTracker() // Reset usage tracker
        state.isLoading = false
        state.error = nil

        print("🔄 用户会话已恢复:")
        print("   用户ID: \(user.id)")
        print("   显示名: \(user.displayName)")
        print("   订阅状态: \(user.subscriptionStatus.displayName)")

        // 注意：订阅状态已经在 UserPersistenceService.restoreUserSession() 中更新了
        // 这里的 user.subscriptionStatus 已经是最新的状态

        // Sync quota after session restoration
        return .send(.syncQuota)

      case .userSessionRestorationFailed:
        print("⚠️ 用户会话恢复失败，保持访客状态")
        return .none

      // Usage tracking actions
      case let .updateSubscriptionStatus(newStatus):
        state.subscriptionStatus = newStatus
        // Reset usage tracker when subscription changes
        state.usageTracker = UsageTracker()
        return .none

      case .consumeGeneration:
        guard state.authenticationStatus == .authenticated else {
          // For guests, use the existing guest usage system
          return .send(.incrementGuestUsage(.imageGeneration))
        }

        // Reset usage if needed (monthly/yearly cycles)
        state.usageTracker.resetIfNeeded()

        // Consume generation based on current subscription limits
        let limits = state.subscriptionStatus.usageLimits
        state.usageTracker.consumeGeneration(with: limits)
        return .none

      case let .addSinglePurchaseCredits(credits):
        state.usageTracker.addSinglePurchaseCredits(credits)
        return .none

      case .resetUsageIfNeeded:
        state.usageTracker.resetIfNeeded()
        return .none
        
      // Quota management actions
      case .syncQuota:
        return .run { send in
          do {
            let quotaInfo = try await self.quotaClient.getCurrentQuota()
            await send(.quotaSynced(quotaInfo))
          } catch {
            await send(.quotaSyncFailed(error.localizedDescription))
          }
        }
        
      case let .quotaSynced(quotaInfo):
        state.quotaInfo = quotaInfo
        state.lastQuotaSync = Date()
        
        // Update legacy usage stats for compatibility
        if quotaInfo.userType == .guest {
          state.guestUsageStats.remainingGenerations = quotaInfo.remainingQuota
        }
        
        print("✅ 配额同步成功: 剩余 \(quotaInfo.remainingQuota)/\(quotaInfo.totalQuota)")
        return .none
        
      case let .quotaSyncFailed(error):
        print("❌ 配额同步失败: \(error)")
        state.error = "配额同步失败: \(error)"
        return .none
        
      case .consumeServerQuota:
        return .run { send in
          do {
            let updatedQuota = try await self.quotaClient.consumeGeneration()
            await send(.serverQuotaConsumed(updatedQuota))
          } catch {
            await send(.serverQuotaConsumptionFailed(error.localizedDescription))
          }
        }
        
      case let .serverQuotaConsumed(quotaInfo):
        state.quotaInfo = quotaInfo
        state.lastQuotaSync = Date()
        
        // Update legacy usage stats for compatibility
        if quotaInfo.userType == .guest {
          state.guestUsageStats.remainingGenerations = quotaInfo.remainingQuota
          state.guestUsageStats.totalGenerations += 1
        }
        
        print("🎯 服务端配额消费成功: 剩余 \(quotaInfo.remainingQuota)/\(quotaInfo.totalQuota)")
        return .none
        
      case let .serverQuotaConsumptionFailed(error):
        print("❌ 服务端配额消费失败: \(error)")
        state.error = "配额消费失败: \(error)"
        return .none
      }
    }
  }
}

// MARK: - Helper Functions

/// 从服务端获取用户的订阅状态
private func fetchUserSubscriptionStatus(userId: String) async -> SubscriptionStatus? {
  print("🔍 获取用户订阅状态: \(userId)")

  // 1. 首先尝试从本地存储恢复订阅状态
  if let savedSubscriptionStatus = UserPersistenceService.restoreSubscriptionStatus() {
    print("✅ 从本地存储恢复订阅状态: \(savedSubscriptionStatus.displayName)")

    // 检查订阅是否过期
    if savedSubscriptionStatus.isActive {
      return savedSubscriptionStatus
    } else {
      print("⚠️ 本地订阅状态已过期，重置为免费版")
      UserPersistenceService.clearSubscriptionStatus()
      return .free
    }
  }

  // 2. 如果本地没有，可以在这里添加服务端API调用
  // TODO: 实现服务端API调用获取用户订阅状态
  /*
  do {
    let response = try await APIClient.getUserSubscriptionStatus(userId: userId)
    if response.subscriptionStatus.isActive {
      // 保存到本地存储
      UserPersistenceService.saveSubscriptionStatus(response.subscriptionStatus)
      return response.subscriptionStatus
    }
  } catch {
    print("❌ 从服务端获取订阅状态失败: \(error)")
  }
  */

  print("ℹ️ 无法获取订阅状态，默认为免费版")
  return .free
}

// MARK: - Supporting Types

public enum AuthenticationStatus: String, Equatable, Sendable {
  case guest = "guest"
  case authenticated = "authenticated"
}

public enum UsageType: Equatable, Sendable {
  case imageGeneration
  case imageDownload
  case styleSelection
}

public struct GuestUsageStats: Equatable, Sendable {
  public var totalGenerations: Int = 0
  public var totalDownloads: Int = 0
  public var totalStyleSelections: Int = 0
  public var remainingGenerations: Int = Self.maxGuestGenerations
  
  public init() {}
  
  // Configuration constants
  public static let maxGuestGenerations = 3
  public static let loginPromptThreshold = 2 // Prompt after 2 generations
}

public struct User: Equatable, Sendable {
  public let id: String
  public let email: String
  public let displayName: String
  public let avatarURL: String?
  public let createdAt: Date
  public let subscriptionStatus: SubscriptionStatus
  
  public init(
    id: String,
    email: String,
    displayName: String,
    avatarURL: String? = nil,
    createdAt: Date = Date(),
    subscriptionStatus: SubscriptionStatus = .free
  ) {
    self.id = id
    self.email = email
    self.displayName = displayName
    self.avatarURL = avatarURL
    self.createdAt = createdAt
    self.subscriptionStatus = subscriptionStatus
  }
  
  // Computed properties
  public var initials: String {
    let components = displayName.components(separatedBy: " ")
    let initials = components.compactMap { $0.first }.prefix(2)
    return String(initials).uppercased()
  }
}

// MARK: - Usage Limits

public struct UsageLimits: Equatable, Sendable, Codable {
  public let generationsPerMonth: Int?  // nil means unlimited
  public let generationsPerYear: Int?   // nil means unlimited
  public let singlePurchaseGenerations: Int? // for single purchases

  public init(
    generationsPerMonth: Int? = nil,
    generationsPerYear: Int? = nil,
    singlePurchaseGenerations: Int? = nil
  ) {
    self.generationsPerMonth = generationsPerMonth
    self.generationsPerYear = generationsPerYear
    self.singlePurchaseGenerations = singlePurchaseGenerations
  }

  public static let unlimited = UsageLimits()

  public static let monthlyBasic = UsageLimits(generationsPerMonth: 40)
  public static let monthlyPremium = UsageLimits(generationsPerMonth: 40)
  public static let yearlyStandard = UsageLimits(generationsPerYear: 600)
  public static let singleUse = UsageLimits(singlePurchaseGenerations: 1)
}

// MARK: - Usage Tracking

public struct UsageTracker: Equatable, Sendable, Codable {
  public var monthlyGenerationsUsed: Int = 0
  public var yearlyGenerationsUsed: Int = 0
  public var singlePurchaseCredits: Int = 0
  public var lastResetDate: Date = Date()
  public var currentSubscriptionType: String? = nil

  public init(
    monthlyGenerationsUsed: Int = 0,
    yearlyGenerationsUsed: Int = 0,
    singlePurchaseCredits: Int = 0,
    lastResetDate: Date = Date(),
    currentSubscriptionType: String? = nil
  ) {
    self.monthlyGenerationsUsed = monthlyGenerationsUsed
    self.yearlyGenerationsUsed = yearlyGenerationsUsed
    self.singlePurchaseCredits = singlePurchaseCredits
    self.lastResetDate = lastResetDate
    self.currentSubscriptionType = currentSubscriptionType
  }

  public mutating func resetIfNeeded() {
    let calendar = Calendar.current
    let now = Date()

    // Reset monthly usage if it's a new month
    if !calendar.isDate(lastResetDate, equalTo: now, toGranularity: .month) {
      monthlyGenerationsUsed = 0
    }

    // Reset yearly usage if it's a new year
    if !calendar.isDate(lastResetDate, equalTo: now, toGranularity: .year) {
      yearlyGenerationsUsed = 0
    }

    lastResetDate = now
  }

  public func canGenerate(with limits: UsageLimits) -> Bool {
    // Check single purchase credits first
    if singlePurchaseCredits > 0 {
      return true
    }

    // Check monthly limits
    if let monthlyLimit = limits.generationsPerMonth {
      return monthlyGenerationsUsed < monthlyLimit
    }

    // Check yearly limits
    if let yearlyLimit = limits.generationsPerYear {
      return yearlyGenerationsUsed < yearlyLimit
    }

    // No limits means unlimited
    return true
  }

  public mutating func consumeGeneration(with limits: UsageLimits) {
    // Use single purchase credits first
    if singlePurchaseCredits > 0 {
      singlePurchaseCredits -= 1
      return
    }

    // Otherwise consume from subscription limits
    if limits.generationsPerMonth != nil {
      monthlyGenerationsUsed += 1
    }

    if limits.generationsPerYear != nil {
      yearlyGenerationsUsed += 1
    }
  }

  public mutating func addSinglePurchaseCredits(_ credits: Int) {
    singlePurchaseCredits += credits
  }
}

public enum SubscriptionStatus: Equatable, Sendable {
  case free
  case premium(expiryDate: Date, usageLimits: UsageLimits)
  case expired

  public var displayName: String {
    switch self {
    case .free: return "免费用户"
    case .premium: return "高级会员"
    case .expired: return "订阅已过期"
    }
  }

  public var isActive: Bool {
    switch self {
    case .premium(let expiryDate, _):
      return expiryDate > Date()
    case .free, .expired:
      return false
    }
  }

  public var isPremiumActive: Bool {
    return isActive
  }

  public var usageLimits: UsageLimits {
    switch self {
    case .premium(_, let limits):
      return limits
    case .free, .expired:
      return UsageLimits() // No limits for free users (they use guest limits)
    }
  }
}

// MARK: - SubscriptionStatus Codable Implementation

extension SubscriptionStatus: Codable {
  private enum CodingKeys: String, CodingKey {
    case type
    case expiryDate
    case usageLimits
  }

  private enum StatusType: String, Codable {
    case free
    case premium
    case expired
  }

  public init(from decoder: Decoder) throws {
    let container = try decoder.container(keyedBy: CodingKeys.self)
    let type = try container.decode(StatusType.self, forKey: .type)

    switch type {
    case .free:
      self = .free
    case .expired:
      self = .expired
    case .premium:
      let expiryDate = try container.decode(Date.self, forKey: .expiryDate)
      let usageLimits = try container.decode(UsageLimits.self, forKey: .usageLimits)
      self = .premium(expiryDate: expiryDate, usageLimits: usageLimits)
    }
  }

  public func encode(to encoder: Encoder) throws {
    var container = encoder.container(keyedBy: CodingKeys.self)

    switch self {
    case .free:
      try container.encode(StatusType.free, forKey: .type)
    case .expired:
      try container.encode(StatusType.expired, forKey: .type)
    case .premium(let expiryDate, let usageLimits):
      try container.encode(StatusType.premium, forKey: .type)
      try container.encode(expiryDate, forKey: .expiryDate)
      try container.encode(usageLimits, forKey: .usageLimits)
    }
  }
}
