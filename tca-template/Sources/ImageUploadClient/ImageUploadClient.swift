import Foundation
import ComposableArchitecture
import NetworkClient
import Dependencies
import AuthenticationClient

// MARK: - Image Upload Client Interface

@DependencyClient
public struct ImageUploadClient: Sendable {
  /// Upload image to server and get URL
  public var uploadImage: @Sendable (Data, String) async throws -> ImageUploadResponse = { _, _ in
    throw ImageUploadError.notImplemented
  }
  
  /// Upload multiple images
  public var uploadImages: @Sendable ([Data]) async throws -> [ImageUploadResponse] = { _ in
    throw ImageUploadError.notImplemented
  }
}

// MARK: - Request/Response Models

public struct ImageUploadResponse: Codable, Sendable {
  public let success: Bool
  public let message: String
  public let data: UploadData?
  public let error: String?
  public let url: String
  public let objectName: String

  private enum CodingKeys: String, CodingKey {
    case success
    case message
    case data
    case error
    case url
    case objectName = "object_name"
  }

  public init(
    success: Bool,
    message: String,
    data: UploadData? = nil,
    error: String? = nil,
    url: String,
    objectName: String
  ) {
    self.success = success
    self.message = message
    self.data = data
    self.error = error
    self.url = url
    self.objectName = objectName
  }
}

public struct UploadData: Codable, Sendable {
  public let objectName: String
  public let contentType: String
  public let size: Int
  public let bucket: String

  private enum CodingKeys: String, CodingKey {
    case objectName = "object_name"
    case contentType = "content_type"
    case size
    case bucket
  }

  public init(
    objectName: String,
    contentType: String,
    size: Int,
    bucket: String
  ) {
    self.objectName = objectName
    self.contentType = contentType
    self.size = size
    self.bucket = bucket
  }
}

// MARK: - Error Types

public enum ImageUploadError: Error, Equatable, LocalizedError, Sendable {
  case notImplemented
  case networkError(String)
  case invalidImageData
  case uploadFailed(String)
  case serverError(String)
  case authenticationRequired
  
  public var errorDescription: String? {
    switch self {
    case .notImplemented:
      return "Upload functionality not implemented"
    case .networkError(let message):
      return "Network error: \(message)"
    case .invalidImageData:
      return "Invalid image data"
    case .uploadFailed(let message):
      return "Upload failed: \(message)"
    case .serverError(let message):
      return "Server error: \(message)"
    case .authenticationRequired:
      return "Authentication required for upload"
    }
  }
}

// MARK: - Dependency Registration

extension ImageUploadClient: DependencyKey {
  public static let liveValue = Self.live()
}

// MARK: - Live Implementation Factory

extension ImageUploadClient {
  public static func live() -> Self {
    return Self(
      uploadImage: { imageData, fileName in
        return try await uploadSingleImage(imageData, fileName: fileName)
      },
      uploadImages: { imagesData in
        return try await uploadMultipleImages(imagesData)
      }
    )
  }
}

extension ImageUploadClient: TestDependencyKey {
  public static let testValue = Self()
}

extension DependencyValues {
  public var imageUploadClient: ImageUploadClient {
    get { self[ImageUploadClient.self] }
    set { self[ImageUploadClient.self] = newValue }
  }
}

// MARK: - Helper Functions

private func uploadSingleImage(_ imageData: Data, fileName: String) async throws -> ImageUploadResponse {
  print("🔄 [ImageUpload] Helper - Starting upload for file: \(fileName)")
  print("🔄 [ImageUpload] Helper - Image data size: \(imageData.count) bytes")

  // Get authentication token
  guard let authToken = AccessTokenManager.getAuthorizationHeader() else {
    print("❌ [ImageUpload] Helper - No valid authentication token found")
    throw ImageUploadError.authenticationRequired
  }

  print("🔐 [ImageUpload] Helper - Using auth token: \(authToken.prefix(50))...")

  // Create multipart form data
  let boundary = "----FormBoundary\(UUID().uuidString)"
  let multipartData = createMultipartFormData(
    imageData: imageData,
    fileName: fileName,
    boundary: boundary
  )

  print("🔄 [ImageUpload] Helper - Created multipart data, size: \(multipartData.count) bytes")

  // Create network request
  let baseURL = "http://127.0.0.1:8000"

  let url = URL(string: "\(baseURL)/api/v1/image/upload?file")!
  let request = NetworkRequest(
    url: url,
    method: .POST,
    headers: [
      "Authorization": authToken,
      "Content-Type": "multipart/form-data; boundary=\(boundary)",
      "User-Agent": "BridalApp/1.0.0",
      "Accept": "*/*",
      "Connection": "keep-alive"
    ],
    body: multipartData,
    timeout: 60.0
  )

  print("🔄 [ImageUpload] Helper - Sending request to: \(url.absoluteString)")
  print("🔄 [ImageUpload] Helper - Request headers: \(request.headers)")
  print("🔄 [ImageUpload] Helper - Request body size: \(multipartData.count) bytes")

  // Use URLSession directly instead of @Dependency
  do {
    // Create URLRequest
    var urlRequest = URLRequest(url: request.url)
    urlRequest.httpMethod = request.method.rawValue
    urlRequest.timeoutInterval = request.timeout

    // Set headers
    for (key, value) in request.headers {
      urlRequest.setValue(value, forHTTPHeaderField: key)
    }

    // Set body
    if let body = request.body {
      urlRequest.httpBody = body
    }

    print("🌐 [ImageUpload] Helper - Making URLSession request...")
    let (responseData, response) = try await URLSession.shared.data(for: urlRequest)

    print("🌐 [ImageUpload] Helper - Received response:")
    if let httpResponse = response as? HTTPURLResponse {
      print("   - Status code: \(httpResponse.statusCode)")
      print("   - Headers: \(httpResponse.allHeaderFields)")
    }
    print("   - Data size: \(responseData.count) bytes")

    // Log raw response for debugging
    if let responseString = String(data: responseData, encoding: .utf8) {
      print("✅ [ImageUpload] Helper - Raw response:")
      print(responseString)
    }

    // Validate HTTP response
    if let httpResponse = response as? HTTPURLResponse {
      if httpResponse.statusCode >= 400 {
        throw ImageUploadError.serverError("HTTP \(httpResponse.statusCode)")
      }
    }

    // Parse response
    print("🔍 [ImageUpload] Helper - Attempting to parse JSON response...")

    do {
      let uploadResponse = try JSONDecoder().decode(ImageUploadResponse.self, from: responseData)

      print("✅ [ImageUpload] Helper - JSON parsing successful!")
      print("✅ [ImageUpload] Helper - Upload successful!")
      print("✅ [ImageUpload] Helper - Response success: \(uploadResponse.success)")
      print("✅ [ImageUpload] Helper - Response message: \(uploadResponse.message)")
      print("✅ [ImageUpload] Helper - Image URL: \(uploadResponse.url)")
      print("✅ [ImageUpload] Helper - Object name: \(uploadResponse.objectName)")

      if let data = uploadResponse.data {
        print("✅ [ImageUpload] Helper - Upload data:")
        print("   - Object name: \(data.objectName)")
        print("   - Content type: \(data.contentType)")
        print("   - Size: \(data.size) bytes")
        print("   - Bucket: \(data.bucket)")
      }

      if let error = uploadResponse.error {
        print("⚠️ [ImageUpload] Helper - Response contains error: \(error)")
      }

      return uploadResponse

    } catch let decodingError as DecodingError {
      print("❌ [ImageUpload] Helper - JSON parsing failed with DecodingError:")

      switch decodingError {
      case .keyNotFound(let key, let context):
        print("   - Missing key: \(key.stringValue)")
        print("   - Context: \(context.debugDescription)")
        print("   - Coding path: \(context.codingPath.map { $0.stringValue })")
      case .typeMismatch(let type, let context):
        print("   - Type mismatch for type: \(type)")
        print("   - Context: \(context.debugDescription)")
        print("   - Coding path: \(context.codingPath.map { $0.stringValue })")
      case .valueNotFound(let type, let context):
        print("   - Value not found for type: \(type)")
        print("   - Context: \(context.debugDescription)")
        print("   - Coding path: \(context.codingPath.map { $0.stringValue })")
      case .dataCorrupted(let context):
        print("   - Data corrupted")
        print("   - Context: \(context.debugDescription)")
        print("   - Coding path: \(context.codingPath.map { $0.stringValue })")
      @unknown default:
        print("   - Unknown decoding error: \(decodingError)")
      }

      throw ImageUploadError.uploadFailed("Failed to parse server response: \(decodingError.localizedDescription)")
    }

  } catch {
    print("❌ [ImageUpload] Helper - Upload failed with error: \(error)")

    if error is DecodingError {
      throw ImageUploadError.uploadFailed("Failed to parse server response")
    } else {
      throw ImageUploadError.uploadFailed(error.localizedDescription)
    }
  }
}

private func uploadMultipleImages(_ imagesData: [Data]) async throws -> [ImageUploadResponse] {
  print("🔄 [ImageUpload] Helper - Starting batch upload for \(imagesData.count) images")

  var responses: [ImageUploadResponse] = []

  for (index, imageData) in imagesData.enumerated() {
    let fileName = "image_\(index + 1)_\(UUID().uuidString).jpeg"
    print("🔄 [ImageUpload] Helper - Uploading image \(index + 1)/\(imagesData.count): \(fileName)")

    do {
      let response = try await uploadSingleImage(imageData, fileName: fileName)
      responses.append(response)
      print("✅ [ImageUpload] Helper - Image \(index + 1) uploaded successfully")
    } catch {
      print("❌ [ImageUpload] Helper - Failed to upload image \(index + 1): \(error)")
      throw error
    }
  }

  print("✅ [ImageUpload] Helper - Batch upload completed. \(responses.count) images uploaded successfully")
  return responses
}

private func createMultipartFormData(
  imageData: Data,
  fileName: String,
  boundary: String
) -> Data {
  var formData = Data()

  // Add file field
  formData.append("--\(boundary)\r\n".data(using: .utf8)!)
  formData.append("Content-Disposition: form-data; name=\"file\"; filename=\"\(fileName)\"\r\n".data(using: .utf8)!)
  formData.append("Content-Type: image/jpeg\r\n\r\n".data(using: .utf8)!)
  formData.append(imageData)
  formData.append("\r\n".data(using: .utf8)!)

  // Add closing boundary
  formData.append("--\(boundary)--\r\n".data(using: .utf8)!)

  return formData
}
