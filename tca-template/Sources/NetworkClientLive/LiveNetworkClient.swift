import Dependencies
import Foundation
import NetworkClient

// MARK: - Live Network Client Implementation

extension NetworkClient: DependencyKey {
  public static let liveValue = Self(
    request: { request in
      print("🌐 [NetworkClient] Live implementation - Making request to: \(request.url.absoluteString)")
      print("🌐 [NetworkClient] Live implementation - Method: \(request.method.rawValue)")
      print("🌐 [NetworkClient] Live implementation - Headers: \(request.headers)")
      if let body = request.body {
        print("🌐 [NetworkClient] Live implementation - Body size: \(body.count) bytes")
      }

      let result = try await performRequest(request)
      print("✅ [NetworkClient] Live implementation - Request completed, response size: \(result.count) bytes")
      return result
    },
    upload: { request, data in
      print("🌐 [NetworkClient] Live implementation - Making upload request to: \(request.url.absoluteString)")
      print("🌐 [NetworkClient] Live implementation - Upload data size: \(data.count) bytes")

      let result = try await performUpload(request, data: data)
      print("✅ [NetworkClient] Live implementation - Upload completed, response size: \(result.count) bytes")
      return result
    },
    download: { request in
      print("🌐 [NetworkClient] Live implementation - Making download request to: \(request.url.absoluteString)")

      let result = try await performDownload(request)
      print("✅ [NetworkClient] Live implementation - Download completed, file URL: \(result.absoluteString)")
      return result
    }
  )
}

// MARK: - Private Implementation

private func performRequest(_ request: NetworkRequest) async throws -> Data {
  print("🌐 [NetworkClient] performRequest - Creating URLRequest...")
  let urlRequest = try createURLRequest(from: request)

  print("🌐 [NetworkClient] performRequest - URLRequest created:")
  print("   - URL: \(urlRequest.url?.absoluteString ?? "nil")")
  print("   - Method: \(urlRequest.httpMethod ?? "nil")")
  print("   - Headers: \(urlRequest.allHTTPHeaderFields ?? [:])")
  print("   - Body size: \(urlRequest.httpBody?.count ?? 0) bytes")
  print("   - Timeout: \(urlRequest.timeoutInterval) seconds")

  do {
    print("🌐 [NetworkClient] performRequest - Making URLSession request...")
    let (data, response) = try await URLSession.shared.data(for: urlRequest)

    print("🌐 [NetworkClient] performRequest - Received response:")
    if let httpResponse = response as? HTTPURLResponse {
      print("   - Status code: \(httpResponse.statusCode)")
      print("   - Headers: \(httpResponse.allHeaderFields)")
    }
    print("   - Data size: \(data.count) bytes")

    // Log response data for debugging
    if let responseString = String(data: data, encoding: .utf8) {
      print("🌐 [NetworkClient] performRequest - Response body:")
      print(responseString)
    }

    try validateResponse(response, data: data)
    print("✅ [NetworkClient] performRequest - Response validation passed")
    return data
  } catch let error as NetworkError {
    print("❌ [NetworkClient] performRequest - NetworkError: \(error)")
    throw error
  } catch {
    print("❌ [NetworkClient] performRequest - URLSession error: \(error)")
    let mappedError = mapURLSessionError(error)
    print("❌ [NetworkClient] performRequest - Mapped error: \(mappedError)")
    throw mappedError
  }
}

private func performUpload(_ request: NetworkRequest, data: Data) async throws -> Data {
  var urlRequest = try createURLRequest(from: request)
  urlRequest.httpBody = data
  
  do {
    let (responseData, response) = try await URLSession.shared.data(for: urlRequest)
    try validateResponse(response, data: responseData)
    return responseData
  } catch let error as NetworkError {
    throw error
  } catch {
    throw mapURLSessionError(error)
  }
}

private func performDownload(_ request: NetworkRequest) async throws -> URL {
  let urlRequest = try createURLRequest(from: request)
  
  do {
    let (url, response) = try await URLSession.shared.download(for: urlRequest)
    try validateResponse(response, data: nil)
    return url
  } catch let error as NetworkError {
    throw error
  } catch {
    throw mapURLSessionError(error)
  }
}

private func createURLRequest(from request: NetworkRequest) throws -> URLRequest {
  var urlRequest = URLRequest(url: request.url)
  urlRequest.httpMethod = request.method.rawValue
  urlRequest.timeoutInterval = request.timeout
  
  // Set headers
  for (key, value) in request.headers {
    urlRequest.setValue(value, forHTTPHeaderField: key)
  }
  
  // Set body
  if let body = request.body {
    urlRequest.httpBody = body
  }
  
  return urlRequest
}

private func validateResponse(_ response: URLResponse?, data: Data?) throws {
  guard let httpResponse = response as? HTTPURLResponse else {
    throw NetworkError.invalidResponse
  }
  
  let statusCode = httpResponse.statusCode
  
  switch statusCode {
  case 200...299:
    // Success - do nothing
    break
  case 400...499:
    // Client error
    throw NetworkError.httpError(statusCode: statusCode, data: data)
  case 500...599:
    // Server error
    let errorMessage = data.flatMap { String(data: $0, encoding: .utf8) } ?? "Server error"
    throw NetworkError.serverError(errorMessage)
  default:
    throw NetworkError.httpError(statusCode: statusCode, data: data)
  }
}

private func mapURLSessionError(_ error: Error) -> NetworkError {
  if let urlError = error as? URLError {
    switch urlError.code {
    case .notConnectedToInternet, .networkConnectionLost:
      return .noInternetConnection
    case .timedOut:
      return .timeout
    case .badURL:
      return .invalidURL(urlError.localizedDescription)
    default:
      return .unknown(urlError.localizedDescription)
    }
  }
  
  return .unknown(error.localizedDescription)
}

// MARK: - Configuration

public struct NetworkConfiguration: Sendable {
  public let baseURL: URL
  public let defaultHeaders: [String: String]
  public let timeout: TimeInterval
  
  public init(
    baseURL: URL,
    defaultHeaders: [String: String] = [:],
    timeout: TimeInterval = 30.0
  ) {
    self.baseURL = baseURL
    self.defaultHeaders = defaultHeaders
    self.timeout = timeout
  }
}

// MARK: - Convenience Methods

public extension NetworkClient {
  static func configured(with configuration: NetworkConfiguration) -> Self {
    Self(
      request: { request in
        var modifiedRequest = request
        
        // Apply default headers if not already set
        var headers = configuration.defaultHeaders
        headers.merge(request.headers) { _, new in new }
        
        modifiedRequest = NetworkRequest(
          url: request.url,
          method: request.method,
          headers: headers,
          body: request.body,
          timeout: request.timeout == 30.0 ? configuration.timeout : request.timeout
        )
        
        return try await performRequest(modifiedRequest)
      },
      upload: { request, data in
        var modifiedRequest = request
        
        // Apply default headers if not already set
        var headers = configuration.defaultHeaders
        headers.merge(request.headers) { _, new in new }
        
        modifiedRequest = NetworkRequest(
          url: request.url,
          method: request.method,
          headers: headers,
          body: request.body,
          timeout: request.timeout == 30.0 ? configuration.timeout : request.timeout
        )
        
        return try await performUpload(modifiedRequest, data: data)
      },
      download: { request in
        var modifiedRequest = request
        
        // Apply default headers if not already set
        var headers = configuration.defaultHeaders
        headers.merge(request.headers) { _, new in new }
        
        modifiedRequest = NetworkRequest(
          url: request.url,
          method: request.method,
          headers: headers,
          body: request.body,
          timeout: request.timeout == 30.0 ? configuration.timeout : request.timeout
        )
        
        return try await performDownload(modifiedRequest)
      }
    )
  }
}

// MARK: - Mock Network Client for Testing

public extension NetworkClient {
  static func mock(
    requestHandler: @escaping @Sendable (NetworkRequest) async throws -> Data = { _ in Data() },
    uploadHandler: @escaping @Sendable (NetworkRequest, Data) async throws -> Data = { _, _ in Data() },
    downloadHandler: @escaping @Sendable (NetworkRequest) async throws -> URL = { _ in URL(fileURLWithPath: "/tmp/mock") }
  ) -> Self {
    Self(
      request: requestHandler,
      upload: uploadHandler,
      download: downloadHandler
    )
  }
}
