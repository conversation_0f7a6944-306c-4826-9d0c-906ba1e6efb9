import Foundation
import ComposableArchitecture
import UserStateCore
import AuthenticationClient

// MARK: - Login Test Helper

public struct LoginTestHelper {

  /// Test Apple Sign In with mock data
  public static func testAppleSignIn() {
    print("🍎 Testing Apple Sign In...")

    // Create mock name components
    var nameComponents = PersonNameComponents()
    nameComponents.givenName = "Apple"
    nameComponents.familyName = "User"

    // Create mock credential
    let mockCredential = AppleIDCredential(
      userID: "test-apple-user",
      email: "<EMAIL>",
      fullName: nameComponents,
      identityToken: "mock-token".data(using: .utf8)!,
      authorizationCode: "mock-code".data(using: .utf8)!
    )

    print("✅ Mock Apple credential created")
    print("   User ID: \(mockCredential.userID)")
    print("   Email: \(mockCredential.email ?? "N/A")")
    print("   Full Name: \(mockCredential.fullName?.formatted() ?? "N/A")")

    // In a real test, this would trigger the login flow
    print("✅ Apple Sign In test completed")
  }

  /// Test Email Sign In with mock data
  public static func testEmailSignIn() {
    print("📧 Testing Email Sign In...")

    let testEmail = "<EMAIL>"
    let testPassword = "password123"

    print("✅ Mock email credentials created")
    print("   Email: \(testEmail)")
    print("   Password: \(testPassword.count) characters")

    // In a real test, this would trigger the login flow
    print("✅ Email Sign In test completed")
  }

  /// Test logout functionality
  public static func testLogout() {
    print("🚪 Testing Logout...")

    // Create mock authenticated user
    let mockUser = User(
      id: "test-user",
      email: "<EMAIL>",
      displayName: "Test User",
      avatarURL: nil,
      createdAt: Date(),
      subscriptionStatus: .free
    )

    print("✅ Mock authenticated user created")
    print("   User: \(mockUser.displayName)")
    print("   Email: \(mockUser.email)")

    // In a real test, this would trigger the logout flow
    print("✅ Logout test completed")
  }
  
  /// Verify authentication status
  public static func verifyAuthenticationStatus(
    isAuthenticated: Bool,
    user: User?,
    expectedStatus: AuthenticationStatus
  ) -> Bool {
    let actualStatus: AuthenticationStatus = isAuthenticated ? .authenticated : .guest
    
    if actualStatus != expectedStatus {
      print("❌ Authentication status mismatch. Expected: \(expectedStatus), Actual: \(actualStatus)")
      return false
    }
    
    if isAuthenticated && user == nil {
      print("❌ User should not be nil when authenticated")
      return false
    }
    
    if !isAuthenticated && user != nil {
      print("❌ User should be nil when not authenticated")
      return false
    }
    
    print("✅ Authentication status verified successfully")
    return true
  }
  
  /// Print user information for debugging
  public static func printUserInfo(_ user: User?) {
    guard let user = user else {
      print("👤 User: Not logged in")
      return
    }
    
    print("👤 User Information:")
    print("   ID: \(user.id)")
    print("   Email: \(user.email)")
    print("   Display Name: \(user.displayName)")
    print("   Subscription: \(user.subscriptionStatus.displayName)")
    print("   Created: \(user.createdAt)")
  }
}

// MARK: - Mock Data Extensions

extension User {
  /// Create a mock user for testing
  public static func mockUser(
    id: String = "mock-user-123",
    email: String = "<EMAIL>",
    displayName: String = "Test User",
    subscriptionStatus: SubscriptionStatus = .free
  ) -> User {
    return User(
      id: id,
      email: email,
      displayName: displayName,
      avatarURL: nil,
      createdAt: Date(),
      subscriptionStatus: subscriptionStatus
    )
  }
  
  /// Create a mock premium user
  public static var mockPremiumUser: User {
    return mockUser(
      id: "premium-user-456",
      email: "<EMAIL>",
      displayName: "Premium User",
      subscriptionStatus: .premium(expiryDate: Date().addingTimeInterval(365 * 24 * 60 * 60), usageLimits: UserStateCore.UsageLimits.yearlyStandard)
    )
  }
  
  /// Create a mock VIP user (premium with longer expiry)
  public static var mockVIPUser: User {
    return mockUser(
      id: "vip-user-789",
      email: "<EMAIL>",
      displayName: "VIP User",
      subscriptionStatus: .premium(expiryDate: Date().addingTimeInterval(2 * 365 * 24 * 60 * 60), usageLimits: UserStateCore.UsageLimits.yearlyStandard) // 2 years
    )
  }
}

// MARK: - Test Scenarios

public enum LoginTestScenario {
  case appleSignInSuccess
  case emailSignInSuccess
  case logoutSuccess
  case guestMode
  
  public var description: String {
    switch self {
    case .appleSignInSuccess:
      return "Apple Sign In - Success"
    case .emailSignInSuccess:
      return "Email Sign In - Success"
    case .logoutSuccess:
      return "Logout - Success"
    case .guestMode:
      return "Guest Mode - No Authentication"
    }
  }
  
  public func execute() {
    print("\n🧪 Testing: \(description)")

    switch self {
    case .appleSignInSuccess:
      LoginTestHelper.testAppleSignIn()

    case .emailSignInSuccess:
      LoginTestHelper.testEmailSignIn()

    case .logoutSuccess:
      LoginTestHelper.testLogout()

    case .guestMode:
      let isValid = LoginTestHelper.verifyAuthenticationStatus(
        isAuthenticated: false,
        user: nil,
        expectedStatus: .guest
      )
      print(isValid ? "✅ Guest mode test passed" : "❌ Guest mode test failed")
    }
  }
}

// MARK: - Test Runner

public struct LoginTestRunner {
  
  /// Run all login tests
  public static func runAllTests() {
    print("🚀 Starting Login Functionality Tests")
    print("=====================================")

    let scenarios: [LoginTestScenario] = [
      .guestMode,
      .emailSignInSuccess,
      .appleSignInSuccess,
      .logoutSuccess
    ]

    for scenario in scenarios {
      scenario.execute()
      print("") // Add spacing between tests
    }

    print("✅ All login tests completed!")
    print("=====================================")
  }

  /// Run a specific test scenario
  public static func runTest(_ scenario: LoginTestScenario) {
    print("🧪 Running single test: \(scenario.description)")
    scenario.execute()
  }
}
