import SwiftUI
import ComposableArchitecture
import QuotaClient
import UserStateCore
import CommonUI
import ImageGenerationClient

#if canImport(UIKit)
import UIKit
#endif

// MARK: - Quota Exhaustion View

public struct QuotaExhaustedView: View {
  let store: StoreOf<QuotaExhausted>
  
  public init(store: StoreOf<QuotaExhausted>) {
    self.store = store
  }
  
  public var body: some View {
    WithViewStore(store, observe: { $0 }) { viewStore in
      VStack(spacing: 32) {
        // Header
        VStack(spacing: 16) {
          Image(systemName: "exclamationmark.triangle.fill")
            .font(.system(size: 64))
            .foregroundColor(.orange)
          
          Text("生成次数已用完")
            .font(.title2)
            .fontWeight(.bold)
            .multilineTextAlignment(.center)
          
          if let quotaInfo = viewStore.quotaInfo {
            Text("您已使用完 \(quotaInfo.totalQuota) 次生成额度")
              .font(.body)
              .foregroundColor(.secondary)
              .multilineTextAlignment(.center)
          }
        }
        .padding(.horizontal)
        
        // Quota Status Card
        if let quotaInfo = viewStore.quotaInfo {
          QuotaStatusCard(quotaInfo: quotaInfo)
        }
        
        // Suggested Actions
        VStack(spacing: 16) {
          if let suggestedAction = viewStore.suggestedAction {
            switch suggestedAction {
            case .upgrade:
              upgradeButton(viewStore: viewStore)
            case .login:
              loginButton(viewStore: viewStore)
            case .waitForRefresh:
              waitForRefreshView(viewStore: viewStore)
            case .contactSupport:
              contactSupportButton(viewStore: viewStore)
            }
          }
          
          // Secondary actions
          HStack(spacing: 16) {
            Button("查看历史作品") {
              viewStore.send(.viewHistory)
            }
            .buttonStyle(.bordered)
            
            Button("返回首页") {
              viewStore.send(.goToHome)
            }
            .buttonStyle(.bordered)
          }
        }
        .padding(.horizontal)
        
        Spacer()
      }
      .padding()
      .background(backgroundView)
      .onAppear {
        viewStore.send(.onAppear)
      }
    }
  }
  
  // MARK: - Background
  
  private var backgroundView: some View {
    #if canImport(UIKit)
    Color(UIColor.systemGroupedBackground)
    #else
    Color.gray.opacity(0.1)
    #endif
  }
  
  // MARK: - Action Buttons
  
  private func upgradeButton(viewStore: ViewStoreOf<QuotaExhausted>) -> some View {
    Button {
      viewStore.send(.upgradeSubscription)
    } label: {
      HStack {
        Image(systemName: "crown.fill")
        Text("升级到高级版")
        Spacer()
        Text("解锁40次/月")
          .font(.caption)
          .foregroundColor(.white.opacity(0.8))
      }
      .padding()
      .background(
        LinearGradient(
          colors: [.blue, .purple],
          startPoint: .leading,
          endPoint: .trailing
        )
      )
      .foregroundColor(.white)
      .cornerRadius(12)
    }
  }
  
  private func loginButton(viewStore: ViewStoreOf<QuotaExhausted>) -> some View {
    Button {
      viewStore.send(.showLogin)
    } label: {
      HStack {
        Image(systemName: "person.circle.fill")
        Text("登录获取更多次数")
        Spacer()
        Image(systemName: "chevron.right")
      }
      .padding()
      .background(Color.blue)
      .foregroundColor(.white)
      .cornerRadius(12)
    }
  }
  
  private func waitForRefreshView(viewStore: ViewStoreOf<QuotaExhausted>) -> some View {
    VStack(spacing: 12) {
      HStack {
        Image(systemName: "clock.fill")
        Text("配额将在以下时间刷新")
        Spacer()
      }
      
      if let refreshDate = viewStore.quotaInfo?.refreshDate {
        Text(refreshDate, style: .relative)
          .font(.title3)
          .fontWeight(.medium)
          .foregroundColor(.blue)
      }
      
      Button("设置提醒") {
        viewStore.send(.setRefreshReminder)
      }
      .buttonStyle(.bordered)
    }
    .padding()
    .background(systemBackgroundView)
    .cornerRadius(12)
  }
  
  private func contactSupportButton(viewStore: ViewStoreOf<QuotaExhausted>) -> some View {
    Button {
      viewStore.send(.contactSupport)
    } label: {
      HStack {
        Image(systemName: "questionmark.circle.fill")
        Text("联系客服")
        Spacer()
        Image(systemName: "chevron.right")
      }
      .padding()
      .background(Color.gray.opacity(0.2))
      .foregroundColor(.primary)
      .cornerRadius(12)
    }
  }
  
  private var systemBackgroundView: some View {
    #if canImport(UIKit)
    Color(UIColor.systemBackground)
    #else
    Color.white
    #endif
  }
}

// MARK: - Quota Status Card

private struct QuotaStatusCard: View {
  let quotaInfo: QuotaInfo
  
  var body: some View {
    VStack(spacing: 16) {
      HStack {
        VStack(alignment: .leading, spacing: 4) {
          Text("当前配额")
            .font(.caption)
            .foregroundColor(.secondary)
          
          Text("\(quotaInfo.remainingQuota) / \(quotaInfo.totalQuota)")
            .font(.title2)
            .fontWeight(.bold)
        }
        
        Spacer()
        
        VStack(alignment: .trailing, spacing: 4) {
          Text("用户类型")
            .font(.caption)
            .foregroundColor(.secondary)
          
          Text(quotaInfo.userType == .guest ? "游客" : "订阅用户")
            .font(.body)
            .fontWeight(.medium)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(quotaInfo.userType == .guest ? Color.orange.opacity(0.2) : Color.blue.opacity(0.2))
            .cornerRadius(8)
        }
      }
      
      // Progress Bar
      ProgressView(value: quotaInfo.usagePercentage)
        .progressViewStyle(.linear)
        .scaleEffect(y: 2)
        .animation(.easeInOut, value: quotaInfo.usagePercentage)
    }
    .padding()
    .background(systemBackgroundColor)
    .cornerRadius(12)
    .shadow(radius: 2)
  }
  
  private var systemBackgroundColor: some View {
    #if canImport(UIKit)
    Color(UIColor.systemBackground)
    #else
    Color.white
    #endif
  }
}

// MARK: - Quota Exhausted Reducer

@Reducer
public struct QuotaExhausted: Sendable {
  @ObservableState
  public struct State: Equatable, Sendable {
    public var quotaInfo: QuotaInfo?
    public var suggestedAction: SuggestedAction?
    public var isLoading = false
    public var error: String?
    
    public init(
      quotaInfo: QuotaInfo? = nil,
      suggestedAction: SuggestedAction? = nil
    ) {
      self.quotaInfo = quotaInfo
      self.suggestedAction = suggestedAction
    }
  }
  
  public enum Action: Sendable {
    case onAppear
    case upgradeSubscription
    case showLogin
    case setRefreshReminder
    case contactSupport
    case viewHistory
    case goToHome
    case refreshQuota
    case quotaRefreshed(QuotaInfo)
    case quotaRefreshFailed(String)
  }
  
  @Dependency(\.quotaClient) var quotaClient
  
  public init() {}
  
  public var body: some Reducer<State, Action> {
    Reduce { state, action in
      switch action {
      case .onAppear:
        return .send(.refreshQuota)
        
      case .upgradeSubscription:
        // This will be handled by parent to show subscription screen
        return .none
        
      case .showLogin:
        // This will be handled by parent to show login screen
        return .none
        
      case .setRefreshReminder:
        // Set local notification for quota refresh
        if let refreshDate = state.quotaInfo?.refreshDate {
          scheduleQuotaRefreshNotification(for: refreshDate)
        }
        return .none
        
      case .contactSupport:
        // Open support channel (email, chat, etc.)
        openSupportChannel()
        return .none
        
      case .viewHistory:
        // Navigate to image history
        return .none
        
      case .goToHome:
        // Navigate back to home
        return .none
        
      case .refreshQuota:
        state.isLoading = true
        state.error = nil
        
        return .run { send in
          do {
            let quotaInfo = try await self.quotaClient.refreshQuota()
            await send(.quotaRefreshed(quotaInfo))
          } catch {
            await send(.quotaRefreshFailed(error.localizedDescription))
          }
        }
        
      case let .quotaRefreshed(quotaInfo):
        state.isLoading = false
        state.quotaInfo = quotaInfo
        
        // Update suggested action based on quota status
        if quotaInfo.canGenerate {
          // User now has quota, can dismiss this screen
          return .none
        } else {
          state.suggestedAction = quotaInfo.userType == .guest ? .login : .upgrade
        }
        
        return .none
        
      case let .quotaRefreshFailed(error):
        state.isLoading = false
        state.error = error
        return .none
      }
    }
  }
}

// MARK: - Helper Functions

private func scheduleQuotaRefreshNotification(for date: Date) {
  // Implementation for local notification scheduling
  print("📅 Scheduling quota refresh notification for: \(date)")
  // TODO: Implement UNNotificationRequest
}

private func openSupportChannel() {
  // Implementation for opening support
  print("📞 Opening support channel")
  // TODO: Implement support URL or email
}

// MARK: - Preview

#Preview {
  QuotaExhaustedView(
    store: Store(
      initialState: QuotaExhausted.State(
        quotaInfo: QuotaInfo(
          userType: .guest,
          remainingQuota: 0,
          totalQuota: 1,
          quotaPeriod: .oneTime,
          refreshDate: nil,
          deviceId: "preview-device"
        ),
        suggestedAction: .login
      )
    ) {
      QuotaExhausted()
    }
  )
}