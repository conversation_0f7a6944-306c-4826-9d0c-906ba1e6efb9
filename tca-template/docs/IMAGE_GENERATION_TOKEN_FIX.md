# 🎨 图片生成Token修复 - 使用Apple登录的access_token

## 📋 问题分析

根据提供的图片生成日志，发现了严重的认证token问题：

### 错误日志分析
```
🎨 [NewImageGeneration] Live implementation - Request headers: [
  "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ2MjM3MTksInN1YiI6ImU2ODIzYjc2LTBhNGEtNDY3Yy1hZWZiLTA5ZjNlNGU1Nzk2OSJ9.Z7cW-lSP1PpR75CX_9w5siOcxgAZGgE5vaWbLbtUdpQ"
]

🌐 [NewImageGeneration] Live implementation - Received response:
   - Status code: 404
   - Data size: 27 bytes
✅ [NewImageGeneration] Live implementation - Raw response:
{"detail":"User not found"}
```

### 问题根因
1. **多个硬编码Token**: 代码中存在3个不同的硬编码token
2. **Token用户ID不匹配**: 硬编码token的用户ID与Apple登录用户ID不匹配
3. **维护困难**: 多个地方的硬编码token难以维护和更新

### Token对比分析
- **硬编码Token用户ID**: `e6823b76-0a4a-467c-aefb-09f3e4e57969`
- **Apple登录用户ID**: `9c509938-e996-4551-80d2-c074cb4148d4`
- **结果**: 用户ID不匹配，导致"User not found"错误

## 🔧 修复方案

### 1. 移除多个硬编码Token

**修复前 - NewImageGenerationClientLive.swift**:
```swift
private let authToken = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ1NjE4MDAsInN1YiI6ImU2ODIzYjc2LTBhNGEtNDY3Yy1hZWZiLTA5ZjNlNGU1Nzk2OSJ9.Hegoj48qBr1NSDL7OF5JMphpEAwtkLOdn5m-aMihxTk"
```

**修复前 - NewImageGenerationClient.swift (第471行)**:
```swift
let authToken = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ2MjM3MTksInN1YiI6ImU2ODIzYjc2LTBhNGEtNDY3Yy1hZWZiLTA5ZjNlNGU1Nzk2OSJ9.Z7cW-lSP1PpR75CX_9w5siOcxgAZGgE5vaWbLbtUdpQ"
```

**修复前 - NewImageGenerationClient.swift (第610行)**:
```swift
let authToken = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ2MTY4NDIsInN1YiI6ImU2ODIzYjc2LTBhNGEtNDY3Yy1hZWZiLTA5ZjNlNGU1Nzk2OSJ9.zATgYVH6GXSS8mBEOANXT1WkhI-Magts0pUnWGx6ZSs"
```

**修复后**:
```swift
// 添加导入
import AuthenticationClient

// 动态获取认证token
guard let authToken = AccessTokenManager.getAuthorizationHeader() else {
  print("❌ [NewImageGeneration] No valid authentication token found")
  throw NewImageGenerationError.authenticationRequired
}

print("🔐 [NewImageGeneration] Using auth token: \(authToken.prefix(50))...")
```

### 2. 统一认证管理

**核心修复逻辑**:
```swift
// 在每个需要认证的函数开始处
guard let authToken = AccessTokenManager.getAuthorizationHeader() else {
  throw NewImageGenerationError.authenticationRequired
}

// 在请求头中使用动态token
headers: [
  "Authorization": authToken,  // 动态获取的有效token
  "Content-Type": "application/json",
  // ...
]
```

### 3. 更新依赖配置

**Package.swift修改**:
```swift
.target(
  name: "NewImageGenerationClient",
  dependencies: [
    "NetworkClient",
    "AuthenticationClient",  // 新增依赖
    .product(name: "Dependencies", package: "swift-dependencies"),
    // ...
  ]
),
.target(
  name: "NewImageGenerationClientLive", 
  dependencies: [
    "NewImageGenerationClient",
    "NetworkClient",
    "NetworkClientLive",
    "AuthenticationClient",  // 新增依赖
  ]
)
```

## ✅ 修复效果验证

### Token对比
```
🔴 修复前（多个硬编码Token）:
   Token 1: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ2MjM3MTksInN1YiI6ImU2...
   Token 2: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ2MTY4NDIsInN1YiI6ImU2...
   Token 3: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ1NjE4MDAsInN1YiI6ImU2...
   用户ID: e6823b76-0a4a-467c-aefb-09f3e4e57969

🟢 修复后（动态获取有效Token）:
   bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ3MDUzNDAsInN1YiI6Ijlj...
   用户ID: 9c509938-e996-4551-80d2-c074cb4148d4
```

### 请求头对比
```
修复前:
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ2MjM3MTksInN1YiI6ImU2ODIzYjc2LTBhNGEtNDY3Yy1hZWZiLTA5ZjNlNGU1Nzk2OSJ9.Z7cW-lSP1PpR75CX_9w5siOcxgAZGgE5vaWbLbtUdpQ

修复后:
Authorization: bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ3MDUzNDAsInN1YiI6IjljNTA5OTM4LWU5OTYtNDU1MS04MGQyLWMwNzRjYjQxNDhkNCJ9.gNOIbrBxMtS77OKinjQi_FSmzDxTRfuNB9dh-Z5Meh8
```

## 🎯 修复优势

### 1. 统一认证管理
- ✅ 使用Apple登录返回的真实access_token
- ✅ 与AccessTokenManager集成
- ✅ 自动处理token过期和刷新

### 2. 用户匹配正确
- ✅ Token中的用户ID与当前登录用户一致
- ✅ 不再出现"User not found"错误
- ✅ 图片生成请求能正确关联到用户

### 3. 代码维护性
- ✅ 移除所有硬编码token，提高安全性
- ✅ 统一的错误处理机制
- ✅ 更好的日志和调试信息

### 4. 多接口支持
- ✅ 图片生成接口 (`/api/v1/image/generate-image`)
- ✅ 同步生成接口 (`/api/v1/image/generate-image-sync`)
- ✅ 记录查询接口 (`/api/v1/image/record-info/{taskId}`)

## 🔄 完整的修复流程

### 1. Apple登录成功
```json
{
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ3MDUzNDAsInN1YiI6IjljNTA5OTM4LWU5OTYtNDU1MS04MGQyLWMwNzRjYjQxNDhkNCJ9.gNOIbrBxMtS77OKinjQi_FSmzDxTRfuNB9dh-Z5Meh8",
    "token_type": "bearer",
    "user": {
        "id": "9c509938-e996-4551-80d2-c074cb4148d4",
        "email": "<EMAIL>"
    }
}
```

### 2. Token存储
```swift
// 存储到UserDefaults和Keychain
AccessTokenManager.storeTokens(
  accessToken: response.accessToken,
  tokenType: response.tokenType
)
```

### 3. 图片生成使用
```swift
// 动态获取存储的token
let authToken = AccessTokenManager.getAuthorizationHeader()
// 结果: "bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

### 4. 请求成功
```
🔐 [NewImageGeneration] Using auth token: bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
✅ [NewImageGeneration] Generation task submitted!
✅ [NewImageGeneration] Response code: 200
```

## 📁 修改的文件

1. **`Sources/NewImageGenerationClientLive/NewImageGenerationClientLive.swift`**
   - 移除硬编码token
   - 添加AuthenticationClient导入
   - 使用AccessTokenManager动态获取token

2. **`Sources/NewImageGenerationClient/NewImageGenerationClient.swift`**
   - 移除多个硬编码token
   - 添加AuthenticationClient导入
   - 在两个函数中使用AccessTokenManager

3. **`Package.swift`**
   - 为NewImageGenerationClient和NewImageGenerationClientLive添加AuthenticationClient依赖

4. **`test_image_generation_token_fix.swift`**
   - 验证脚本，确保修复正确

## 🚀 预期结果

修复后，图片生成流程将：

1. **自动获取正确Token**: 使用Apple登录后存储的access_token
2. **用户匹配正确**: Token中的用户ID与当前登录用户一致
3. **不再出现404错误**: 服务器能正确识别用户
4. **统一认证管理**: 与整个应用的认证系统保持一致
5. **支持多个接口**: 生成、同步、查询等所有接口都使用正确token

### 成功日志示例
```
🔐 [NewImageGeneration] Using auth token: bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
🎨 [NewImageGeneration] Live implementation - Sending request to: http://127.0.0.1:8000/api/v1/image/generate-image
✅ [NewImageGeneration] Generation task submitted!
✅ [NewImageGeneration] Task ID: abc123
✅ [NewImageGeneration] Status: processing
```

🎉 **图片生成Token修复完成！现在使用Apple登录的真实access_token进行图片生成。**
