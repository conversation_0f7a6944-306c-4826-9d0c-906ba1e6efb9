# 🖼️ 图片上传Token修复 - 使用Apple登录的access_token

## 📋 问题分析

根据提供的图片上传日志，发现了关键问题：

### 错误日志分析
```
🔄 [ImageUpload] Helper - Request headers: [
  "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ1NjE4MDAsInN1YiI6ImU2ODIzYjc2LTBhNGEtNDY3Yy1hZWZiLTA5ZjNlNGU1Nzk2OSJ9.Hegoj48qBr1NSDL7OF5JMphpEAwtkLOdn5m-aMihxTk"
]

🌐 [ImageUpload] Helper - Received response:
   - Status code: 404
   - Data size: 27 bytes
✅ [ImageUpload] Helper - Raw response:
{"detail":"User not found"}
```

### 问题根因
1. **硬编码过期Token**: 图片上传使用了硬编码的token，而不是Apple登录返回的access_token
2. **Token不匹配**: 硬编码token对应的用户ID与当前登录用户不匹配
3. **未集成认证系统**: 图片上传模块没有与统一的认证管理系统集成

### Token对比分析
- **硬编码Token用户ID**: `e6823b76-0a4a-467c-aefb-09f3e4e57969`
- **Apple登录用户ID**: `9c509938-e996-4551-80d2-c074cb4148d4`
- **结果**: 用户ID不匹配，导致"User not found"错误

## 🔧 修复方案

### 1. 移除硬编码Token

**修复前 - ImageUploadClientLive.swift**:
```swift
private let baseURL = "http://127.0.0.1:8000"
private let authToken = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ1NjE4MDAsInN1YiI6ImU2ODIzYjc2LTBhNGEtNDY3Yy1hZWZiLTA5ZjNlNGU1Nzk2OSJ9.Hegoj48qBr1NSDL7OF5JMphpEAwtkLOdn5m-aMihxTk"
```

**修复后**:
```swift
private let baseURL = "http://127.0.0.1:8000"
// 移除硬编码token，改为动态获取
```

### 2. 集成AccessTokenManager

**修复前**:
```swift
headers: [
  "Authorization": authToken,  // 硬编码token
  "Content-Type": "multipart/form-data; boundary=\(boundary)",
  // ...
]
```

**修复后**:
```swift
// 添加导入
import AuthenticationClient

// 动态获取认证token
guard let authToken = AccessTokenManager.getAuthorizationHeader() else {
  print("❌ [ImageUpload] No valid authentication token found")
  throw ImageUploadError.authenticationRequired
}

print("🔐 [ImageUpload] Using auth token: \(authToken.prefix(50))...")

headers: [
  "Authorization": authToken,  // 动态获取的有效token
  "Content-Type": "multipart/form-data; boundary=\(boundary)",
  // ...
]
```

### 3. 更新依赖配置

**Package.swift修改**:
```swift
.target(
  name: "ImageUploadClient",
  dependencies: [
    "NetworkClient",
    "AuthenticationClient",  // 新增依赖
    .product(name: "Dependencies", package: "swift-dependencies"),
    // ...
  ]
),
.target(
  name: "ImageUploadClientLive", 
  dependencies: [
    "ImageUploadClient",
    "NetworkClient",
    "NetworkClientLive",
    "AuthenticationClient",  // 新增依赖
  ]
)
```

## ✅ 修复效果验证

### Token对比
```
🔴 修复前（硬编码过期Token）:
   Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ1NjE4MDAsInN1YiI6ImU2...
   用户ID: e6823b76-0a4a-467c-aefb-09f3e4e57969

🟢 修复后（动态获取有效Token）:
   bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ3MDUzNDAsInN1YiI6Ijlj...
   用户ID: 9c509938-e996-4551-80d2-c074cb4148d4
```

### 请求头对比
```
修复前:
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ1NjE4MDAsInN1YiI6ImU2ODIzYjc2LTBhNGEtNDY3Yy1hZWZiLTA5ZjNlNGU1Nzk2OSJ9.Hegoj48qBr1NSDL7OF5JMphpEAwtkLOdn5m-aMihxTk

修复后:
Authorization: bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ3MDUzNDAsInN1YiI6IjljNTA5OTM4LWU5OTYtNDU1MS04MGQyLWMwNzRjYjQxNDhkNCJ9.gNOIbrBxMtS77OKinjQi_FSmzDxTRfuNB9dh-Z5Meh8
```

## 🎯 修复优势

### 1. 统一认证管理
- ✅ 使用Apple登录返回的真实access_token
- ✅ 与AccessTokenManager集成
- ✅ 自动处理token过期和刷新

### 2. 用户匹配正确
- ✅ Token中的用户ID与当前登录用户一致
- ✅ 不再出现"User not found"错误
- ✅ 图片上传请求能正确关联到用户

### 3. 代码维护性
- ✅ 移除硬编码token，提高安全性
- ✅ 统一的错误处理机制
- ✅ 更好的日志和调试信息

### 4. 错误处理增强
```swift
guard let authToken = AccessTokenManager.getAuthorizationHeader() else {
  print("❌ [ImageUpload] No valid authentication token found")
  throw ImageUploadError.authenticationRequired
}
```

## 🔄 完整的修复流程

### 1. Apple登录成功
```json
{
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ3MDUzNDAsInN1YiI6IjljNTA5OTM4LWU5OTYtNDU1MS04MGQyLWMwNzRjYjQxNDhkNCJ9.gNOIbrBxMtS77OKinjQi_FSmzDxTRfuNB9dh-Z5Meh8",
    "token_type": "bearer",
    "user": {
        "id": "9c509938-e996-4551-80d2-c074cb4148d4",
        "email": "<EMAIL>"
    }
}
```

### 2. Token存储
```swift
// 存储到UserDefaults和Keychain
AccessTokenManager.storeTokens(
  accessToken: response.accessToken,
  tokenType: response.tokenType
)
```

### 3. 图片上传使用
```swift
// 动态获取存储的token
let authToken = AccessTokenManager.getAuthorizationHeader()
// 结果: "bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

### 4. 请求成功
```
🔐 [ImageUpload] Using auth token: bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
✅ [ImageUpload] Upload successful!
```

## 📁 修改的文件

1. **`Sources/ImageUploadClientLive/ImageUploadClientLive.swift`**
   - 移除硬编码token
   - 添加AuthenticationClient导入
   - 使用AccessTokenManager动态获取token

2. **`Sources/ImageUploadClient/ImageUploadClient.swift`**
   - 移除硬编码token
   - 添加AuthenticationClient导入
   - 使用AccessTokenManager动态获取token

3. **`Package.swift`**
   - 为ImageUploadClient和ImageUploadClientLive添加AuthenticationClient依赖

4. **`test_image_upload_token_fix.swift`**
   - 验证脚本，确保修复正确

## 🚀 预期结果

修复后，图片上传流程将：

1. **自动获取正确Token**: 使用Apple登录后存储的access_token
2. **用户匹配正确**: Token中的用户ID与当前登录用户一致
3. **不再出现404错误**: 服务器能正确识别用户
4. **统一认证管理**: 与整个应用的认证系统保持一致

### 成功日志示例
```
🔐 [ImageUpload] Using auth token: bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
🔄 [ImageUpload] Helper - Sending request to: http://127.0.0.1:8000/api/v1/image/upload?file
✅ [ImageUpload] Helper - Upload successful!
✅ [ImageUpload] Helper - Image URL: https://storage.example.com/image_123.jpeg
```

🎉 **图片上传Token修复完成！现在使用Apple登录的真实access_token进行图片上传。**
