# 🍎 Apple ID登录真实邮箱传递功能修复

## 📋 问题分析

根据提供的日志分析，发现Apple ID登录时获取到了真实邮箱 `<EMAIL>`，但当前的实现没有将这个真实邮箱正确传递到 `/apple/login` 接口。

### 日志分析
```
🍎 获取到Apple ID凭证:
   用户ID: 001031.ed50d3aba6e14fa5a08019de106260ae.0506
   邮箱: <EMAIL>
   姓名: test haha
```

### 期望的API请求格式
```json
{
    "identity_token": "eyJraWQiOiJVYUlJRlkyZlc0IiwiYWxnIjoiUlMyNTYifQ...",
    "platform": "ios",
    "user_info": {
        "firstName": "test",
        "lastName": "haha",
        "email": "<EMAIL>"
    },
    "real_email": "<EMAIL>"
}
```

## 🔧 修复方案

### 1. 扩展 AppleOAuthRequest 结构

**修改前:**
```swift
public struct AppleOAuthRequest: Codable, Sendable {
  public let identityToken: String
  public let platform: String
  public let userInfo: UserInfo?
}
```

**修改后:**
```swift
public struct AppleOAuthRequest: Codable, Sendable {
  public let identityToken: String
  public let platform: String
  public let userInfo: UserInfo?
  public let realEmail: String?   // 新增：真实邮箱字段

  private enum CodingKeys: String, CodingKey {
    case identityToken = "identity_token"
    case platform = "platform"
    case userInfo = "user_info"
    case realEmail = "real_email"  // 新增
  }
}
```

### 2. 扩展 UserInfo 结构

**修改前:**
```swift
public struct UserInfo: Codable, Sendable {
  public let firstName: String?
  public let lastName: String?
}
```

**修改后:**
```swift
public struct UserInfo: Codable, Sendable {
  public let firstName: String?
  public let lastName: String?
  public let email: String?  // 新增：邮箱字段
}
```

### 3. 更新请求构建逻辑

**在 `callAppleOAuthAPI` 函数中:**
```swift
// 检查是否有用户信息（首次授权时提供）
var userInfo: UserInfo? = nil
if let fullName = credential.fullName {
  let firstName = fullName.givenName
  let lastName = fullName.familyName

  if (firstName != nil && !firstName!.isEmpty) || (lastName != nil && !lastName!.isEmpty) {
    userInfo = UserInfo(firstName: firstName, lastName: lastName, email: credential.email)
    // 新增：包含邮箱信息
  }
}

// 获取真实邮箱（如果可用）
let realEmail = credential.email

let requestData = AppleOAuthRequest(
  identityToken: identityTokenString,
  platform: "ios",
  userInfo: userInfo,
  realEmail: realEmail  // 新增：传递真实邮箱
)
```

### 4. 增强日志输出

**在 AppleSignInView.swift 中:**
```swift
print("🍎 获取到Apple ID凭证:")
print("   用户ID: \(appleIDCredential.user)")
print("   真实邮箱: \(appleIDCredential.email ?? "未提供")")
print("   姓名: \(appleIDCredential.fullName?.formatted() ?? "未提供")")

// 特别标注真实邮箱信息
if let realEmail = appleIDCredential.email {
  print("✅ 获取到真实邮箱，将传递给后端: \(realEmail)")
} else {
  print("⚠️ 未获取到真实邮箱，用户可能选择了隐藏邮箱")
}
```

## ✅ 验证结果

### 生成的API请求JSON
```json
{
  "user_info" : {
    "firstName" : "test",
    "email" : "<EMAIL>",
    "lastName" : "haha"
  },
  "real_email" : "<EMAIL>",
  "identity_token" : "eyJraWQiOiJVYUlJRlkyZlc0IiwiYWxnIjoiUlMyNTYifQ...",
  "platform" : "ios"
}
```

### 关键字段验证
- ✅ `identity_token`: 包含Apple提供的身份令牌
- ✅ `platform`: 标识为iOS平台
- ✅ `user_info`: 包含用户信息（首次授权时）
  - ✅ `firstName`: 用户名字
  - ✅ `lastName`: 用户姓氏
  - ✅ `email`: 用户邮箱（新增）
- ✅ `real_email`: 客户端获取的真实邮箱（新增）

## 🎯 功能特性

### 1. 智能邮箱处理
- **首次授权**: 同时在 `user_info.email` 和 `real_email` 中传递邮箱
- **后续登录**: 在 `real_email` 中传递邮箱（如果可用）
- **隐私保护**: 支持用户选择隐藏邮箱的情况

### 2. 向后兼容
- 保持原有的 `user_info` 结构
- 新增的 `real_email` 字段为可选
- 不影响现有的登录流程

### 3. 详细日志
- 清晰标识真实邮箱的获取状态
- 区分首次授权和后续登录
- 便于调试和问题排查

## 📁 修改的文件

1. **`Sources/AuthenticationClient/AuthenticationClient.swift`**
   - 扩展 `AppleOAuthRequest` 结构
   - 扩展 `UserInfo` 结构
   - 更新 `callAppleOAuthAPI` 函数
   - 更新 `AppleOAuthAPIClient.loginWithApple` 方法

2. **`Sources/UserStateSwiftUI/AppleSignInView.swift`**
   - 增强日志输出
   - 明确标注真实邮箱状态

3. **`verify_apple_login_fix.swift`**
   - 验证脚本，确保功能正确实现

## 🚀 使用效果

现在Apple ID登录功能可以：

1. **正确传递真实邮箱**: 将用户的真实邮箱传递给后端API
2. **支持多种场景**: 首次授权、后续登录、隐私保护等
3. **提供详细日志**: 便于调试和监控
4. **保持兼容性**: 不影响现有功能

### 实际请求示例
当用户使用邮箱 `<EMAIL>` 进行Apple ID登录时，后端将收到包含以下信息的请求：

- `identity_token`: Apple提供的身份验证令牌
- `real_email`: `<EMAIL>`（客户端获取的真实邮箱）
- `user_info.email`: `<EMAIL>`（首次授权时）
- `user_info.firstName`: `test`
- `user_info.lastName`: `haha`

🎉 **Apple ID登录真实邮箱传递功能修复完成！**
