{"originHash": "fa1e649d0d744f10ed6b7900dafa39701010ece69cee7945cd2c9c087923f54f", "pins": [{"identity": "combine-schedulers", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/combine-schedulers", "state": {"revision": "5928286acce13def418ec36d05a001a9641086f2", "version": "1.0.3"}}, {"identity": "swift-case-paths", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-case-paths", "state": {"revision": "9810c8d6c2914de251e072312f01d3bf80071852", "version": "1.7.1"}}, {"identity": "swift-clocks", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-clocks", "state": {"revision": "cc46202b53476d64e824e0b6612da09d84ffde8e", "version": "1.0.6"}}, {"identity": "swift-collections", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-collections", "state": {"revision": "8c0c0a8b49e080e54e5e328cc552821ff07cd341", "version": "1.2.1"}}, {"identity": "swift-composable-architecture", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-composable-architecture", "state": {"revision": "6574de2396319a58e86e2178577268cb4aeccc30", "version": "1.20.2"}}, {"identity": "swift-concurrency-extras", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-concurrency-extras", "state": {"revision": "82a4ae7170d98d8538ec77238b7eb8e7199ef2e8", "version": "1.3.1"}}, {"identity": "swift-custom-dump", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-custom-dump", "state": {"revision": "82645ec760917961cfa08c9c0c7104a57a0fa4b1", "version": "1.3.3"}}, {"identity": "swift-dependencies", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-dependencies", "state": {"revision": "4c90d6b2b9bf0911af87b103bb40f41771891596", "version": "1.9.2"}}, {"identity": "swift-identified-collections", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-identified-collections", "state": {"revision": "322d9ffeeba85c9f7c4984b39422ec7cc3c56597", "version": "1.1.1"}}, {"identity": "swift-navigation", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-navigation", "state": {"revision": "ae208d1a5cf33aee1d43734ea780a09ada6e2a21", "version": "2.3.2"}}, {"identity": "swift-perception", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-perception", "state": {"revision": "d924c62a70fca5f43872f286dbd7cef0957f1c01", "version": "1.6.0"}}, {"identity": "swift-sharing", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-sharing", "state": {"revision": "75e846ee3159dc75b3a29bfc24b6ce5a557ddca9", "version": "2.5.2"}}, {"identity": "swift-syntax", "kind": "remoteSourceControl", "location": "https://github.com/swiftlang/swift-syntax", "state": {"revision": "f99ae8aa18f0cf0d53481901f88a0991dc3bd4a2", "version": "601.0.1"}}, {"identity": "xctest-dynamic-overlay", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/xctest-dynamic-overlay", "state": {"revision": "23e3442166b5122f73f9e3e622cd1e4bafeab3b7", "version": "1.6.0"}}], "version": 3}