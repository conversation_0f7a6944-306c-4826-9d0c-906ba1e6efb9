#!/bin/bash

# Apple OAuth API 测试脚本
# 用于测试 Apple OAuth 登录接口

echo "🍎 Apple OAuth API 测试脚本"
echo "================================"

# 配置
BASE_URL="http://localhost:8000"
ENDPOINT="/api/v1/oauth/apple/login"
FULL_URL="${BASE_URL}${ENDPOINT}"

# 模拟的 Apple ID Token (仅用于测试)
MOCK_IDENTITY_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.G7e6kK8N2bXvTDYfV7UDFlLN0HE1ekLvUrthLbxZpKk"

echo "📍 测试端点: $FULL_URL"
echo "🔑 使用模拟 Token: ${MOCK_IDENTITY_TOKEN:0:50}..."
echo ""

# 检查服务器是否运行
echo "🔍 检查服务器状态..."
if curl -s --connect-timeout 5 "$BASE_URL" > /dev/null 2>&1; then
    echo "✅ 服务器运行中"
else
    echo "❌ 服务器未运行，请先启动后端服务"
    echo "   提示: 确保后端服务在 $BASE_URL 上运行"
    exit 1
fi

echo ""
echo "🚀 发送 Apple OAuth 请求..."

# 发送请求
RESPONSE=$(curl -s -w "\n%{http_code}" -X POST "$FULL_URL" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d "{
    \"identity_token\": \"$MOCK_IDENTITY_TOKEN\",
    \"platform\": \"ios\"
  }")

# 分离响应体和状态码
HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
RESPONSE_BODY=$(echo "$RESPONSE" | head -n -1)

echo "📊 响应状态码: $HTTP_CODE"
echo "📄 响应内容:"
echo "$RESPONSE_BODY" | jq . 2>/dev/null || echo "$RESPONSE_BODY"

echo ""

# 判断结果
case $HTTP_CODE in
    200)
        echo "✅ 请求成功！Apple OAuth API 正常工作"
        ;;
    404)
        echo "❌ 端点未找到 (404)"
        echo "   检查: 后端是否实现了 $ENDPOINT 端点"
        ;;
    500)
        echo "❌ 服务器内部错误 (500)"
        echo "   检查: 后端服务器日志"
        ;;
    *)
        echo "⚠️  意外的响应状态码: $HTTP_CODE"
        ;;
esac

echo ""
echo "🔧 调试信息:"
echo "   - 如果收到 404，请确保后端实现了 Apple OAuth 端点"
echo "   - 如果收到 500，请检查后端服务器日志"
echo "   - 如果收到其他错误，请检查网络连接和请求格式"
echo ""
echo "📱 iOS 应用集成:"
echo "   - 确保 APIEndpoints.baseURL 指向正确的服务器地址"
echo "   - 在真实设备上测试 Apple ID 登录功能"
echo "   - 检查应用日志中的 Apple OAuth API 调用信息"

echo ""
echo "🎯 测试完成"
