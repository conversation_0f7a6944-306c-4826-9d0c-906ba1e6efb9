#!/bin/bash

# Apple OAuth 最终测试脚本
# 验证所有修复是否正确工作

echo "🎯 Apple OAuth 最终测试脚本"
echo "================================"

# 检查编译状态
echo "1. 检查编译状态..."
if swift build > /dev/null 2>&1; then
    echo "   ✅ 编译成功"
else
    echo "   ❌ 编译失败"
    echo "   请先修复编译错误"
    exit 1
fi

# 检查关键修复
echo ""
echo "2. 检查关键修复..."

# 检查 BridalApp 是否正确配置 networkClient
if grep -q '\$0\.networkClient = \.liveValue' Sources/BridalApp/BridalApp.swift; then
    echo "   ✅ BridalApp 正确配置 networkClient 依赖"
else
    echo "   ❌ BridalApp 未正确配置 networkClient 依赖"
fi

# 检查 AuthenticationClient 是否使用正确的并发模式
if grep -q 'let capturedNetworkClient = networkClient' Sources/AuthenticationClient/AuthenticationClient.swift; then
    echo "   ✅ AuthenticationClient 使用正确的并发模式"
else
    echo "   ❌ AuthenticationClient 并发模式不正确"
fi

# 检查是否有详细的调试日志
if grep -q '🔍 开始执行网络请求' Sources/AuthenticationClient/AuthenticationClient.swift; then
    echo "   ✅ 包含详细的调试日志"
else
    echo "   ❌ 缺少详细的调试日志"
fi

# 检查 API 端点配置
if grep -q 'baseURL.*=.*"http://localhost:8000"' Sources/CommonUI/Constants.swift; then
    echo "   ✅ API 端点配置正确"
else
    echo "   ❌ API 端点配置错误"
fi

echo ""
echo "3. 测试后端 API 连接..."

# 检查后端服务器是否运行
BASE_URL="http://localhost:8000"
if curl -s --connect-timeout 5 "$BASE_URL" > /dev/null 2>&1; then
    echo "   ✅ 后端服务器运行中"
    
    # 测试 Apple OAuth 端点
    ENDPOINT="/api/v1/oauth/apple/login"
    FULL_URL="${BASE_URL}${ENDPOINT}"
    
    echo "   🧪 测试 Apple OAuth 端点..."
    
    # 发送测试请求
    MOCK_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.G7e6kK8N2bXvTDYfV7UDFlLN0HE1ekLvUrthLbxZpKk"
    
    RESPONSE=$(curl -s -w "\n%{http_code}" -X POST "$FULL_URL" \
      -H "Content-Type: application/json" \
      -H "Accept: application/json" \
      -d "{
        \"identity_token\": \"$MOCK_TOKEN\",
        \"platform\": \"ios\"
      }")
    
    HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
    RESPONSE_BODY=$(echo "$RESPONSE" | head -n -1)
    
    case $HTTP_CODE in
        200)
            echo "   ✅ Apple OAuth API 端点正常工作 (HTTP $HTTP_CODE)"
            ;;
        404)
            echo "   ⚠️  Apple OAuth API 端点未找到 (HTTP $HTTP_CODE)"
            echo "      请确保后端实现了 $ENDPOINT 端点"
            ;;
        500)
            echo "   ⚠️  服务器内部错误 (HTTP $HTTP_CODE)"
            echo "      请检查后端服务器日志"
            ;;
        *)
            echo "   ⚠️  意外的响应状态码: $HTTP_CODE"
            ;;
    esac
    
else
    echo "   ⚠️  后端服务器未运行"
    echo "      请启动后端服务器: $BASE_URL"
fi

echo ""
echo "4. 预期的应用日志..."
echo "   当用户进行 Apple ID 登录时，应该看到以下日志："
echo ""
echo "   🔧 配置应用依赖..."
echo "   ✅ NetworkClient 配置完成（Live模式）"
echo "   🔧 AppleOAuthAPIClient: 开始处理 Apple ID 凭证"
echo "      用户ID: [user_id]"
echo "      邮箱: [email_or_未提供]"
echo "   ✅ Identity token 获取成功，长度: [length]"
echo "   🔧 构建网络请求..."
echo "      Base URL: http://localhost:8000"
echo "      Path: /api/v1/oauth/apple/login"
echo "      Full URL: http://localhost:8000/api/v1/oauth/apple/login"
echo "   ✅ 网络请求构建成功"
echo "   🍎 开始调用 Apple OAuth API"
echo "   🔍 NetworkClient 类型: NetworkClient"
echo "   🍎 Apple OAuth API 调用已启动（异步）"
echo "   🔍 开始执行网络请求..."
echo "   ✅ 网络请求成功，响应数据长度: [length]"
echo "   ✅ JSON 解析成功"
echo "   ✅ Apple OAuth API 调用成功"

echo ""
echo "5. 故障排除指南..."
echo ""
echo "   如果仍然看到 'no live implementation' 错误："
echo "   • 确保 BridalApp.swift 中有 '\$0.networkClient = .liveValue'"
echo "   • 确保导入了 'import NetworkClientLive'"
echo "   • 重新编译并重启应用"
echo ""
echo "   如果网络请求失败："
echo "   • 检查后端服务器是否运行在 http://localhost:8000"
echo "   • 检查防火墙设置"
echo "   • 查看后端服务器日志"
echo ""
echo "   如果 JSON 解析失败："
echo "   • 检查后端返回的响应格式"
echo "   • 确保响应包含 'success', 'message', 'data' 字段"

echo ""
echo "6. 测试总结..."

# 计算成功的检查项
SUCCESS_COUNT=0
TOTAL_COUNT=4

if swift build > /dev/null 2>&1; then ((SUCCESS_COUNT++)); fi
if grep -q '\$0\.networkClient = \.liveValue' Sources/BridalApp/BridalApp.swift; then ((SUCCESS_COUNT++)); fi
if grep -q 'let capturedNetworkClient = networkClient' Sources/AuthenticationClient/AuthenticationClient.swift; then ((SUCCESS_COUNT++)); fi
if grep -q '🔍 开始执行网络请求' Sources/AuthenticationClient/AuthenticationClient.swift; then ((SUCCESS_COUNT++)); fi

echo "   代码检查结果: $SUCCESS_COUNT/$TOTAL_COUNT 项通过"

if [ $SUCCESS_COUNT -eq $TOTAL_COUNT ]; then
    echo ""
    echo "🎉 所有检查通过！Apple OAuth 功能已完全修复"
    echo ""
    echo "📱 现在可以："
    echo "   1. 在真实设备上测试 Apple ID 登录"
    echo "   2. 查看应用日志确认 API 调用成功"
    echo "   3. 验证后端数据库中的用户数据"
    echo ""
    echo "🔧 如需进一步测试后端 API："
    echo "   ./scripts/test_apple_oauth.sh"
else
    echo ""
    echo "⚠️  发现问题，请检查失败的项目并修复"
fi

echo ""
echo "🎯 测试完成"
