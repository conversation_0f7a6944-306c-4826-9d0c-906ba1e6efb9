#!/bin/bash

# Apple OAuth 修复验证脚本
# 用于验证 Apple OAuth 功能是否正确修复

echo "🔍 Apple OAuth 修复验证脚本"
echo "================================"

# 检查编译状态
echo "1. 检查编译状态..."
if swift build > /dev/null 2>&1; then
    echo "   ✅ 编译成功"
else
    echo "   ❌ 编译失败"
    echo "   请先修复编译错误"
    exit 1
fi

# 检查关键文件是否存在
echo ""
echo "2. 检查关键文件..."

FILES=(
    "Sources/AuthenticationClient/AuthenticationClient.swift"
    "Sources/LoginCore/LoginCore.swift"
    "Sources/LoginSwiftUI/LoginView.swift"
    "Sources/NetworkClientLive/LiveNetworkClient.swift"
    "Sources/BridalApp/BridalApp.swift"
    "Sources/CommonUI/Constants.swift"
)

for file in "${FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "   ✅ $file"
    else
        echo "   ❌ $file (缺失)"
    fi
done

# 检查关键代码片段
echo ""
echo "3. 检查关键代码修复..."

# 检查 LoginView 是否调用正确的 action
if grep -q "store.send(.appleSignIn(credential))" Sources/LoginSwiftUI/LoginView.swift; then
    echo "   ✅ LoginView 正确调用 appleSignIn action"
else
    echo "   ❌ LoginView 未正确调用 appleSignIn action"
fi

# 检查 LoginCore 是否有 appleSignIn action
if grep -q "case appleSignIn(AppleIDCredential)" Sources/LoginCore/LoginCore.swift; then
    echo "   ✅ LoginCore 包含 appleSignIn action"
else
    echo "   ❌ LoginCore 缺少 appleSignIn action"
fi

# 检查 AuthenticationClient 是否有 Apple OAuth 实现
if grep -q "AppleOAuthAPIClient" Sources/AuthenticationClient/AuthenticationClient.swift; then
    echo "   ✅ AuthenticationClient 包含 Apple OAuth 实现"
else
    echo "   ❌ AuthenticationClient 缺少 Apple OAuth 实现"
fi

# 检查 BridalApp 是否导入 NetworkClientLive
if grep -q "import NetworkClientLive" Sources/BridalApp/BridalApp.swift; then
    echo "   ✅ BridalApp 正确导入 NetworkClientLive"
else
    echo "   ❌ BridalApp 未导入 NetworkClientLive"
fi

# 检查 Package.swift 是否包含 NetworkClientLive 依赖
if grep -A 10 "BridalApp" Package.swift | grep -q "NetworkClientLive"; then
    echo "   ✅ Package.swift 包含 NetworkClientLive 依赖"
else
    echo "   ❌ Package.swift 缺少 NetworkClientLive 依赖"
fi

# 检查 API 端点配置
if grep -q "appleOAuth.*=.*\"/api/v1/oauth/apple/login\"" Sources/CommonUI/Constants.swift; then
    echo "   ✅ API 端点配置正确"
else
    echo "   ❌ API 端点配置错误"
fi

echo ""
echo "4. 检查网络客户端实现..."

# 检查 NetworkClient 是否有 live 实现
if grep -q "extension NetworkClient: DependencyKey" Sources/NetworkClientLive/LiveNetworkClient.swift; then
    echo "   ✅ NetworkClient 有 live 实现"
else
    echo "   ❌ NetworkClient 缺少 live 实现"
fi

echo ""
echo "5. 验证总结..."

# 计算成功的检查项
SUCCESS_COUNT=0
TOTAL_COUNT=7

# 重新检查所有项目
if swift build > /dev/null 2>&1; then ((SUCCESS_COUNT++)); fi
if grep -q "store.send(.appleSignIn(credential))" Sources/LoginSwiftUI/LoginView.swift; then ((SUCCESS_COUNT++)); fi
if grep -q "case appleSignIn(AppleIDCredential)" Sources/LoginCore/LoginCore.swift; then ((SUCCESS_COUNT++)); fi
if grep -q "AppleOAuthAPIClient" Sources/AuthenticationClient/AuthenticationClient.swift; then ((SUCCESS_COUNT++)); fi
if grep -q "import NetworkClientLive" Sources/BridalApp/BridalApp.swift; then ((SUCCESS_COUNT++)); fi
if grep -A 10 "BridalApp" Package.swift | grep -q "NetworkClientLive"; then ((SUCCESS_COUNT++)); fi
if grep -q "extension NetworkClient: DependencyKey" Sources/NetworkClientLive/LiveNetworkClient.swift; then ((SUCCESS_COUNT++)); fi

echo "   检查结果: $SUCCESS_COUNT/$TOTAL_COUNT 项通过"

if [ $SUCCESS_COUNT -eq $TOTAL_COUNT ]; then
    echo ""
    echo "🎉 所有检查通过！Apple OAuth 修复成功"
    echo ""
    echo "📱 下一步："
    echo "   1. 在真实设备上测试 Apple ID 登录"
    echo "   2. 检查应用日志确认 API 调用"
    echo "   3. 验证后端接收到请求"
    echo ""
    echo "🔧 测试后端 API："
    echo "   ./scripts/test_apple_oauth.sh"
else
    echo ""
    echo "⚠️  发现问题，请检查失败的项目并修复"
    echo ""
    echo "📚 参考文档："
    echo "   - tca-template/APPLE_OAUTH_SUMMARY.md"
    echo "   - tca-template/docs/APPLE_OAUTH_IMPLEMENTATION.md"
fi

echo ""
echo "🔍 验证完成"
