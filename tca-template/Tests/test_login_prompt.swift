#!/usr/bin/env swift

import Foundation
import ComposableArchitecture
import AppCore
import UserStateCore

// 简单的测试脚本来验证登录提示功能

print("🧪 测试登录提示功能")
print("==================")

// 创建测试store
let store = TestStore(initialState: AppFeature.State.main(MainTab.State())) {
  AppFeature()
}

print("\n1️⃣ 测试初始状态")
print("isShowingLoginPrompt: \(store.state.main?.isShowingLoginPrompt ?? false)")

print("\n2️⃣ 发送 promptLogin 动作")
store.send(.main(.userState(.promptLogin))) { state in
  if case .main(var mainState) = state {
    mainState.isShowingLoginPrompt = true
    state = .main(mainState)
  }
}

print("测试完成后 isShowingLoginPrompt: \(store.state.main?.isShowingLoginPrompt ?? false)")

print("\n✅ 测试完成！")
print("如果上面显示 isShowingLoginPrompt: true，说明修复成功")
