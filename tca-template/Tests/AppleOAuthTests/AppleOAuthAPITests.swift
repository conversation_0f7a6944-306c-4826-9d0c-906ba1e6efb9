import XCTest
import AuthenticationClient
import Foundation

final class AppleOAuthAPITests: XCTestCase {

  func testAppleOAuthRequestSerialization() throws {
    let request = AppleOAuthRequest(
      identityToken: "test_token_123",
      platform: "ios"
    )

    let jsonData = try JSONEncoder().encode(request)
    let jsonString = String(data: jsonData, encoding: .utf8)!

    XCTAssertTrue(jsonString.contains("\"identity_token\":\"test_token_123\""))
    XCTAssertTrue(jsonString.contains("\"platform\":\"ios\""))
  }

  
  func testAppleOAuthResponseDeserialization() throws {
    let jsonString = """
    {
      "success": true,
      "message": "登录成功",
      "data": {
        "userId": "test_user_123",
        "email": "<EMAIL>",
        "name": "Test User",
        "token": "test_token_123"
      }
    }
    """
    
    let jsonData = jsonString.data(using: .utf8)!
    let response = try JSONDecoder().decode(AppleOAuthResponse.self, from: jsonData)
    
    XCTAssertTrue(response.success)
    XCTAssertEqual(response.message, "登录成功")
    XCTAssertEqual(response.data?.userId, "test_user_123")
    XCTAssertEqual(response.data?.email, "<EMAIL>")
    XCTAssertEqual(response.data?.name, "Test User")
    XCTAssertEqual(response.data?.token, "test_token_123")
  }
}
