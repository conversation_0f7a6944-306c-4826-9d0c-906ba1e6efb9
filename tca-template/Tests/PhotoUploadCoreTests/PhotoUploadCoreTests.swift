import ComposableArchitecture
import PhotoU<PERSON>loadCore
import XCTest

#if canImport(UIKit)
import UIKit
#endif

@MainActor
final class PhotoUploadCoreTests: XCTestCase {
  
  func testOnAppear() async {
    let store = TestStore(initialState: PhotoUpload.State()) {
      PhotoUpload()
    } withDependencies: {
      $0.photoLibraryClient.checkPermission = { true }
    }
    
    await store.send(.onAppear)
  }
  
  func testSelectFromPhotoLibrary() async {
    let store = TestStore(initialState: PhotoUpload.State()) {
      PhotoUpload()
    }
    
    await store.send(.selectFromPhotoLibrary) {
      $0.sourceType = .photoLibrary
      $0.isShowingImagePicker = true
    }
  }
  
  func testPhotosSelected() async {
    let mockPhoto = PhotoItem(image: UIImage(systemName: "photo")!)
    let store = TestStore(initialState: PhotoUpload.State()) {
      PhotoUpload()
    }
    
    await store.send(.photosSelected([mockPhoto])) {
      $0.selectedImages = [mockPhoto]
      $0.isShowingImagePicker = false
      $0.isShowingCamera = false
    }
  }
  
  func testRemovePhoto() async {
    let mockPhoto = PhotoItem(image: UIImage(systemName: "photo")!)
    let store = TestStore(
      initialState: PhotoUpload.State(selectedImages: [mockPhoto])
    ) {
      PhotoUpload()
    }
    
    await store.send(.removePhoto(mockPhoto)) {
      $0.selectedImages = []
    }
  }
  
  func testMaxSelectionLimit() async {
    let mockPhotos = (0..<6).map { _ in PhotoItem(image: UIImage(systemName: "photo")!) }
    let store = TestStore(initialState: PhotoUpload.State()) {
      PhotoUpload()
    }
    
    await store.send(.photosSelected(mockPhotos)) {
      $0.selectedImages = Array(mockPhotos.prefix(5)) // Should only add 5 photos
      $0.isShowingImagePicker = false
      $0.isShowingCamera = false
    }
  }
  
  func testClearAllPhotos() async {
    let mockPhoto = PhotoItem(image: UIImage(systemName: "photo")!)
    let store = TestStore(
      initialState: PhotoUpload.State(selectedImages: [mockPhoto])
    ) {
      PhotoUpload()
    }
    
    await store.send(PhotoUpload.Action.clearAllPhotos) {
      $0.selectedImages = []
    }
  }
  
  func testErrorHandling() async {
    let store = TestStore(initialState: PhotoUpload.State()) {
      PhotoUpload()
    } withDependencies: {
      $0.photoLibraryClient.checkPermission = { false }
    }
    
    await store.send(.onAppear)
    await store.receive(.errorOccurred(.permissionDenied)) {
      $0.error = .permissionDenied
    }
  }
  
  func testDismissError() async {
    let store = TestStore(
      initialState: PhotoUpload.State(error: .permissionDenied)
    ) {
      PhotoUpload()
    }

    await store.send(.dismissError) {
      $0.error = nil
    }
  }

  func testProceedToStyleSelectionWithoutPhotos() async {
    let store = TestStore(initialState: PhotoUpload.State()) {
      PhotoUpload()
    }

    await store.send(.proceedToStyleSelection) {
      $0.error = .imageProcessingFailed
    }
  }

  func testProceedToStyleSelectionWithPhotos() async {
    let mockPhoto = PhotoItem(image: UIImage(systemName: "photo")!)
    let store = TestStore(
      initialState: PhotoUpload.State(selectedImages: [mockPhoto])
    ) {
      PhotoUpload()
    }

    await store.send(.proceedToStyleSelection)
    // No state change expected as this is handled by parent reducer
  }

  func testMaxCapacityHandling() async {
    let existingPhotos = (0..<5).map { _ in PhotoItem(image: UIImage(systemName: "photo")!) }
    let newPhoto = PhotoItem(image: UIImage(systemName: "photo.fill")!)

    let store = TestStore(
      initialState: PhotoUpload.State(selectedImages: existingPhotos)
    ) {
      PhotoUpload()
    }

    await store.send(PhotoUpload.Action.photosSelected([newPhoto])) {
      $0.error = PhotoUploadError.maxSelectionReached
      $0.isShowingImagePicker = false
      $0.isShowingCamera = false
    }
  }
}
