import XCTest
import ComposableArchitecture
@testable import ImageTypeSelectionCore
@testable import SubscriptionCore
@testable import MainTabCore
@testable import UserStateCore

// MARK: - Subscription Flow Tests

@MainActor
final class SubscriptionFlowTests: XCTestCase {
  
  func testPremiumTemplateShowsSubscription() async {
    // 创建一个免费用户状态
    let store = TestStore(
      initialState: ImageTypeSelection.State(
        userSubscriptionStatus: .free
      )
    ) {
      ImageTypeSelection()
    }
    
    // 创建一个付费模板
    let premiumTemplate = ImageTemplate(
      id: "premium_test",
      name: "Premium Wedding",
      description: "Luxury wedding style",
      category: .wedding,
      isPremium: true
    )
    
    // 模拟选择付费模板
    await store.send(.templateSelected(premiumTemplate)) {
      $0.selectedTemplate = premiumTemplate
    }
    
    // 验证显示订阅页面的动作被发送
    await store.receive(.showSubscriptionForTemplate(premiumTemplate))
  }
  
  func testFreeTemplateDirectlyProceeds() async {
    let store = TestStore(
      initialState: ImageTypeSelection.State(
        userSubscriptionStatus: .free
      )
    ) {
      ImageTypeSelection()
    }
    
    // 创建一个免费模板
    let freeTemplate = ImageTemplate(
      id: "free_test",
      name: "Basic Wedding",
      description: "Simple wedding style",
      category: .wedding,
      isPremium: false
    )
    
    // 模拟选择免费模板
    await store.send(.templateSelected(freeTemplate)) {
      $0.selectedTemplate = freeTemplate
    }
    
    // 验证直接进入生成流程
    await store.receive(.proceedToGeneration(freeTemplate))
  }
  
  func testPremiumUserCanAccessPremiumTemplate() async {
    // 创建一个高级用户状态
    let premiumExpiryDate = Date().addingTimeInterval(365 * 24 * 60 * 60) // 1年后过期
    let store = TestStore(
      initialState: ImageTypeSelection.State(
        userSubscriptionStatus: .premium(expiryDate: premiumExpiryDate)
      )
    ) {
      ImageTypeSelection()
    }
    
    // 创建一个付费模板
    let premiumTemplate = ImageTemplate(
      id: "premium_test",
      name: "Premium Wedding",
      description: "Luxury wedding style",
      category: .wedding,
      isPremium: true
    )
    
    // 模拟高级用户选择付费模板
    await store.send(.templateSelected(premiumTemplate)) {
      $0.selectedTemplate = premiumTemplate
    }
    
    // 验证直接进入生成流程（不显示订阅页面）
    await store.receive(.proceedToGeneration(premiumTemplate))
  }
  
  func testSubscriptionStatusCheck() {
    // 测试免费用户状态
    let freeStatus = SubscriptionStatus.free
    XCTAssertFalse(freeStatus.isPremiumActive)
    
    // 测试有效的高级用户状态
    let validPremiumStatus = SubscriptionStatus.premium(expiryDate: Date().addingTimeInterval(3600))
    XCTAssertTrue(validPremiumStatus.isPremiumActive)
    
    // 测试过期的高级用户状态
    let expiredPremiumStatus = SubscriptionStatus.premium(expiryDate: Date().addingTimeInterval(-3600))
    XCTAssertFalse(expiredPremiumStatus.isPremiumActive)
    
    // 测试已过期状态
    let expiredStatus = SubscriptionStatus.expired
    XCTAssertFalse(expiredStatus.isPremiumActive)
  }
  
  func testMainTabSubscriptionFlow() async {
    let store = TestStore(
      initialState: MainTab.State()
    ) {
      MainTab()
    }
    
    // 模拟从图片类型选择页面触发订阅
    let premiumTemplate = ImageTemplate(
      id: "premium_test",
      name: "Premium Wedding",
      description: "Luxury wedding style",
      category: .wedding,
      isPremium: true
    )
    
    await store.send(.homeFlow(.imageTypeSelection(.showSubscriptionForTemplate(premiumTemplate))))
    
    // 验证订阅页面被显示
    await store.receive(.showSubscription(premiumTemplate.id)) {
      $0.subscription = Subscription.State(selectedTemplate: premiumTemplate.id)
      $0.isShowingSubscription = true
    }
  }
}

// MARK: - Mock Data Extensions

extension ImageTemplate {
  static let mockFreeTemplate = ImageTemplate(
    id: "mock_free",
    name: "Free Template",
    description: "A free template for testing",
    category: .wedding,
    isPremium: false
  )
  
  static let mockPremiumTemplate = ImageTemplate(
    id: "mock_premium",
    name: "Premium Template",
    description: "A premium template for testing",
    category: .wedding,
    isPremium: true
  )
}
