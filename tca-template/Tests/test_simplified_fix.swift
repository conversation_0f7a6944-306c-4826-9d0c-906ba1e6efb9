#!/usr/bin/env swift

import Foundation

// 测试简化的 Apple OAuth API 调用
func testSimplifiedAppleOAuthAPI() async {
    print("🧪 开始测试简化的 Apple OAuth API 调用...")
    
    // 创建符合 Apple JWT 格式的 identity_token
    func createAppleJWTToken() -> String {
        // JWT Header
        let header = [
            "alg": "HS256",
            "typ": "JWT"
        ]
        
        // JWT Payload - 符合 Apple Identity Token 格式
        let currentTime = Int(Date().timeIntervalSince1970)
        let payload = [
            "iss": "https://appleid.apple.com",
            "aud": "com.yourapp.service",
            "exp": currentTime + 600, // 10分钟后过期
            "iat": currentTime,
            "sub": "001031.ed50d3aba6e14fa5a08019de106260ae.0506",
            "email": "<EMAIL>",
            "email_verified": true,
            "is_private_email": true,
            "real_user_status": 2
        ] as [String : Any]
        
        // 简单的 Base64 编码（测试环境不验证签名）
        let headerData = try! JSONSerialization.data(withJSONObject: header)
        let payloadData = try! JSONSerialization.data(withJSONObject: payload)
        
        let headerBase64 = headerData.base64EncodedString()
            .replacingOccurrences(of: "+", with: "-")
            .replacingOccurrences(of: "/", with: "_")
            .replacingOccurrences(of: "=", with: "")
        
        let payloadBase64 = payloadData.base64EncodedString()
            .replacingOccurrences(of: "+", with: "-")
            .replacingOccurrences(of: "/", with: "_")
            .replacingOccurrences(of: "=", with: "")
        
        // 简单的签名（测试环境）
        let signature = "test-signature-for-development"
        let signatureBase64 = signature.data(using: .utf8)!.base64EncodedString()
            .replacingOccurrences(of: "+", with: "-")
            .replacingOccurrences(of: "/", with: "_")
            .replacingOccurrences(of: "=", with: "")
        
        return "\(headerBase64).\(payloadBase64).\(signatureBase64)"
    }
    
    // 请求数据结构
    struct AppleOAuthRequest: Codable {
        let identityToken: String
        let platform: String
        
        private enum CodingKeys: String, CodingKey {
            case identityToken = "identity_token"
            case platform = "platform"
        }
    }
    
    do {
        // 创建 JWT Token
        let jwtToken = createAppleJWTToken()
        print("🔧 生成的 JWT Token: \(jwtToken)")
        print("📏 Token 长度: \(jwtToken.count)")
        
        let requestData = AppleOAuthRequest(
            identityToken: jwtToken,
            platform: "ios"
        )
        
        // 构建 URL
        let baseURL = "http://localhost:8000"
        let path = "/api/v1/oauth/apple/login"
        let fullURL = "\(baseURL)\(path)"
        
        print("🔧 构建网络请求...")
        print("   Base URL: \(baseURL)")
        print("   Path: \(path)")
        print("   Full URL: \(fullURL)")
        
        guard let url = URL(string: fullURL) else {
            print("❌ URL 构建失败")
            return
        }
        
        // 构建请求
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("*/*", forHTTPHeaderField: "Accept")
        request.setValue("localhost:8000", forHTTPHeaderField: "Host")
        request.setValue("keep-alive", forHTTPHeaderField: "Connection")
        
        // 序列化请求数据
        let jsonData = try JSONEncoder().encode(requestData)
        request.httpBody = jsonData
        
        // 打印请求详情
        print("✅ 网络请求构建成功")
        print("🔍 请求详情:")
        print("   URL: \(request.url?.absoluteString ?? "无")")
        print("   Method: \(request.httpMethod ?? "无")")
        print("   Headers: \(request.allHTTPHeaderFields ?? [:])")
        if let bodyData = request.httpBody,
           let bodyString = String(data: bodyData, encoding: .utf8) {
            print("   Body: \(bodyString)")
        }
        
        print("🍎 开始调用 Apple OAuth API")
        
        // 执行网络请求
        let (data, response) = try await URLSession.shared.data(for: request)
        
        print("✅ 网络请求成功")
        print("   响应数据长度: \(data.count)")
        
        if let httpResponse = response as? HTTPURLResponse {
            print("   HTTP 状态码: \(httpResponse.statusCode)")
            print("   响应头: \(httpResponse.allHeaderFields)")
        }
        
        if let responseString = String(data: data, encoding: .utf8) {
            print("   响应内容: \(responseString)")
        }

        // 尝试解析响应（使用新的响应格式）
        struct TestAppleOAuthResponse: Codable {
            let accessToken: String
            let tokenType: String
            let user: TestAppleOAuthUser
            let isNewUser: Bool

            private enum CodingKeys: String, CodingKey {
                case accessToken = "access_token"
                case tokenType = "token_type"
                case user = "user"
                case isNewUser = "is_new_user"
            }
        }

        struct TestAppleOAuthUser: Codable {
            let id: String
            let email: String
            let fullName: String
            let authProvider: String
            let providerUserId: String

            private enum CodingKeys: String, CodingKey {
                case id = "id"
                case email = "email"
                case fullName = "full_name"
                case authProvider = "auth_provider"
                case providerUserId = "provider_user_id"
            }
        }

        do {
            let apiResponse = try JSONDecoder().decode(TestAppleOAuthResponse.self, from: data)
            print("✅ JSON 解析成功")
            print("   Access Token: \(apiResponse.accessToken)")
            print("   Token Type: \(apiResponse.tokenType)")
            print("   用户 ID: \(apiResponse.user.id)")
            print("   用户邮箱: \(apiResponse.user.email)")
            print("   用户姓名: \(apiResponse.user.fullName)")
            print("   是否新用户: \(apiResponse.isNewUser)")

            // 模拟存储 access_token
            print("✅ Access Token 可以正常存储和使用")
        } catch {
            print("❌ JSON 解析失败: \(error)")
        }

        print("✅ 测试完成！")
        
    } catch {
        print("❌ 测试失败: \(error.localizedDescription)")
        print("   错误类型: \(type(of: error))")
    }
}

// 运行测试
Task {
    await testSimplifiedAppleOAuthAPI()
    print("🏁 所有测试完成")
    exit(0)
}

// 保持脚本运行
RunLoop.main.run()
