import ComposableArchitecture
import StyleSelectionCore
import XCTest

@MainActor
final class StyleSelectionCoreTests: XCTestCase {
  
  func testOnAppear() async {
    let store = TestStore(initialState: StyleSelection.State()) {
      StyleSelection()
    } withDependencies: {
      $0.styleClient.loadStyles = { WeddingStyle.mockStyles }
    }
    
    await store.send(.onAppear) {
      $0.isLoading = true
    }
    
    await store.receive(.binding(.set(\.availableStyles, WeddingStyle.mockStyles))) {
      $0.availableStyles = WeddingStyle.mockStyles
    }
    
    await store.receive(.binding(.set(\.isLoading, false))) {
      $0.isLoading = false
    }
  }
  
  func testStyleSelection() async {
    let mockStyle = WeddingStyle.mockStyles.first!
    let store = TestStore(initialState: StyleSelection.State()) {
      StyleSelection()
    }
    
    await store.send(.styleSelected(mockStyle)) {
      $0.selectedStyle = mockStyle
      $0.isShowingCustomPrompt = false
      $0.customPrompt = ""
    }
  }
  
  func testCustomStyleSelection() async {
    let customStyle = WeddingStyle.mockStyles.first { $0.isCustom }!
    let store = TestStore(initialState: StyleSelection.State()) {
      StyleSelection()
    }
    
    await store.send(.styleSelected(customStyle)) {
      $0.selectedStyle = customStyle
      $0.isShowingCustomPrompt = true
    }
  }
  
  func testCustomPromptHandling() async {
    let store = TestStore(initialState: StyleSelection.State()) {
      StyleSelection()
    }
    
    await store.send(.showCustomPrompt) {
      $0.isShowingCustomPrompt = true
    }
    
    await store.send(.customPromptChanged("海边日落婚纱照")) {
      $0.customPrompt = "海边日落婚纱照"
    }
    
    await store.send(.hideCustomPrompt) {
      $0.isShowingCustomPrompt = false
    }
  }
  
  func testLoadMoreStyles() async {
    let additionalStyles = [
      WeddingStyle(
        name: "测试风格",
        description: "测试描述",
        previewImageName: "test_style",
        prompt: "test prompt"
      )
    ]
    
    let store = TestStore(initialState: StyleSelection.State()) {
      StyleSelection()
    } withDependencies: {
      $0.styleClient.loadMoreStyles = { additionalStyles }
    }
    
    await store.send(.loadMoreStyles) {
      $0.isLoading = true
    }
    
    await store.receive(.binding(.set(\.availableStyles, additionalStyles))) {
      $0.availableStyles = additionalStyles
    }
    
    await store.receive(.binding(.set(\.isLoading, false))) {
      $0.isLoading = false
    }
  }
  
  func testErrorHandling() async {
    let store = TestStore(initialState: StyleSelection.State()) {
      StyleSelection()
    } withDependencies: {
      $0.styleClient.loadStyles = {
        throw StyleSelectionError.networkError("Network failed")
      }
    }
    
    await store.send(.onAppear) {
      $0.isLoading = true
    }
    
    await store.receive(.errorOccurred(.loadingFailed("Network failed"))) {
      $0.error = .loadingFailed("Network failed")
      $0.isLoading = false
    }
  }
  
  func testDismissError() async {
    let store = TestStore(
      initialState: StyleSelection.State(error: .networkError("Test error"))
    ) {
      StyleSelection()
    }
    
    await store.send(.dismissError) {
      $0.error = nil
    }
  }
  
  func testCanProceed() {
    let stateWithoutSelection = StyleSelection.State()
    XCTAssertFalse(stateWithoutSelection.canProceed)
    
    let stateWithSelection = StyleSelection.State(
      selectedStyle: WeddingStyle.mockStyles.first
    )
    XCTAssertTrue(stateWithSelection.canProceed)
  }
  
  func testIsCustomStyle() {
    let customStyle = WeddingStyle.mockStyles.first { $0.isCustom }!
    let regularStyle = WeddingStyle.mockStyles.first { !$0.isCustom }!
    
    let stateWithCustomStyle = StyleSelection.State(selectedStyle: customStyle)
    XCTAssertTrue(stateWithCustomStyle.isCustomStyle)
    
    let stateWithRegularStyle = StyleSelection.State(selectedStyle: regularStyle)
    XCTAssertFalse(stateWithRegularStyle.isCustomStyle)
  }
}
