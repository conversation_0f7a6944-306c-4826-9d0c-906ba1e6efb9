#!/usr/bin/env swift

import Foundation
import AuthenticationClient
import NetworkClient
import NetworkClientLive
import ComposableArchitecture

// 简单的测试脚本来验证 NetworkClient 依赖注入修复

print("🧪 开始测试 NetworkClient 依赖注入修复...")

// 创建符合 Apple JWT 格式的 identity_token
func createAppleJWTToken() -> String {
    // JWT Header
    let header = [
        "alg": "HS256",
        "typ": "JWT"
    ]

    // JWT Payload - 符合 Apple Identity Token 格式
    let currentTime = Int(Date().timeIntervalSince1970)
    let payload = [
        "iss": "https://appleid.apple.com",
        "aud": "com.yourapp.service",
        "exp": currentTime + 600, // 10分钟后过期
        "iat": currentTime,
        "sub": "001031.ed50d3aba6e14fa5a08019de106260ae.0506",
        "email": "<EMAIL>",
        "email_verified": true,
        "is_private_email": true,
        "real_user_status": 2
    ] as [String : Any]

    // 简单的 Base64 编码（测试环境不验证签名）
    let headerData = try! JSONSerialization.data(withJSONObject: header)
    let payloadData = try! JSONSerialization.data(withJSONObject: payload)

    let headerBase64 = headerData.base64EncodedString()
        .replacingOccurrences(of: "+", with: "-")
        .replacingOccurrences(of: "/", with: "_")
        .replacingOccurrences(of: "=", with: "")

    let payloadBase64 = payloadData.base64EncodedString()
        .replacingOccurrences(of: "+", with: "-")
        .replacingOccurrences(of: "/", with: "_")
        .replacingOccurrences(of: "=", with: "")

    // 简单的签名（测试环境）
    let signature = "test-signature-for-development"
    let signatureBase64 = signature.data(using: .utf8)!.base64EncodedString()
        .replacingOccurrences(of: "+", with: "-")
        .replacingOccurrences(of: "/", with: "_")
        .replacingOccurrences(of: "=", with: "")

    return "\(headerBase64).\(payloadBase64).\(signatureBase64)"
}

// 模拟 Apple ID 凭证
struct MockAppleIDCredential: AppleIDCredential {
    let userID: String = "001031.ed50d3aba6e14fa5a08019de106260ae.0506"
    let email: String? = "<EMAIL>"
    let fullName: PersonNameComponents? = {
        var name = PersonNameComponents()
        name.givenName = "Test"
        name.familyName = "User"
        return name
    }()
    let identityToken: Data?
    let authorizationCode: Data? = nil

    init() {
        let jwtToken = createAppleJWTToken()
        self.identityToken = jwtToken.data(using: .utf8)
        print("🔧 生成的 JWT Token: \(jwtToken)")
        print("📏 Token 长度: \(jwtToken.count)")
    }
}

// 测试函数
func testNetworkClientDependency() async {
    print("📱 创建测试环境...")

    // 使用 withDependencies 来设置依赖
    await withDependencies {
        $0.networkClient = .liveValue
        $0.loggingClient = .liveValue
    } operation: {
        do {
            print("🔧 测试 AuthenticationClient.signInWithApple...")
            print("🔧 创建 Mock Apple ID 凭证...")

            let mockCredential = MockAppleIDCredential()
            print("✅ Mock 凭证创建成功")
            print("   用户ID: \(mockCredential.userID)")
            print("   邮箱: \(mockCredential.email ?? "无")")
            print("   姓名: \(mockCredential.fullName?.formatted() ?? "无")")

            let authClient = AuthenticationClient.liveValue

            print("🚀 开始调用 signInWithApple...")
            // 这应该不会抛出 "no live implementation" 错误
            let response = try await authClient.signInWithApple(mockCredential)

            print("✅ 测试成功！")
            print("   Token: \(response.token)")
            print("   User: \(response.user?.displayName ?? "Unknown")")
            print("   Email: \(response.user?.email ?? "Unknown")")
            print("   Provider: \(response.user?.authProvider.rawValue ?? "Unknown")")
            print("   2FA Required: \(response.twoFactorRequired)")

        } catch {
            print("❌ 测试失败: \(error)")
            print("   错误类型: \(type(of: error))")
            if let localizedError = error as? LocalizedError {
                print("   错误描述: \(localizedError.localizedDescription)")
            }
        }
    }
}

// 运行测试
Task {
    await testNetworkClientDependency()
    print("🏁 测试完成")
}

// 保持脚本运行
RunLoop.main.run()
