import XCTest
import ComposableArchitecture
import AuthenticationClient
import UserState<PERSON>ore
import SwiftUI

#if canImport(AuthenticationServices)
import AuthenticationServices
#endif

/// 专门测试Apple ID登录修复的测试类
@MainActor
final class AppleSignInFixTests: XCTestCase {
  
  // MARK: - 错误代码1000修复测试
  
  func testAppleSignInError1000Handling() async {
    // 模拟真机上遇到的错误代码1000
    let store = TestStore(initialState: UserState.State()) {
      UserState()
    } withDependencies: {
      $0.authenticationClient = AuthenticationClient(
        login: { _, _ in
          throw AuthenticationError.invalidCredentials
        },
        twoFactor: { _, _ in
          throw AuthenticationError.invalidCredentials
        },
        signInWithApple: { _ in
          // 模拟错误代码1000 (用户取消或配置问题)
          let error = NSError(
            domain: "com.apple.AuthenticationServices.AuthorizationError",
            code: 1000,
            userInfo: [NSLocalizedDescriptionKey: "The operation couldn't be completed."]
          )
          throw error
        },
        logout: {}
      )
    }
    
    let mockCredential = AppleIDCredential(
      userID: "test.error.1000",
      email: "<EMAIL>",
      fullName: nil,
      identityToken: nil,
      authorizationCode: nil
    )
    
    await store.send(.signInWithApple(mockCredential)) { state in
      state.isLoading = true
      state.error = nil
    }
    
    await store.receive(\.loginFailed) { state in
      state.isLoading = false
      XCTAssertNotNil(state.error)
      // 验证错误消息是用户友好的
      XCTAssertTrue(state.error?.contains("操作") == true || state.error?.contains("登录") == true)
    }
  }
  
  // MARK: - Apple ID配置修复测试
  
  func testAppleSignInWithNonceConfiguration() async {
    // 测试添加nonce配置后的登录流程
    let store = TestStore(initialState: UserState.State()) {
      UserState()
    } withDependencies: {
      $0.authenticationClient = AuthenticationClient(
        login: { _, _ in
          throw AuthenticationError.invalidCredentials
        },
        twoFactor: { _, _ in
          throw AuthenticationError.invalidCredentials
        },
        signInWithApple: { credential in
          // 验证凭证包含必要信息
          XCTAssertFalse(credential.userID.isEmpty, "用户ID不应为空")
          
          try await Task.sleep(for: .milliseconds(100))
          
          let user = AuthenticatedUser(
            id: credential.userID,
            email: credential.email ?? "<EMAIL>",
            displayName: "Nonce Test User",
            avatarURL: nil,
            authProvider: .apple
          )
          
          return AuthenticationResponse(
            token: "nonce_test_token",
            twoFactorRequired: false,
            user: user
          )
        },
        logout: {}
      )
    }
    
    let mockCredential = AppleIDCredential(
      userID: "nonce.test.user",
      email: "<EMAIL>",
      fullName: PersonNameComponents(
        givenName: "Nonce",
        familyName: "Test"
      ),
      identityToken: "nonce_token".data(using: .utf8),
      authorizationCode: "nonce_code".data(using: .utf8)
    )
    
    await store.send(.signInWithApple(mockCredential)) { state in
      state.isLoading = true
      state.error = nil
    }
    
    await store.receive(\.loginSucceeded) { state in
      state.isLoading = false
      state.authenticationStatus = .authenticated
      XCTAssertNotNil(state.user)
      XCTAssertEqual(state.user?.id, "nonce.test.user")
    }
  }
  
  // MARK: - 真机环境模拟测试
  
  func testRealDeviceScenarios() async {
    // 模拟真机上可能遇到的各种场景
    let scenarios: [(String, Error)] = [
      ("网络连接问题", NSError(domain: NSURLErrorDomain, code: NSURLErrorNotConnectedToInternet)),
      ("Apple服务不可用", NSError(domain: "com.apple.AuthenticationServices.AuthorizationError", code: 1001)),
      ("用户账户问题", NSError(domain: "com.apple.AuthenticationServices.AuthorizationError", code: 1002)),
      ("设备限制", NSError(domain: "com.apple.AuthenticationServices.AuthorizationError", code: 1003))
    ]
    
    for (scenarioName, error) in scenarios {
      print("🧪 测试场景: \(scenarioName)")
      
      let store = TestStore(initialState: UserState.State()) {
        UserState()
      } withDependencies: {
        $0.authenticationClient = AuthenticationClient(
          login: { _, _ in throw AuthenticationError.invalidCredentials },
          twoFactor: { _, _ in throw AuthenticationError.invalidCredentials },
          signInWithApple: { _ in throw error },
          logout: {}
        )
      }
      
      let mockCredential = AppleIDCredential(
        userID: "scenario.test",
        email: "<EMAIL>",
        fullName: nil,
        identityToken: nil,
        authorizationCode: nil
      )
      
      await store.send(.signInWithApple(mockCredential)) { state in
        state.isLoading = true
        state.error = nil
      }
      
      await store.receive(\.loginFailed) { state in
        state.isLoading = false
        XCTAssertNotNil(state.error, "场景 \(scenarioName) 应该产生错误")
      }
    }
  }
  
  // MARK: - 状态跟踪修复验证
  
  func testWithPerceptionTrackingFix() async {
    // 验证WithPerceptionTracking修复后的状态跟踪
    let store = TestStore(initialState: UserState.State()) {
      UserState()
    }
    
    // 测试状态变化是否被正确跟踪
    await store.send(.promptLogin) { state in
      state.shouldPromptLogin = true
    }
    
    await store.send(.dismissLoginPrompt) { state in
      state.shouldPromptLogin = false
    }
    
    // 测试加载状态
    await store.send(.signInWithApple(AppleIDCredential(
      userID: "tracking.test",
      email: "<EMAIL>",
      fullName: nil,
      identityToken: nil,
      authorizationCode: nil
    ))) { state in
      state.isLoading = true
      state.error = nil
    }
  }
  
  // MARK: - 错误恢复测试
  
  func testErrorRecovery() async {
    let store = TestStore(initialState: UserState.State()) {
      UserState()
    } withDependencies: {
      $0.authenticationClient = AuthenticationClient(
        login: { _, _ in throw AuthenticationError.invalidCredentials },
        twoFactor: { _, _ in throw AuthenticationError.invalidCredentials },
        signInWithApple: { credential in
          // 第一次失败，第二次成功
          if credential.userID == "recovery.fail" {
            throw AuthenticationError.appleSignInFailed
          } else {
            let user = AuthenticatedUser(
              id: credential.userID,
              email: credential.email ?? "<EMAIL>",
              displayName: "Recovery Test",
              avatarURL: nil,
              authProvider: .apple
            )
            
            return AuthenticationResponse(
              token: "recovery_token",
              twoFactorRequired: false,
              user: user
            )
          }
        },
        logout: {}
      )
    }
    
    // 第一次尝试失败
    await store.send(.signInWithApple(AppleIDCredential(
      userID: "recovery.fail",
      email: "<EMAIL>",
      fullName: nil,
      identityToken: nil,
      authorizationCode: nil
    ))) { state in
      state.isLoading = true
      state.error = nil
    }
    
    await store.receive(\.loginFailed) { state in
      state.isLoading = false
      XCTAssertNotNil(state.error)
    }
    
    // 第二次尝试成功
    await store.send(.signInWithApple(AppleIDCredential(
      userID: "recovery.success",
      email: "<EMAIL>",
      fullName: nil,
      identityToken: nil,
      authorizationCode: nil
    ))) { state in
      state.isLoading = true
      state.error = nil
    }
    
    await store.receive(\.loginSucceeded) { state in
      state.isLoading = false
      state.authenticationStatus = .authenticated
      XCTAssertNotNil(state.user)
    }
  }
}
