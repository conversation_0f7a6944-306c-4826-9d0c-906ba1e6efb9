#!/usr/bin/env swift

import Foundation

// 示例：如何在后续 API 请求中使用存储的 access_token

// 模拟 AccessTokenManager（实际使用时从 AuthenticationClient 导入）
struct AccessTokenManager {
    static func getAppleAccessToken() -> String? {
        return UserDefaults.standard.string(forKey: "apple_access_token")
    }
    
    static func getAppleTokenType() -> String? {
        return UserDefaults.standard.string(forKey: "apple_token_type")
    }
    
    static func getAuthorizationHeader() -> String? {
        guard let token = getAppleAccessToken(),
              let tokenType = getAppleTokenType() else {
            return nil
        }
        return "\(tokenType) \(token)"
    }
}

// 示例：调用需要认证的 API
func callAuthenticatedAPI() async {
    print("🔐 开始调用需要认证的 API...")
    
    // 检查是否有有效的 access_token
    guard let authHeader = AccessTokenManager.getAuthorizationHeader() else {
        print("❌ 没有有效的 access_token，请先登录")
        return
    }
    
    print("✅ 找到有效的 access_token")
    print("   Authorization Header: \(authHeader)")
    
    do {
        // 示例：调用用户信息 API
        let url = URL(string: "http://localhost:8000/api/v1/user/profile")!
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue(authHeader, forHTTPHeaderField: "Authorization")  // 🔑 关键：添加认证头
        
        print("🔍 请求详情:")
        print("   URL: \(request.url?.absoluteString ?? "无")")
        print("   Method: \(request.httpMethod ?? "无")")
        print("   Headers: \(request.allHTTPHeaderFields ?? [:])")
        
        print("🚀 开始调用认证 API...")
        
        // 执行网络请求
        let (data, response) = try await URLSession.shared.data(for: request)
        
        print("✅ 认证 API 调用成功")
        print("   响应数据长度: \(data.count)")
        
        if let httpResponse = response as? HTTPURLResponse {
            print("   HTTP 状态码: \(httpResponse.statusCode)")
            
            if httpResponse.statusCode == 200 {
                print("✅ 认证成功，API 调用正常")
            } else if httpResponse.statusCode == 401 {
                print("❌ 认证失败，access_token 可能已过期")
            } else {
                print("⚠️ API 调用返回状态码: \(httpResponse.statusCode)")
            }
        }
        
        if let responseString = String(data: data, encoding: .utf8) {
            print("   响应内容: \(responseString)")
        }
        
    } catch {
        print("❌ 认证 API 调用失败: \(error.localizedDescription)")
        print("   错误类型: \(type(of: error))")
    }
}

// 示例：调用其他需要认证的 API
func callImageGenerationAPI() async {
    print("🎨 开始调用图像生成 API...")
    
    guard let authHeader = AccessTokenManager.getAuthorizationHeader() else {
        print("❌ 没有有效的 access_token，请先登录")
        return
    }
    
    do {
        let url = URL(string: "http://localhost:8000/api/v1/images/generate")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue(authHeader, forHTTPHeaderField: "Authorization")  // 🔑 关键：添加认证头
        
        // 示例请求数据
        let requestData = [
            "style": "romantic",
            "prompt": "beautiful wedding photo",
            "count": 1
        ]
        
        let jsonData = try JSONSerialization.data(withJSONObject: requestData)
        request.httpBody = jsonData
        
        print("🔍 图像生成请求详情:")
        print("   URL: \(request.url?.absoluteString ?? "无")")
        print("   Authorization: \(authHeader)")
        if let bodyString = String(data: jsonData, encoding: .utf8) {
            print("   Body: \(bodyString)")
        }
        
        print("🚀 开始调用图像生成 API...")
        
        // 执行网络请求
        let (data, response) = try await URLSession.shared.data(for: request)
        
        if let httpResponse = response as? HTTPURLResponse {
            print("   HTTP 状态码: \(httpResponse.statusCode)")
            
            if httpResponse.statusCode == 200 {
                print("✅ 图像生成 API 调用成功")
            } else if httpResponse.statusCode == 401 {
                print("❌ 认证失败，access_token 可能已过期")
            } else if httpResponse.statusCode == 403 {
                print("❌ 权限不足，可能需要升级订阅")
            } else {
                print("⚠️ API 调用返回状态码: \(httpResponse.statusCode)")
            }
        }
        
        if let responseString = String(data: data, encoding: .utf8) {
            print("   响应内容: \(responseString)")
        }
        
    } catch {
        print("❌ 图像生成 API 调用失败: \(error.localizedDescription)")
    }
}

// 运行示例
Task {
    print("🧪 开始测试认证 API 调用...")
    
    // 首先检查是否有存储的 token
    if let token = AccessTokenManager.getAppleAccessToken() {
        print("✅ 找到存储的 access_token: \(String(token.prefix(20)))...")
        
        // 调用需要认证的 API
        await callAuthenticatedAPI()
        
        print("\n" + "="*50 + "\n")
        
        // 调用图像生成 API
        await callImageGenerationAPI()
        
    } else {
        print("❌ 没有找到存储的 access_token")
        print("💡 请先运行 Apple ID 登录获取 access_token")
    }
    
    print("🏁 认证 API 测试完成")
    exit(0)
}

// 保持脚本运行
RunLoop.main.run()
