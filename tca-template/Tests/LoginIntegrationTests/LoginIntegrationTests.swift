import XCTest
import ComposableArchitecture
import AppCore
import UserStateCore
import AuthenticationClient

@MainActor
final class LoginIntegrationTests: XCTestCase {
  
  func testCompleteLoginFlow() async {
    // 测试完整的登录流程：从主界面到登录成功
    let store = TestStore(initialState: AppFeature.State.main(MainTab.State())) {
      AppFeature()
    } withDependencies: {
      $0.authenticationClient = AuthenticationClient(
        login: { _, _ in
          throw AuthenticationError.invalidCredentials
        },
        twoFactor: { _, _ in
          throw AuthenticationError.invalidCredentials
        },
        signInWithApple: { credential in
          try await Task.sleep(for: .milliseconds(100))
          
          let user = AuthenticatedUser(
            id: credential.userID,
            email: credential.email ?? "<EMAIL>",
            displayName: credential.fullName?.formatted() ?? "Apple用户",
            avatarURL: nil,
            authProvider: .apple
          )
          
          return AuthenticationResponse(
            token: "test_apple_token",
            twoFactorRequired: false,
            user: user
          )
        },
        logout: {
          try await Task.sleep(for: .milliseconds(50))
        }
      )
    }
    
    // 1. 用户点击"立即登录"
    await store.send(.main(.userState(.promptLogin))) { state in
      if case .main(var mainState) = state {
        mainState.isShowingLoginPrompt = true
        mainState.userState.shouldPromptLogin = true
        state = .main(mainState)
      }
    }
    
    // 2. 用户点击Apple ID登录
    let mockCredential = AppleIDCredential(
      userID: "integration.test.user",
      email: "<EMAIL>",
      fullName: PersonNameComponents(
        givenName: "Integration",
        familyName: "Test"
      ),
      identityToken: "mock_token".data(using: .utf8),
      authorizationCode: "mock_code".data(using: .utf8)
    )
    
    await store.send(.main(.userState(.signInWithApple(mockCredential)))) { state in
      if case .main(var mainState) = state {
        mainState.userState.isLoading = true
        mainState.userState.error = nil
        state = .main(mainState)
      }
    }
    
    // 3. 登录成功
    await store.receive(.main(.userState(.loginSucceeded))) { state in
      if case .main(var mainState) = state {
        mainState.userState.isLoading = false
        mainState.userState.authenticationStatus = .authenticated
        mainState.userState.shouldPromptLogin = false
        mainState.userState.guestUsageStats = GuestUsageStats()

        // 关键修复：登录成功后应该关闭登录弹窗
        mainState.isShowingLoginPrompt = false

        // 验证用户信息
        XCTAssertNotNil(mainState.userState.user)
        XCTAssertEqual(mainState.userState.user?.id, "integration.test.user")
        XCTAssertEqual(mainState.userState.user?.subscriptionStatus, .premium)

        state = .main(mainState)
      }
    }
  }
  
  func testLoginPromptDismissal() async {
    let store = TestStore(initialState: AppFeature.State.main(MainTab.State())) {
      AppFeature()
    }
    
    // 1. 显示登录提示
    await store.send(.main(.userState(.promptLogin))) { state in
      if case .main(var mainState) = state {
        mainState.isShowingLoginPrompt = true
        mainState.userState.shouldPromptLogin = true
        state = .main(mainState)
      }
    }
    
    // 2. 用户关闭登录提示
    await store.send(.main(.dismissLoginPrompt)) { state in
      if case .main(var mainState) = state {
        mainState.isShowingLoginPrompt = false
        state = .main(mainState)
      }
    }
    
    await store.receive(.main(.userState(.dismissLoginPrompt))) { state in
      if case .main(var mainState) = state {
        mainState.userState.shouldPromptLogin = false
        state = .main(mainState)
      }
    }
  }
  
  func testNavigateToEmailLogin() async {
    let store = TestStore(initialState: AppFeature.State.main(MainTab.State())) {
      AppFeature()
    }
    
    // 1. 显示登录提示
    await store.send(.main(.userState(.promptLogin))) { state in
      if case .main(var mainState) = state {
        mainState.isShowingLoginPrompt = true
        mainState.userState.shouldPromptLogin = true
        state = .main(mainState)
      }
    }
    
    // 2. 用户选择邮箱登录
    await store.send(.main(.navigateToLogin)) { state in
      if case .main(var mainState) = state {
        mainState.isShowingLoginPrompt = false
        state = .main(mainState)
      }
    }
    
    // 3. 应该导航到登录界面
    await store.receive { action in
      if case .login = action {
        return true
      }
      return false
    } transform: { state in
      // 验证状态切换到登录界面
      XCTAssertTrue(state.isLogin)
    }
  }
  
  func testLoginSuccessClosesPrompt() async {
    // 测试登录成功后自动关闭登录弹窗
    let store = TestStore(initialState: AppFeature.State.main(MainTab.State())) {
      AppFeature()
    } withDependencies: {
      $0.authenticationClient = AuthenticationClient(
        login: { _, _ in
          throw AuthenticationError.invalidCredentials
        },
        twoFactor: { _, _ in
          throw AuthenticationError.invalidCredentials
        },
        signInWithApple: { credential in
          try await Task.sleep(for: .milliseconds(100))

          let user = AuthenticatedUser(
            id: credential.userID,
            email: credential.email ?? "<EMAIL>",
            displayName: credential.fullName?.formatted() ?? "Test User",
            avatarURL: nil,
            authProvider: .apple
          )

          return AuthenticationResponse(
            token: "test_token",
            twoFactorRequired: false,
            user: user
          )
        },
        logout: {}
      )
    }

    // 1. 显示登录提示
    await store.send(.main(.userState(.promptLogin))) { state in
      if case .main(var mainState) = state {
        mainState.isShowingLoginPrompt = true
        mainState.userState.shouldPromptLogin = true
        state = .main(mainState)
      }
    }

    // 2. 开始Apple ID登录
    let mockCredential = AppleIDCredential(
      userID: "test.close.prompt",
      email: "<EMAIL>",
      fullName: PersonNameComponents(givenName: "Test", familyName: "User"),
      identityToken: "token".data(using: .utf8),
      authorizationCode: "code".data(using: .utf8)
    )

    await store.send(.main(.userState(.signInWithApple(mockCredential)))) { state in
      if case .main(var mainState) = state {
        mainState.userState.isLoading = true
        mainState.userState.error = nil
        state = .main(mainState)
      }
    }

    // 3. 登录成功，验证弹窗自动关闭
    await store.receive(.main(.userState(.loginSucceeded))) { state in
      if case .main(var mainState) = state {
        mainState.userState.isLoading = false
        mainState.userState.authenticationStatus = .authenticated
        mainState.userState.shouldPromptLogin = false
        mainState.userState.guestUsageStats = GuestUsageStats()

        // 关键验证：登录弹窗应该自动关闭
        mainState.isShowingLoginPrompt = false

        XCTAssertNotNil(mainState.userState.user)
        XCTAssertEqual(mainState.userState.user?.id, "test.close.prompt")

        state = .main(mainState)
      }
    }
  }

  func testGuestModeUsageTracking() async {
    let store = TestStore(initialState: AppFeature.State.main(MainTab.State())) {
      AppFeature()
    }
    
    // 测试访客模式的使用统计
    await store.send(.main(.userState(.incrementGuestUsage(.imageGeneration)))) { state in
      if case .main(var mainState) = state {
        mainState.userState.guestUsageStats.totalGenerations += 1
        mainState.userState.guestUsageStats.remainingGenerations = max(0, mainState.userState.guestUsageStats.remainingGenerations - 1)
        state = .main(mainState)
      }
    }
    
    // 应该触发登录提示检查
    await store.receive(.main(.userState(.checkLoginPromptThreshold)))
  }
}

// MARK: - 状态验证扩展

extension AppFeature.State {
  var isMain: Bool {
    if case .main = self { return true }
    return false
  }
  
  var isLogin: Bool {
    if case .login = self { return true }
    return false
  }
  
  var isWelcome: Bool {
    if case .welcome = self { return true }
    return false
  }
}

// MARK: - 性能测试

extension LoginIntegrationTests {
  
  func testLoginPerformance() async {
    measure {
      Task {
        let store = TestStore(initialState: AppFeature.State.main(MainTab.State())) {
          AppFeature()
        } withDependencies: {
          $0.authenticationClient = AuthenticationClient(
            login: { _, _ in
              throw AuthenticationError.invalidCredentials
            },
            twoFactor: { _, _ in
              throw AuthenticationError.invalidCredentials
            },
            signInWithApple: { credential in
              // 快速响应以测试性能
              try await Task.sleep(for: .milliseconds(10))
              
              let user = AuthenticatedUser(
                id: credential.userID,
                email: credential.email ?? "<EMAIL>",
                displayName: "Performance Test",
                avatarURL: nil,
                authProvider: .apple
              )
              
              return AuthenticationResponse(
                token: "perf_token",
                twoFactorRequired: false,
                user: user
              )
            },
            logout: {}
          )
        }
        
        let mockCredential = AppleIDCredential(
          userID: "perf.test",
          email: "<EMAIL>",
          fullName: nil,
          identityToken: nil,
          authorizationCode: nil
        )
        
        await store.send(.main(.userState(.signInWithApple(mockCredential))))
        await store.receive(.main(.userState(.loginSucceeded)))
      }
    }
  }
}
