import XCTest
import ComposableArchitecture
import AuthenticationClient
import UserStateCore
import UserStateSwiftUI
import SwiftUI

#if canImport(AuthenticationServices)
import AuthenticationServices
#endif

@MainActor
final class AppleSignInTests: XCTestCase {
  
  func testAppleSignInSuccess() async {
    // 创建测试用的Apple ID凭证
    let mockCredential = AppleIDCredential(
      userID: "test.user.001",
      email: "<EMAIL>",
      fullName: PersonNameComponents(
        givenName: "Test",
        familyName: "User"
      ),
      identityToken: "mock_identity_token".data(using: .utf8),
      authorizationCode: "mock_auth_code".data(using: .utf8)
    )
    
    // 创建测试store
    let store = TestStore(initialState: UserState.State()) {
      UserState()
    } withDependencies: {
      $0.authenticationClient = AuthenticationClient(
        login: { _, _ in
          throw AuthenticationError.invalidCredentials
        },
        twoFactor: { _, _ in
          throw AuthenticationError.invalidCredentials
        },
        signInWithApple: { credential in
          // 模拟成功的Apple ID登录
          try await Task.sleep(for: .milliseconds(100))
          
          let user = AuthenticatedUser(
            id: credential.userID,
            email: credential.email ?? "<EMAIL>",
            displayName: credential.fullName?.formatted() ?? "Apple用户",
            avatarURL: nil,
            authProvider: .apple
          )
          
          return AuthenticationResponse(
            token: "apple_access_token_\(Date().timeIntervalSince1970)",
            twoFactorRequired: false,
            user: user
          )
        },
        logout: {
          try await Task.sleep(for: .milliseconds(50))
        }
      )
    }
    
    // 测试Apple ID登录流程
    await store.send(.signInWithApple(mockCredential)) { state in
      state.isLoading = true
      state.error = nil
    }
    
    await store.receive(\.loginSucceeded) { state in
      state.isLoading = false
      state.authenticationStatus = .authenticated
      state.shouldPromptLogin = false
      state.guestUsageStats = GuestUsageStats()
      
      // 验证用户信息
      XCTAssertNotNil(state.user)
      XCTAssertEqual(state.user?.id, "test.user.001")
      XCTAssertEqual(state.user?.email, "<EMAIL>")
      XCTAssertEqual(state.user?.subscriptionStatus, .premium)
    }
  }
  
  func testAppleSignInFailure() async {
    let mockCredential = AppleIDCredential(
      userID: "test.user.002",
      email: "<EMAIL>",
      fullName: nil,
      identityToken: nil,
      authorizationCode: nil
    )
    
    let store = TestStore(initialState: UserState.State()) {
      UserState()
    } withDependencies: {
      $0.authenticationClient = AuthenticationClient(
        login: { _, _ in
          throw AuthenticationError.invalidCredentials
        },
        twoFactor: { _, _ in
          throw AuthenticationError.invalidCredentials
        },
        signInWithApple: { _ in
          // 模拟Apple ID登录失败
          throw AuthenticationError.appleSignInFailed
        },
        logout: {
          try await Task.sleep(for: .milliseconds(50))
        }
      )
    }
    
    await store.send(.signInWithApple(mockCredential)) { state in
      state.isLoading = true
      state.error = nil
    }
    
    await store.receive(\.loginFailed) { state in
      state.isLoading = false
      XCTAssertNotNil(state.error)
      XCTAssertEqual(state.authenticationStatus, .guest)
    }
  }
  
  func testPromptLoginAction() async {
    let store = TestStore(initialState: UserState.State()) {
      UserState()
    }
    
    await store.send(.promptLogin) { state in
      state.shouldPromptLogin = true
    }
  }
  
  func testDismissLoginPrompt() async {
    let store = TestStore(
      initialState: UserState.State(shouldPromptLogin: true)
    ) {
      UserState()
    }
    
    await store.send(.dismissLoginPrompt) { state in
      state.shouldPromptLogin = false
    }
  }
}

// MARK: - Apple ID错误处理测试

extension AppleSignInTests {
  
  func testAppleSignInErrorHandling() {
    // 测试不同的Apple ID错误代码
    let testCases: [(ASAuthorizationError.Code, String)] = [
      (.canceled, "登录已取消"),
      (.failed, "Apple登录失败，请检查网络连接后重试"),
      (.invalidResponse, "Apple服务响应异常，请稍后重试"),
      (.notHandled, "Apple登录服务暂时不可用"),
      (.unknown, "Apple登录遇到未知错误，请重试")
    ]
    
    for (errorCode, expectedMessage) in testCases {
      let error = ASAuthorizationError(errorCode)
      
      // 这里我们测试错误消息的映射逻辑
      let actualMessage = mapAppleSignInError(error)
      XCTAssertEqual(actualMessage, expectedMessage, "错误代码 \(errorCode) 的消息映射不正确")
    }
  }
}

// MARK: - 辅助函数

private func mapAppleSignInError(_ error: Error) -> String {
  if let authError = error as? ASAuthorizationError {
    switch authError.code {
    case .canceled:
      return "登录已取消"
    case .failed:
      return "Apple登录失败，请检查网络连接后重试"
    case .invalidResponse:
      return "Apple服务响应异常，请稍后重试"
    case .notHandled:
      return "Apple登录服务暂时不可用"
    case .unknown:
      return "Apple登录遇到未知错误，请重试"
    @unknown default:
      return "Apple登录失败，请重试"
    }
  } else {
    return "Apple登录失败，请重试"
  }
}

#if canImport(AuthenticationServices)
extension ASAuthorizationError {
  convenience init(_ code: ASAuthorizationError.Code) {
    self.init(_nsError: NSError(
      domain: ASAuthorizationErrorDomain,
      code: code.rawValue,
      userInfo: nil
    ))
  }
}
#endif
