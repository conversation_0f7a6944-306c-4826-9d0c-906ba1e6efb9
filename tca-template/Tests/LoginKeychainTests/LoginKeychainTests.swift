import Testing
import ComposableArchitecture
import Foundation
@testable import UserStateCore
@testable import UserStorageClient
@testable import LaunchCore

@Suite("用户认证集成测试")
struct LoginKeychainTests {
  
  @Test("基础用户存储测试")
  func testBasicUserStorage() async throws {
    // Create a simple test user
    let testUser = User(
      id: "test_123",
      email: "<EMAIL>",
      displayName: "Test User",
      avatarURL: nil,
      createdAt: Date(),
      subscriptionStatus: .free
    )
    
    let testToken = "test_token_123"
    
    // Test the User model creation
    #expect(testUser.id == "test_123")
    #expect(testUser.email == "<EMAIL>")
    #expect(testUser.displayName == "Test User")
    #expect(testUser.subscriptionStatus == .free)
    
    print("✅ 基础用户存储测试通过")
  }
  
  @Test("Launch状态测试")
  func testLaunchState() async throws {
    let store = TestStore(initialState: Launch.State()) {
      Launch()
    } withDependencies: {
      // Mock implementation that returns not logged in
      $0.userStorageClient = UserStorageClient(
        saveUser: { _, _ in
          // Mock save operation
        },
        loadUser: {
          return nil // Return nil to simulate no stored user
        },
        deleteUser: {
          // Mock delete operation
        },
        isUserLoggedIn: {
          return false // Return false to simulate not logged in
        }
      )
    }

    await store.send(.checkAuthenticationStatus)
    await store.receive(.userNotAuthenticated) {
      $0.isLoading = false
    }
    
    print("✅ Launch状态测试通过")
  }
  
  @Test("用户认证状态枚举测试")
  func testUserAuthenticationStatus() {
    let guestStatus = AuthenticationStatus.guest
    let authenticatedStatus = AuthenticationStatus.authenticated
    
    #expect(guestStatus != authenticatedStatus)
    
    print("✅ 用户认证状态枚举测试通过")
  }
}