#!/usr/bin/env swift

import Foundation

// 测试 Apple ID 登录的不同场景：首次登录 vs 非首次登录

// 创建符合 Apple JWT 格式的 identity_token
func createAppleJWTToken() -> String {
    // JWT Header
    let header = [
        "alg": "HS256",
        "typ": "JWT"
    ]
    
    // JWT Payload - 符合 Apple Identity Token 格式
    let currentTime = Int(Date().timeIntervalSince1970)
    let payload = [
        "iss": "https://appleid.apple.com",
        "aud": "com.wenhaofree.bridal-swift",
        "exp": currentTime + 600, // 10分钟后过期
        "iat": currentTime,
        "sub": "001031.ed50d3aba6e14fa5a08019de106260ae.0506",
        "email": "<EMAIL>",
        "email_verified": true,
        "is_private_email": true,
        "real_user_status": 2
    ] as [String : Any]
    
    // 简单的 Base64 编码（测试环境不验证签名）
    let headerData = try! JSONSerialization.data(withJSONObject: header)
    let payloadData = try! JSONSerialization.data(withJSONObject: payload)
    
    let headerBase64 = headerData.base64EncodedString()
        .replacingOccurrences(of: "+", with: "-")
        .replacingOccurrences(of: "/", with: "_")
        .replacingOccurrences(of: "=", with: "")
    
    let payloadBase64 = payloadData.base64EncodedString()
        .replacingOccurrences(of: "+", with: "-")
        .replacingOccurrences(of: "/", with: "_")
        .replacingOccurrences(of: "=", with: "")
    
    // 简单的签名（测试环境）
    let signature = "test-signature-for-development"
    let signatureBase64 = signature.data(using: .utf8)!.base64EncodedString()
        .replacingOccurrences(of: "+", with: "-")
        .replacingOccurrences(of: "/", with: "_")
        .replacingOccurrences(of: "=", with: "")
    
    return "\(headerBase64).\(payloadBase64).\(signatureBase64)"
}

// 测试场景1：首次登录（带用户信息）
func testFirstTimeLogin() async {
    print("🆕 测试场景1：首次登录（带用户信息）")
    print(String(repeating: "=", count: 50))
    
    do {
        let jwtToken = createAppleJWTToken()
        
        // 首次登录请求数据（包含用户信息）
        let requestData = [
            "identity_token": jwtToken,
            "platform": "ios",
            "user_info": [
                "firstName": "张",
                "lastName": "三"
            ]
        ] as [String : Any]
        
        let url = URL(string: "http://localhost:8000/api/v1/oauth/apple/login")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let jsonData = try JSONSerialization.data(withJSONObject: requestData)
        request.httpBody = jsonData
        
        print("🔍 首次登录请求详情:")
        print("   URL: \(request.url?.absoluteString ?? "无")")
        if let bodyString = String(data: jsonData, encoding: .utf8) {
            print("   Body: \(bodyString)")
        }
        
        print("🚀 开始调用首次登录 API...")
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        if let httpResponse = response as? HTTPURLResponse {
            print("✅ 首次登录请求成功")
            print("   HTTP 状态码: \(httpResponse.statusCode)")
            
            if let responseString = String(data: data, encoding: .utf8) {
                print("   响应内容: \(responseString)")
                
                // 检查响应中的用户姓名
                if responseString.contains("张 三") || responseString.contains("张三") {
                    print("✅ 用户姓名正确保存：张 三")
                } else {
                    print("⚠️ 用户姓名可能未正确保存")
                }
            }
        }
        
    } catch {
        print("❌ 首次登录测试失败: \(error.localizedDescription)")
    }
}

// 测试场景2：非首次登录（不带用户信息）
func testSubsequentLogin() async {
    print("\n🔄 测试场景2：非首次登录（不带用户信息）")
    print(String(repeating: "=", count: 50))
    
    do {
        let jwtToken = createAppleJWTToken()
        
        // 非首次登录请求数据（不包含用户信息）
        let requestData = [
            "identity_token": jwtToken,
            "platform": "ios"
        ] as [String : Any]
        
        let url = URL(string: "http://localhost:8000/api/v1/oauth/apple/login")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let jsonData = try JSONSerialization.data(withJSONObject: requestData)
        request.httpBody = jsonData
        
        print("🔍 非首次登录请求详情:")
        print("   URL: \(request.url?.absoluteString ?? "无")")
        if let bodyString = String(data: jsonData, encoding: .utf8) {
            print("   Body: \(bodyString)")
        }
        
        print("🚀 开始调用非首次登录 API...")
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        if let httpResponse = response as? HTTPURLResponse {
            print("✅ 非首次登录请求成功")
            print("   HTTP 状态码: \(httpResponse.statusCode)")
            
            if let responseString = String(data: data, encoding: .utf8) {
                print("   响应内容: \(responseString)")
                
                // 检查响应中的用户姓名（应该使用默认生成的或之前保存的）
                if responseString.contains("Apple User") || responseString.contains("张") {
                    print("✅ 用户信息正确处理（使用默认名称或已保存的姓名）")
                } else {
                    print("⚠️ 用户信息处理可能有问题")
                }
            }
        }
        
    } catch {
        print("❌ 非首次登录测试失败: \(error.localizedDescription)")
    }
}

// 运行测试
Task {
    print("🧪 开始测试 Apple ID 登录的不同场景...")
    print("📝 根据文档，首次登录应包含用户信息，非首次登录不包含用户信息")
    print("")
    
    // 测试首次登录
    await testFirstTimeLogin()
    
    // 等待一秒
    try? await Task.sleep(for: .seconds(1))
    
    // 测试非首次登录
    await testSubsequentLogin()
    
    print("\n🏁 所有测试完成")
    print("💡 检查上述日志，确认:")
    print("   1. 首次登录请求包含 user_info 字段")
    print("   2. 非首次登录请求不包含 user_info 字段")
    print("   3. 后端正确处理两种情况并保存用户数据")
    
    exit(0)
}

// 保持脚本运行
RunLoop.main.run()
