import XCTest
import AuthenticationClient
import Foundation

#if canImport(UIKit)
import UIKit
#endif

/// 测试Apple ID登录真实邮箱传递功能
final class AppleLoginRealEmailTests: XCTestCase {
  
  func testAppleOAuthRequestWithRealEmail() throws {
    // 测试包含真实邮箱的请求
    let userInfo = UserInfo(
      firstName: "test",
      lastName: "haha", 
      email: "<EMAIL>"
    )
    
    let request = AppleOAuthRequest(
      identityToken: "mock_identity_token",
      platform: "ios",
      userInfo: userInfo,
      realEmail: "<EMAIL>"
    )
    
    // 验证JSON序列化
    let encoder = JSONEncoder()
    let jsonData = try encoder.encode(request)
    let jsonString = String(data: jsonData, encoding: .utf8)!
    
    print("🧪 生成的JSON请求:")
    print(jsonString)
    
    // 验证包含所有必要字段
    XCTAssertTrue(jsonString.contains("identity_token"))
    XCTAssertTrue(jsonString.contains("platform"))
    XCTAssertTrue(jsonString.contains("user_info"))
    XCTAssertTrue(jsonString.contains("real_email"))
    XCTAssertTrue(jsonString.contains("<EMAIL>"))
    XCTAssertTrue(jsonString.contains("firstName"))
    XCTAssertTrue(jsonString.contains("lastName"))
    XCTAssertTrue(jsonString.contains("email"))
  }
  
  func testAppleOAuthRequestWithoutRealEmail() throws {
    // 测试不包含真实邮箱的请求（用户隐藏邮箱）
    let request = AppleOAuthRequest(
      identityToken: "mock_identity_token",
      platform: "ios",
      userInfo: nil,
      realEmail: nil
    )
    
    // 验证JSON序列化
    let encoder = JSONEncoder()
    let jsonData = try encoder.encode(request)
    let jsonString = String(data: jsonData, encoding: .utf8)!
    
    print("🧪 生成的JSON请求（无邮箱）:")
    print(jsonString)
    
    // 验证基本字段存在，可选字段为null
    XCTAssertTrue(jsonString.contains("identity_token"))
    XCTAssertTrue(jsonString.contains("platform"))
  }
  
  func testUserInfoWithEmail() throws {
    // 测试UserInfo包含邮箱
    let userInfo = UserInfo(
      firstName: "test",
      lastName: "haha",
      email: "<EMAIL>"
    )
    
    let encoder = JSONEncoder()
    let jsonData = try encoder.encode(userInfo)
    let jsonString = String(data: jsonData, encoding: .utf8)!
    
    print("🧪 UserInfo JSON:")
    print(jsonString)
    
    XCTAssertTrue(jsonString.contains("firstName"))
    XCTAssertTrue(jsonString.contains("lastName"))
    XCTAssertTrue(jsonString.contains("email"))
    XCTAssertTrue(jsonString.contains("<EMAIL>"))
  }
  
  func testMockAppleIDCredential() {
    // 模拟从日志中看到的Apple ID凭证
    var nameComponents = PersonNameComponents()
    nameComponents.givenName = "test"
    nameComponents.familyName = "haha"
    
    let credential = AppleIDCredential(
      userID: "001031.ed50d3aba6e14fa5a08019de106260ae.0506",
      email: "<EMAIL>",
      fullName: nameComponents,
      identityToken: "mock_token".data(using: .utf8),
      authorizationCode: "mock_code".data(using: .utf8)
    )
    
    // 验证凭证信息
    XCTAssertEqual(credential.userID, "001031.ed50d3aba6e14fa5a08019de106260ae.0506")
    XCTAssertEqual(credential.email, "<EMAIL>")
    XCTAssertEqual(credential.fullName?.givenName, "test")
    XCTAssertEqual(credential.fullName?.familyName, "haha")
    
    print("🧪 模拟的Apple ID凭证:")
    print("   用户ID: \(credential.userID)")
    print("   真实邮箱: \(credential.email ?? "未提供")")
    print("   姓名: \(credential.fullName?.formatted() ?? "未提供")")
  }
  
  func testExpectedAPIRequestFormat() throws {
    // 测试期望的API请求格式
    let expectedJSON = """
    {
        "identity_token": "eyJraWQiOiJVYUlJRlkyZlc0IiwiYWxnIjoiUlMyNTYifQ...",
        "platform": "ios",
        "user_info": {
            "firstName": "test",
            "lastName": "haha",
            "email": "<EMAIL>"
        },
        "real_email": "<EMAIL>"
    }
    """
    
    print("🧪 期望的API请求格式:")
    print(expectedJSON)
    
    // 验证我们的实现能生成类似的结构
    let userInfo = UserInfo(
      firstName: "test",
      lastName: "haha",
      email: "<EMAIL>"
    )
    
    let request = AppleOAuthRequest(
      identityToken: "eyJraWQiOiJVYUlJRlkyZlc0IiwiYWxnIjoiUlMyNTYifQ...",
      platform: "ios",
      userInfo: userInfo,
      realEmail: "<EMAIL>"
    )
    
    let encoder = JSONEncoder()
    encoder.outputFormatting = .prettyPrinted
    let jsonData = try encoder.encode(request)
    let actualJSON = String(data: jsonData, encoding: .utf8)!
    
    print("🧪 实际生成的JSON:")
    print(actualJSON)
    
    // 验证关键字段存在
    XCTAssertTrue(actualJSON.contains("identity_token"))
    XCTAssertTrue(actualJSON.contains("user_info"))
    XCTAssertTrue(actualJSON.contains("real_email"))
    XCTAssertTrue(actualJSON.contains("<EMAIL>"))
  }
}
