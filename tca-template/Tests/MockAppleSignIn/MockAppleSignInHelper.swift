import Foundation
import AuthenticationClient
import ComposableArchitecture

#if canImport(AuthenticationServices)
import AuthenticationServices
#endif

/// Apple ID登录的Mock测试工具
public struct MockAppleSignInHelper {
  
  // MARK: - Mock凭证生成
  
  /// 创建成功的Mock Apple ID凭证
  public static func createSuccessCredential(
    userID: String = "mock.apple.user.123",
    email: String? = "<EMAIL>",
    givenName: String? = "Mock",
    familyName: String? = "User"
  ) -> AppleIDCredential {
    var nameComponents = PersonNameComponents()
    nameComponents.givenName = givenName
    nameComponents.familyName = familyName
    
    return AppleIDCredential(
      userID: userID,
      email: email,
      fullName: nameComponents,
      identityToken: "mock_identity_token".data(using: .utf8),
      authorizationCode: "mock_authorization_code".data(using: .utf8)
    )
  }
  
  /// 创建最小化的Mock Apple ID凭证（模拟隐私保护场景）
  public static func createPrivacyCredential(
    userID: String = "mock.private.user.456"
  ) -> AppleIDCredential {
    return AppleIDCredential(
      userID: userID,
      email: nil, // 用户选择隐藏邮箱
      fullName: nil, // 用户选择隐藏姓名
      identityToken: "private_token".data(using: .utf8),
      authorizationCode: "private_code".data(using: .utf8)
    )
  }
  
  // MARK: - Mock AuthenticationClient
  
  /// 创建总是成功的Mock AuthenticationClient
  public static func createSuccessClient() -> AuthenticationClient {
    return AuthenticationClient(
      login: { _, _ in
        throw AuthenticationError.invalidCredentials
      },
      twoFactor: { _, _ in
        throw AuthenticationError.invalidCredentials
      },
      signInWithApple: { credential in
        try await Task.sleep(for: .milliseconds(500)) // 模拟网络延迟
        
        let displayName: String
        if let fullName = credential.fullName {
          let formatter = PersonNameComponentsFormatter()
          displayName = formatter.string(from: fullName)
        } else {
          displayName = "Apple用户"
        }
        
        let user = AuthenticatedUser(
          id: credential.userID,
          email: credential.email ?? "<EMAIL>",
          displayName: displayName,
          avatarURL: nil,
          authProvider: .apple
        )
        
        return AuthenticationResponse(
          token: "mock_apple_token_\(Date().timeIntervalSince1970)",
          twoFactorRequired: false,
          user: user
        )
      },
      logout: {
        try await Task.sleep(for: .milliseconds(100))
      }
    )
  }
  
  /// 创建总是失败的Mock AuthenticationClient
  public static func createFailureClient(
    error: Error = AuthenticationError.appleSignInFailed
  ) -> AuthenticationClient {
    return AuthenticationClient(
      login: { _, _ in
        throw AuthenticationError.invalidCredentials
      },
      twoFactor: { _, _ in
        throw AuthenticationError.invalidCredentials
      },
      signInWithApple: { _ in
        try await Task.sleep(for: .milliseconds(200))
        throw error
      },
      logout: {
        try await Task.sleep(for: .milliseconds(100))
      }
    )
  }
  
  /// 创建模拟网络问题的Mock AuthenticationClient
  public static func createNetworkErrorClient() -> AuthenticationClient {
    let networkError = NSError(
      domain: NSURLErrorDomain,
      code: NSURLErrorNotConnectedToInternet,
      userInfo: [
        NSLocalizedDescriptionKey: "网络连接不可用，请检查您的网络设置"
      ]
    )
    
    return createFailureClient(error: networkError)
  }
  
  /// 创建模拟用户取消的Mock AuthenticationClient
  public static func createUserCancelledClient() -> AuthenticationClient {
    #if canImport(AuthenticationServices)
    let cancelError = NSError(
      domain: ASAuthorizationErrorDomain,
      code: ASAuthorizationError.canceled.rawValue,
      userInfo: [
        NSLocalizedDescriptionKey: "用户取消了Apple ID登录"
      ]
    )
    #else
    let cancelError = NSError(
      domain: "com.apple.AuthenticationServices.AuthorizationError",
      code: 1001,
      userInfo: [
        NSLocalizedDescriptionKey: "用户取消了Apple ID登录"
      ]
    )
    #endif
    
    return createFailureClient(error: cancelError)
  }
  
  // MARK: - 测试场景生成器
  
  /// 生成各种测试场景
  public static func createTestScenarios() -> [(String, AuthenticationClient, AppleIDCredential)] {
    return [
      (
        "成功登录 - 完整信息",
        createSuccessClient(),
        createSuccessCredential()
      ),
      (
        "成功登录 - 隐私保护",
        createSuccessClient(),
        createPrivacyCredential()
      ),
      (
        "登录失败 - 网络错误",
        createNetworkErrorClient(),
        createSuccessCredential()
      ),
      (
        "登录失败 - 用户取消",
        createUserCancelledClient(),
        createSuccessCredential()
      ),
      (
        "登录失败 - 服务错误",
        createFailureClient(),
        createSuccessCredential()
      )
    ]
  }
  
  // MARK: - 测试验证工具
  
  /// 验证Apple ID凭证的有效性
  public static func validateCredential(_ credential: AppleIDCredential) -> Bool {
    // 基本验证
    guard !credential.userID.isEmpty else {
      print("❌ 凭证验证失败: 用户ID为空")
      return false
    }
    
    // 检查是否至少有邮箱或姓名信息
    let hasEmail = credential.email != nil && !credential.email!.isEmpty
    let hasName = credential.fullName != nil
    
    if !hasEmail && !hasName {
      print("⚠️ 凭证警告: 既没有邮箱也没有姓名信息（可能是隐私保护）")
    }
    
    print("✅ 凭证验证通过:")
    print("   用户ID: \(credential.userID)")
    print("   邮箱: \(credential.email ?? "未提供")")
    print("   姓名: \(credential.fullName?.formatted() ?? "未提供")")
    print("   身份令牌: \(credential.identityToken != nil ? "有" : "无")")
    print("   授权码: \(credential.authorizationCode != nil ? "有" : "无")")
    
    return true
  }
  
  /// 验证认证响应的有效性
  public static func validateAuthResponse(_ response: AuthenticationResponse) -> Bool {
    guard !response.token.isEmpty else {
      print("❌ 响应验证失败: 令牌为空")
      return false
    }
    
    guard let user = response.user else {
      print("❌ 响应验证失败: 用户信息为空")
      return false
    }
    
    guard !user.id.isEmpty else {
      print("❌ 响应验证失败: 用户ID为空")
      return false
    }
    
    print("✅ 认证响应验证通过:")
    print("   令牌: \(response.token.prefix(10))...")
    print("   用户ID: \(user.id)")
    print("   邮箱: \(user.email)")
    print("   显示名: \(user.displayName)")
    print("   认证方式: \(user.authProvider.rawValue)")
    
    return true
  }
}

// MARK: - 测试运行器

public struct AppleSignInTestRunner {
  
  /// 运行所有Apple ID登录测试场景
  public static func runAllScenarios() async {
    print("🚀 开始Apple ID登录测试")
    print("=" * 50)
    
    let scenarios = MockAppleSignInHelper.createTestScenarios()
    
    for (index, (name, client, credential)) in scenarios.enumerated() {
      print("\n🧪 场景 \(index + 1): \(name)")
      print("-" * 30)
      
      do {
        let response = try await client.signInWithApple(credential)
        print("✅ 登录成功")
        _ = MockAppleSignInHelper.validateAuthResponse(response)
      } catch {
        print("❌ 登录失败: \(error.localizedDescription)")
      }
    }
    
    print("\n🏁 测试完成")
    print("=" * 50)
  }
  
  /// 运行单个测试场景
  public static func runScenario(
    name: String,
    client: AuthenticationClient,
    credential: AppleIDCredential
  ) async {
    print("🧪 测试场景: \(name)")
    
    guard MockAppleSignInHelper.validateCredential(credential) else {
      print("❌ 凭证验证失败，跳过测试")
      return
    }
    
    do {
      let response = try await client.signInWithApple(credential)
      print("✅ 登录成功")
      _ = MockAppleSignInHelper.validateAuthResponse(response)
    } catch {
      print("❌ 登录失败: \(error.localizedDescription)")
    }
  }
}

// MARK: - 字符串扩展

private extension String {
  static func * (left: String, right: Int) -> String {
    return String(repeating: left, count: right)
  }
}
