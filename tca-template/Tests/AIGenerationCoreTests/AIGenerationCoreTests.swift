import ComposableArchitecture
import AIGenerationCore
import XCTest

@MainActor
final class AIGenerationCoreTests: XCTestCase {
  
  func testStartGeneration() async {
    let mockRequest = GenerationRequest(
      photos: [PhotoData(imageData: Data(), originalSize: CGSize(width: 100, height: 100))],
      style: StyleData(id: UUID(), name: "Test Style", prompt: "test prompt")
    )
    
    let store = TestStore(initialState: AIGeneration.State()) {
      AIGeneration()
    } withDependencies: {
      $0.aiGenerationClient.startGeneration = { _ in "test-id" }
      $0.aiGenerationClient.monitorProgress = { _ in
        AsyncStream { continuation in
          continuation.yield(ProgressUpdate(progress: 1.0, status: .completed, estimatedTimeRemaining: 0))
          continuation.finish()
        }
      }
      $0.aiGenerationClient.getResults = { _ in [] }
    }
    
    await store.send(.startGeneration(mockRequest)) {
      $0.generationRequest = mockRequest
      $0.generationStatus = .preparing
      $0.progress = 0.0
      $0.error = nil
      $0.generatedImages = []
    }
    
    await store.receive(.statusChanged(.preparing)) {
      $0.generationStatus = .preparing
      $0.isRetrying = false
    }
    
    await store.receive(.progressUpdated(1.0, 0)) {
      $0.progress = 1.0
      $0.estimatedTimeRemaining = 0
    }
    
    await store.receive(.statusChanged(.completed)) {
      $0.generationStatus = .completed
      $0.isRetrying = false
    }
    
    await store.receive(.generationCompleted([])) {
      $0.generatedImages = []
      $0.generationStatus = .completed
      $0.progress = 1.0
    }
  }
  
  func testCancelGeneration() async {
    let store = TestStore(
      initialState: AIGeneration.State(generationStatus: .processing)
    ) {
      AIGeneration()
    } withDependencies: {
      $0.aiGenerationClient.cancelGeneration = {}
    }
    
    await store.send(.cancelGeneration) {
      $0.generationStatus = .cancelled
    }
  }
  
  func testRetryGeneration() async {
    let mockRequest = GenerationRequest(
      photos: [PhotoData(imageData: Data(), originalSize: CGSize(width: 100, height: 100))],
      style: StyleData(id: UUID(), name: "Test Style", prompt: "test prompt")
    )
    
    let store = TestStore(
      initialState: AIGeneration.State(
        generationRequest: mockRequest,
        generationStatus: .failed
      )
    ) {
      AIGeneration()
    } withDependencies: {
      $0.aiGenerationClient.startGeneration = { _ in "test-id" }
      $0.aiGenerationClient.monitorProgress = { _ in
        AsyncStream { continuation in
          continuation.finish()
        }
      }
    }
    
    await store.send(.retryGeneration) {
      $0.isRetrying = true
    }
    
    await store.receive(.startGeneration(mockRequest)) {
      $0.generationRequest = mockRequest
      $0.generationStatus = .preparing
      $0.progress = 0.0
      $0.error = nil
      $0.generatedImages = []
    }
    
    await store.receive(.statusChanged(.preparing)) {
      $0.generationStatus = .preparing
      $0.isRetrying = false
    }
  }
  
  func testProgressUpdate() async {
    let store = TestStore(initialState: AIGeneration.State()) {
      AIGeneration()
    }
    
    await store.send(.progressUpdated(0.5, 30)) {
      $0.progress = 0.5
      $0.estimatedTimeRemaining = 30
    }
  }
  
  func testStatusChange() async {
    let store = TestStore(initialState: AIGeneration.State()) {
      AIGeneration()
    }
    
    await store.send(.statusChanged(.processing)) {
      $0.generationStatus = .processing
      $0.isRetrying = false
    }
  }
  
  func testGenerationFailed() async {
    let store = TestStore(initialState: AIGeneration.State()) {
      AIGeneration()
    }
    
    await store.send(.generationFailed(.networkError("Connection failed"))) {
      $0.error = .networkError("Connection failed")
      $0.generationStatus = .failed
      $0.isRetrying = false
    }
  }
  
  func testSaveImage() async {
    let mockImage = GeneratedImage(
      imageData: Data(),
      thumbnailData: Data(),
      size: CGSize(width: 512, height: 768),
      style: StyleData(id: UUID(), name: "Test", prompt: "test"),
      quality: .standard
    )
    
    let store = TestStore(initialState: AIGeneration.State()) {
      AIGeneration()
    } withDependencies: {
      $0.aiGenerationClient.saveToPhotoLibrary = { _ in }
    }
    
    await store.send(.saveImage(mockImage))
  }
  
  func testDismissError() async {
    let store = TestStore(
      initialState: AIGeneration.State(error: .networkError("Test error"))
    ) {
      AIGeneration()
    }
    
    await store.send(.dismissError) {
      $0.error = nil
    }
  }
  
  func testStateProperties() {
    // Test isGenerating
    let idleState = AIGeneration.State(generationStatus: .idle)
    XCTAssertFalse(idleState.isGenerating)
    
    let processingState = AIGeneration.State(generationStatus: .processing)
    XCTAssertTrue(processingState.isGenerating)
    
    let completedState = AIGeneration.State(generationStatus: .completed)
    XCTAssertFalse(completedState.isGenerating)
    
    // Test canRetry
    let failedState = AIGeneration.State(generationStatus: .failed)
    XCTAssertTrue(failedState.canRetry)
    
    XCTAssertFalse(idleState.canRetry)
    
    // Test hasResults
    let stateWithResults = AIGeneration.State(
      generatedImages: [
        GeneratedImage(
          imageData: Data(),
          thumbnailData: Data(),
          size: CGSize(width: 512, height: 768),
          style: StyleData(id: UUID(), name: "Test", prompt: "test"),
          quality: .standard
        )
      ]
    )
    XCTAssertTrue(stateWithResults.hasResults)
    
    let stateWithoutResults = AIGeneration.State()
    XCTAssertFalse(stateWithoutResults.hasResults)
  }
}
