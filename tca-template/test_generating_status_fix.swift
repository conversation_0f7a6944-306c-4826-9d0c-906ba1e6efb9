#!/usr/bin/env swift

import Foundation

// 模拟GENERATING状态的服务器响应
let generatingResponse = """
{
    "code": 200,
    "msg": "success",
    "data": {
        "taskId": "92e524b7d976604631efffd0f9ba1fc7",
        "paramJson": "{\\"callBackUrl\\":\\"http://c89de6f7.natappfree.cc/api/v1/image/callback\\",\\"enableFallback\\":false,\\"fallbackModel\\":\\"FLUX_MAX\\",\\"fileUrlOrPromptNotEmpty\\":true,\\"filesUrl\\":[\\"https://img-bridal.wenhaofree.com/uploads/image_1_8C839A46-3123-4B32-B4E3-0AA50A091AA8.jpeg\\"],\\"isEnhance\\":false,\\"nVariants\\":1,\\"nVariantsValid\\":true,\\"prompt\\":\\"Keep the same face from reference image, preserve facial identity, outdoor wedding photography, natural landscape, beautiful scenery, fresh air atmosphere, natural lighting, scenic background, countryside setting, garden wedding, outdoor ceremony, nature-inspired, rustic charm, outdoor romance, natural beauty, landscape wedding\\",\\"size\\":\\"1:1\\",\\"uploadCn\\":false}",
        "completeTime": null,
        "response": null,
        "successFlag": 0,
        "status": "GENERATING",
        "errorCode": null,
        "errorMessage": null,
        "createTime": 1754019109000,
        "progress": "0.04"
    }
}
"""

// 模拟SUCCESS状态的服务器响应
let successResponse = """
{
    "code": 200,
    "msg": "success",
    "data": {
        "taskId": "92e524b7d976604631efffd0f9ba1fc7",
        "paramJson": "...",
        "completeTime": 1754019200000,
        "response": {
            "resultUrls": [
                "https://tempfile.aiquickdraw.com/s/92e524b7d976604631efffd0f9ba1fc7_0_1754019199_1234.png"
            ]
        },
        "successFlag": 1,
        "status": "SUCCESS",
        "errorCode": null,
        "errorMessage": null,
        "createTime": 1754019109000,
        "progress": "1.00"
    }
}
"""

// 模拟数据结构
struct RecordInfoResponse: Codable {
    let code: Int
    let msg: String
    let data: GenerationRecordData?

    var success: Bool {
        return code == 200
    }

    var message: String {
        return msg
    }

    var isSuccess: Bool {
        return success
    }

    var error: String? {
        return code != 200 ? msg : nil
    }
}

struct GenerationRecordData: Codable {
    let taskId: String
    let paramJson: String
    let completeTime: Int64?
    let response: GenerationResponse?  // 修改为可选
    let successFlag: Int
    let status: String
    let errorCode: Int?
    let errorMessage: String?
    let createTime: Int64
    let progress: String
    
    // 便利属性
    var isSuccess: Bool {
        return status == "SUCCESS"
    }
    
    var progressValue: Double {
        return Double(progress) ?? 0.0
    }
    
    var imageUrls: [String] {
        return response?.resultUrls ?? []  // 修改为可选链
    }
}

struct GenerationResponse: Codable {
    let resultUrls: [String]?
}

// 测试GENERATING状态解析
func testGeneratingStatusParsing() {
    print("🧪 测试GENERATING状态解析")
    print(String(repeating: "=", count: 50))
    
    // 1. 解析GENERATING状态响应
    print("1️⃣ 解析GENERATING状态响应...")
    
    guard let responseData = generatingResponse.data(using: .utf8) else {
        print("❌ 无法转换响应数据")
        return
    }
    
    do {
        let decoder = JSONDecoder()
        let recordInfoResponse = try decoder.decode(RecordInfoResponse.self, from: responseData)
        
        print("✅ RecordInfoResponse 解析成功:")
        print("   Code: \(recordInfoResponse.code)")
        print("   Message: \(recordInfoResponse.msg)")
        print("   Success: \(recordInfoResponse.success)")
        print()
        
        // 2. 检查生成记录数据
        if let generationData = recordInfoResponse.data {
            print("2️⃣ 解析生成记录数据...")
            print("✅ GenerationRecordData 解析成功:")
            print("   Task ID: \(generationData.taskId)")
            print("   Status: \(generationData.status)")
            print("   Success Flag: \(generationData.successFlag)")
            print("   Progress: \(generationData.progress)")
            print("   Progress Value: \(generationData.progressValue)")
            print("   Is Success: \(generationData.isSuccess)")
            print("   Response: \(generationData.response != nil ? "有数据" : "null")")
            print("   Image URLs Count: \(generationData.imageUrls.count)")
            print()
            
            // 3. 验证状态处理
            print("3️⃣ 验证状态处理...")
            
            switch generationData.status {
            case "GENERATING":
                print("✅ 状态: 正在生成中")
                print("✅ 进度: \(Int(generationData.progressValue * 100))%")
                print("✅ 应该显示: 生成进度界面")
                print("✅ 应该继续轮询状态")
                
            case "SUCCESS":
                print("✅ 状态: 生成成功")
                print("✅ 图片数量: \(generationData.imageUrls.count)")
                print("✅ 应该显示: 生成结果界面")
                
            default:
                print("⚠️ 未知状态: \(generationData.status)")
            }
            print()
            
        } else {
            print("❌ 没有生成记录数据")
        }
        
    } catch {
        print("❌ 解析失败: \(error.localizedDescription)")
        return
    }
    
    print("🎉 GENERATING状态解析验证完成！")
}

// 测试SUCCESS状态解析
func testSuccessStatusParsing() {
    print("🧪 测试SUCCESS状态解析")
    print(String(repeating: "=", count: 50))
    
    guard let responseData = successResponse.data(using: .utf8) else {
        print("❌ 无法转换响应数据")
        return
    }
    
    do {
        let decoder = JSONDecoder()
        let recordInfoResponse = try decoder.decode(RecordInfoResponse.self, from: responseData)
        
        if let generationData = recordInfoResponse.data {
            print("✅ SUCCESS状态解析成功:")
            print("   Status: \(generationData.status)")
            print("   Progress: \(generationData.progress)")
            print("   Response: \(generationData.response != nil ? "有数据" : "null")")
            print("   Image URLs Count: \(generationData.imageUrls.count)")
            
            if !generationData.imageUrls.isEmpty {
                print("   First Image URL: \(generationData.imageUrls[0])")
            }
            print()
        }
        
    } catch {
        print("❌ SUCCESS状态解析失败: \(error.localizedDescription)")
    }
}

// 对比修复前后
func showFixComparison() {
    print("📝 修复前后对比")
    print(String(repeating: "=", count: 50))
    
    print("🔴 修复前的问题:")
    print("""
    struct GenerationRecordData: Codable {
      let response: GenerationResponse  // ❌ 非可选类型
    }
    
    问题：
    ❌ GENERATING状态时，response字段为null
    ❌ JSON解析失败: "The data couldn't be read because it is missing."
    ❌ 页面显示服务器错误，而不是生成进度
    """)
    print()
    
    print("🟢 修复后的解决方案:")
    print("""
    struct GenerationRecordData: Codable {
      let response: GenerationResponse?  // ✅ 可选类型
    }
    
    var imageUrls: [String] {
      return response?.resultUrls ?? []  // ✅ 安全的可选链访问
    }
    
    优势：
    ✅ 正确处理GENERATING状态的null response
    ✅ JSON解析成功
    ✅ 页面正确显示生成进度
    ✅ 支持状态轮询直到完成
    """)
    print()
}

// 运行测试
print("🧪 图片生成GENERATING状态处理修复验证")
print(String(repeating: "=", count: 60))
print()

testGeneratingStatusParsing()
print()
testSuccessStatusParsing()
print()
showFixComparison()
