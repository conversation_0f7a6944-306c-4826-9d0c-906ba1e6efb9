import AppCore
import AppSwiftUI
import AuthenticationClientLive
import ComposableArchitecture
import SwiftUI
import LaunchCore
import LoginCore

struct RootView: View {
  // Development configuration: set to true to start with login, false for launch screen
  private static let skipLaunchScreen = false

  // Choose initial state based on configuration
  let store: StoreOf<AppFeature> = Store(
    initialState: Self.skipLaunchScreen
      ? AppFeature.State.login(Login.State())
      : AppFeature.State.launch(Launch.State())
  ) {
    AppFeature.body._printChanges()
  } withDependencies: {
    // Configure live dependencies
    $0.authenticationClient = .liveValue
    // NetworkClient will use its default liveValue
  }

  var body: some View {
    AppView(store: store)
      .onAppear {
        print("🚀 App launched with initial state: \(Self.skipLaunchScreen ? "Login Flow" : "Launch Screen")")
      }
  }
}

#Preview {
  RootView()
}
